async function calculateSleepQuality() {
    try {
        const { currentFile } = this;
        const content = await app.vault.read(currentFile);

        // 增强版时间解析（兼容更多格式，包括引号）
        const extractTime = (text, type) => {
            const match = text.match(new RegExp(`${type}[\\s:：]*["']?(\\d{1,2})[：:]?(\\d{2})["']?`));
            return match ? match[1] + ':' + match[2] : null;
        };

        const wakeTime = extractTime(content, '起床');
        const sleepTime = extractTime(content, '睡觉');
        if (!wakeTime || !sleepTime) return "⏳ 需要「起床: 时间」和「睡觉: 时间」";

        // 计算睡眠时长
        const [wakeH, wakeM] = wakeTime.split(':').map(Number);
        const [sleepH, sleepM] = sleepTime.split(':').map(Number);
        let hours = ((wakeH - sleepH) * 60 + (wakeM - sleepM)) / 60;
        if (sleepH > wakeH) hours += 24;
        hours = parseFloat(hours.toFixed(1));

        // 简化作息评价
        let routineEval, routineTip = "";

        // 完美作息：22:00-00:00睡觉 + 05:00-08:00起床
        if ((sleepH >= 22 && sleepH <= 23) || (sleepH === 0 && sleepM === 0)) {
            if (wakeH >= 5 && (wakeH < 8 || (wakeH === 8 && wakeM === 0))) {
                routineEval = "🌞 作息完美";
            } else {
                routineEval = "🌗 作息尚可";
                if (wakeH >= 8) routineTip = " 可以尝试再早起些";
            }
        } 
        // 0:00-1:00睡觉
        else if (sleepH === 0) {
            if (wakeH <= 9) {
                routineEval = "🌗 作息尚可";
                routineTip = " 可以尝试再早睡些";
            } else {
                routineEval = "🌚 作息混乱";
                routineTip = " 起床时间太晚";
            }
        } 
        // 其他情况
        else {
            routineEval = "🌚 作息混乱";
            if (sleepH > 0) {
                routineTip = " 太晚睡了，注意调整";
            } else if (wakeH > 8) {
                routineTip = " 起得太晚了";
            }
        }

        // 睡眠评价
        let durationEval = "";
        if (hours < 5) durationEval = "⚠️ 睡眠严重不足";
        else if (hours < 6) durationEval = "😴 睡眠不足";
        else if (hours < 7) durationEval = "😌 睡眠基本足够";
        else if (hours < 9) durationEval = "😊 睡眠理想时长";
        else durationEval = "🛌 睡眠时间过长";

        return `${routineEval}${routineTip} | ${durationEval} (${hours}小时)`;

    } catch (error) {
        console.error("睡眠分析错误:", error);
        return "⚠️ 计算失败";
    }
}

exports.default = {
    name: "calculateSleepQuality",
    description: `睡眠质量分析脚本
    
    输入要求：
      起床: "07:30"
      睡觉: "23:45"
      
    评价标准：
      作息完美：22点-0点睡 + 5点-8点起
      作息尚可：0点-1点睡 或 8点-9点起
      作息混乱：超过1点睡 或 超过9点起
      睡眠充足：7-9小时`,
    entry: calculateSleepQuality
};
