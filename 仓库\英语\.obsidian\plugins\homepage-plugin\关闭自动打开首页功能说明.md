# 首页插件 - 关闭自动打开首页功能

## 问题描述

如果您发现每次打开 Obsidian 或笔记时都会自动打开首页，这可能会影响您的使用体验。

## 解决方案

### 方法一：通过设置界面关闭（推荐）

1. **打开插件设置**
   - 进入 Obsidian 设置（Ctrl/Cmd + ,）
   - 选择"插件选项"
   - 找到"Homepage Plugin"并点击

2. **关闭自动打开功能**
   - 找到"启动时打开首页"选项
   - 将开关设置为**关闭状态**
   - 设置会自动保存

3. **验证设置**
   - 重启 Obsidian
   - 确认不再自动打开首页

### 方法二：了解默认设置变更

我们已经将插件的默认设置进行了优化：

- **新用户**：默认不会自动打开首页
- **现有用户**：需要手动关闭该功能（使用方法一）

## 功能说明

### 启动时打开首页
- **开启时**：每次启动 Obsidian 都会自动打开设置的首页文件
- **关闭时**：只有手动使用"打开首页"命令时才会打开首页

### 其他相关功能
- **打开首页命令**：仍然可以通过命令面板或快捷键手动打开首页
- **固定位置功能**：不受此设置影响，仍然正常工作
- **设置当前页为首页**：不受此设置影响，仍然正常工作

## 使用建议

### 适合开启自动打开的场景
- 您有一个固定的工作流程，总是从特定页面开始
- 您的首页包含重要的导航链接或待办事项
- 您希望每次打开 Obsidian 都能看到概览信息

### 适合关闭自动打开的场景
- 您希望 Obsidian 恢复到上次关闭时的状态
- 您不希望被打断当前的工作流程
- 您只是偶尔需要查看首页

## 常见问题

### Q: 关闭后如何手动打开首页？
A: 可以通过以下方式：
- 使用命令面板（Ctrl/Cmd + P）搜索"打开首页"
- 如果设置了快捷键，直接使用快捷键
- 在插件设置中点击"设置当前页为首页"按钮

### Q: 设置没有生效怎么办？
A: 请尝试：
1. 确认设置已保存（开关状态正确）
2. 重启 Obsidian 应用
3. 检查是否有其他插件冲突

### Q: 可以为不同的工作区设置不同的行为吗？
A: 目前插件的设置是全局的，但您可以：
- 在不同工作区使用不同的首页文件
- 根据需要手动开启/关闭自动打开功能

## 更新日志

### v1.2.0
- 将"启动时打开首页"的默认值改为关闭
- 优化设置界面的描述文字
- 提供更清晰的用户指导

---

如果您在使用过程中遇到任何问题，请通过 GitHub 或插件社区反馈。