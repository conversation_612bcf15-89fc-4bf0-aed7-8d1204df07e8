async function generateSpecificScenario(token, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型

  if (!token) {
    new Notice("请设置API密钥");
    return;
  }

  // 显示选择弹窗
  const selections = await showScenarioSelectionModal();
  if (!selections) {
    new Notice("已取消生成场景");
    return;
  }

  new Notice("AI正在生成具体练习场景...");

  // AI提示语：根据用户选择生成具体的练习题目
  const prompt = `
作为一个专业的英语口语训练专家，请根据用户的选择生成一个简洁的中文口语练习场景。

用户选择：
- 对话角色: ${selections.role}
- 场景类型: ${selections.scenario}
- 事件背景: ${selections.event}

请生成一个具体的练习题目，要求：

1. **场景描述**：用中文简洁描述具体情况（100字以内）
2. **你的角色**：数据合规顾问
3. **对话目标**：3个具体目标（中文）
4. **必用英文词汇**：8-10个专业英文术语
5. **关键句式**：3-5个英文表达

注意：
- 场景描述用中文，简洁明了
- 专业术语必须是英文
- 关键句式必须是英文
- 整体内容要简洁，不要冗长

请以JSON格式返回结果：
{
  "scenario_title": "具体场景标题（中文）",
  "background": "简洁的背景描述（中文，100字以内）",
  "dialogue_objectives": [
    "目标1（中文）",
    "目标2（中文）",
    "目标3（中文）"
  ],
  "required_vocabulary": [
    "data breach",
    "compliance",
    "privacy policy"
  ],
  "key_expressions": [
    "We need to address this compliance issue immediately.",
    "Let me explain the data protection requirements."
  ],
  "time_limit": "15-20分钟"
}

只返回JSON格式的结果，不要包含其他说明文字。
`;

  // 调用AI API
  const options = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    }),
  };

  try {
    const response = await obsidian.requestUrl(options);
    const result = response.json;
    
    if (result.choices.length === 0) {
      new Notice("AI生成失败，没有返回内容");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI生成失败，返回内容为空");
      return;
    }

    // 解析AI返回的JSON
    let scenarioData;
    try {
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      scenarioData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI返回格式错误，无法解析JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    // 生成具体练习题目内容
    const currentTime = new Date().toLocaleString('zh-CN');
    const practiceContent = `---
对话角色: "${selections.role}"
场景类型: "${selections.scenario}"
事件背景: "${selections.event}"
生成时间: "${new Date().toISOString().split('T')[0]}"
练习类型: "具体场景练习"
预计时长: "${scenarioData.time_limit || '15-20分钟'}"
---

# ${scenarioData.scenario_title || '具体练习场景'}

## 📋 场景描述
${scenarioData.background || '具体场景背景'}

## 🎯 对话目标
${scenarioData.dialogue_objectives ? scenarioData.dialogue_objectives.map((obj, index) => `${index + 1}. ${obj}`).join('\n') : '完成专业对话'}

## 📚 必用英文词汇
${scenarioData.required_vocabulary ? scenarioData.required_vocabulary.map((vocab, index) => `${index + 1}. ${vocab}`).join('\n') : '使用专业术语'}

## 🗣️ 关键英文表达
${scenarioData.key_expressions ? scenarioData.key_expressions.map((expr, index) => `${index + 1}. "${expr}"`).join('\n') : '使用专业表达'}

---
*练习时长: ${scenarioData.time_limit || '15-20分钟'}*
`;

    // 创建具体练习文件
    const now = new Date();
    const dateStamp = now.toISOString().split('T')[0].replace(/-/g, '');
    const timeStamp = now.toTimeString().slice(0, 5).replace(':', '');

    // 使用AI生成的场景标题作为文件名
    const scenarioTitle = scenarioData.scenario_title || `${selections.role}${selections.scenario}练习`;
    const safeTitle = scenarioTitle
      .replace(/[<>:"/\\|?*\n\r\t]/g, '')
      .replace(/\s+/g, '')
      .substring(0, 50); // 限制长度避免文件名过长

    const practiceFileName = `${safeTitle}.md`;
    const practiceFolder = "英语/口语训练/具体练习";
    
    // 创建具体练习文件夹（如果不存在）
    try {
      await app.vault.createFolder(practiceFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    const practiceFilePath = `${practiceFolder}/${practiceFileName}`;
    
    // 避免文件名冲突
    let finalPath = practiceFilePath;
    let counter = 1;
    while (await app.vault.adapter.exists(finalPath)) {
      const nameWithoutExt = practiceFileName.replace('.md', '');
      finalPath = `${practiceFolder}/${nameWithoutExt}_${counter}.md`;
      counter++;
    }
    
    await app.vault.create(finalPath, practiceContent);
    
    new Notice(`✅ 具体练习场景已生成！`);
    
    // 可选：打开生成的文件
    const createdFile = app.vault.getAbstractFileByPath(finalPath);
    if (createdFile) {
      await app.workspace.getLeaf().openFile(createdFile);
    }
    
  } catch (error) {
    new Notice("生成过程中发生错误，请查看控制台");
    console.error("具体场景生成错误:", error);
  }
}

// 角色对应的场景和事件配置
const roleScenarioConfig = {
  "CEO/高管": {
    scenarios: [
      { value: "战略咨询", text: "💼 战略咨询 - 合规战略制定" },
      { value: "危机管理", text: "🚨 危机管理 - 高层决策" },
      { value: "投资决策", text: "💰 投资决策 - 风险评估" }
    ],
    events: [
      { value: "重大数据泄露", text: "🔓 重大数据泄露 - 公司声誉危机" },
      { value: "监管重罚", text: "⚖️ 监管重罚 - 合规失败后果" },
      { value: "并购尽调", text: "🤝 并购尽调 - 数据合规评估" }
    ]
  },
  "IT技术人员": {
    scenarios: [
      { value: "技术培训", text: "💻 技术培训 - 系统安全实施" },
      { value: "系统评估", text: "🔍 系统评估 - 技术合规检查" },
      { value: "架构设计", text: "🏗️ 架构设计 - 隐私保护设计" }
    ],
    events: [
      { value: "新系统上线", text: "🚀 新系统上线 - 合规技术要求" },
      { value: "安全漏洞", text: "🛡️ 安全漏洞 - 技术修复方案" },
      { value: "数据迁移", text: "📦 数据迁移 - 安全传输要求" }
    ]
  },
  "销售人员": {
    scenarios: [
      { value: "客户沟通", text: "💬 客户沟通 - 隐私承诺说明" },
      { value: "合同谈判", text: "📄 合同谈判 - 数据条款协商" },
      { value: "产品介绍", text: "📋 产品介绍 - 隐私功能展示" }
    ],
    events: [
      { value: "客户质疑", text: "❓ 客户质疑 - 数据安全担忧" },
      { value: "竞标项目", text: "🏆 竞标项目 - 合规优势展示" },
      { value: "续约谈判", text: "🔄 续约谈判 - 新合规要求" }
    ]
  },
  "HR人事": {
    scenarios: [
      { value: "员工培训", text: "👨‍🏫 员工培训 - 数据保护意识" },
      { value: "政策制定", text: "📋 政策制定 - 内部制度建设" },
      { value: "违规处理", text: "⚠️ 违规处理 - 员工违规应对" }
    ],
    events: [
      { value: "员工离职", text: "👋 员工离职 - 数据访问回收" },
      { value: "内部举报", text: "📢 内部举报 - 违规行为调查" },
      { value: "新员工入职", text: "🆕 新员工入职 - 合规培训" }
    ]
  },
  "法务顾问": {
    scenarios: [
      { value: "法律咨询", text: "⚖️ 法律咨询 - 专业法律建议" },
      { value: "合规审查", text: "🔍 合规审查 - 法律风险评估" },
      { value: "诉讼应对", text: "🏛️ 诉讼应对 - 法律纠纷处理" }
    ],
    events: [
      { value: "法规更新", text: "📜 法规更新 - 新法律要求" },
      { value: "用户起诉", text: "⚖️ 用户起诉 - 隐私侵权诉讼" },
      { value: "监管调查", text: "🔍 监管调查 - 执法部门询问" }
    ]
  },
  "客户/用户": {
    scenarios: [
      { value: "权利行使", text: "🙋 权利行使 - 数据主体权利" },
      { value: "投诉处理", text: "📞 投诉处理 - 隐私问题反馈" },
      { value: "咨询服务", text: "💬 咨询服务 - 隐私政策解释" }
    ],
    events: [
      { value: "数据删除请求", text: "🗑️ 数据删除请求 - 被遗忘权" },
      { value: "隐私投诉", text: "📢 隐私投诉 - 数据滥用举报" },
      { value: "访问请求", text: "👁️ 访问请求 - 数据查看权利" }
    ]
  },
  "监管机构": {
    scenarios: [
      { value: "监管沟通", text: "🏛️ 监管沟通 - 官方执法对接" },
      { value: "合规检查", text: "🔍 合规检查 - 监管审查应对" },
      { value: "处罚程序", text: "⚖️ 处罚程序 - 违规后果处理" }
    ],
    events: [
      { value: "突击检查", text: "🚨 突击检查 - 现场执法检查" },
      { value: "违规通知", text: "📋 违规通知 - 合规缺陷整改" },
      { value: "听证程序", text: "👂 听证程序 - 申辩机会提供" }
    ]
  },
  "供应商/合作伙伴": {
    scenarios: [
      { value: "商务谈判", text: "🤝 商务谈判 - 合作协议磋商" },
      { value: "合规对接", text: "🔗 合规对接 - 标准统一协调" },
      { value: "风险评估", text: "📊 风险评估 - 第三方风险管控" }
    ],
    events: [
      { value: "合同续签", text: "📄 合同续签 - 新合规条款" },
      { value: "数据共享", text: "🔄 数据共享 - 跨组织传输" },
      { value: "供应商审计", text: "🔍 供应商审计 - 第三方合规检查" }
    ]
  }
};

// 显示场景选择弹窗
async function showScenarioSelectionModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("🎯 智能场景生成器");

    const container = modal.contentEl.createDiv();
    container.style.cssText = "padding: 25px; width: 650px; max-height: 80vh; overflow-y: auto;";

    // 对话角色选择
    const roleLabel = container.createEl("h3", { text: "👥 选择对话角色" });
    roleLabel.style.cssText = "margin: 0 0 10px 0; font-size: 16px; color: #333;";

    const roleSelect = container.createEl("select");
    roleSelect.style.cssText = "width: 100%; padding: 12px; margin: 0 0 25px 0; border: 1px solid #ddd; border-radius: 6px; font-size: 15px; background: white; height: 45px;";

    // 添加默认选项
    const defaultOption = roleSelect.createEl("option");
    defaultOption.value = "";
    defaultOption.text = "请选择对话角色...";
    defaultOption.disabled = true;
    defaultOption.selected = true;

    const roles = [
      { value: "CEO/高管", text: "🏢 CEO/高管 - 战略决策者" },
      { value: "IT技术人员", text: "💻 IT技术人员 - 系统开发维护" },
      { value: "销售人员", text: "💼 销售人员 - 客户关系管理" },
      { value: "HR人事", text: "👥 HR人事 - 员工数据管理" },
      { value: "法务顾问", text: "⚖️ 法务顾问 - 法律合规专家" },
      { value: "客户/用户", text: "🙋 客户/用户 - 数据主体" },
      { value: "监管机构", text: "🏛️ 监管机构 - 执法部门" },
      { value: "供应商/合作伙伴", text: "🤝 供应商/合作伙伴 - 第三方" }
    ];

    roles.forEach(role => {
      const option = roleSelect.createEl("option");
      option.value = role.value;
      option.text = role.text;
    });

    // 场景类型选择
    const scenarioLabel = container.createEl("h3", { text: "📋 选择场景类型" });
    scenarioLabel.style.cssText = "margin: 0 0 10px 0; font-size: 16px; color: #333;";

    const scenarioSelect = container.createEl("select");
    scenarioSelect.style.cssText = "width: 100%; padding: 12px; margin: 0 0 25px 0; border: 1px solid #ddd; border-radius: 6px; font-size: 15px; background: white; height: 45px;";
    scenarioSelect.disabled = true;

    const scenarioDefaultOption = scenarioSelect.createEl("option");
    scenarioDefaultOption.text = "请先选择角色...";

    // 事件背景选择
    const eventLabel = container.createEl("h3", { text: "🎭 选择事件背景" });
    eventLabel.style.cssText = "margin: 0 0 10px 0; font-size: 16px; color: #333;";

    const eventSelect = container.createEl("select");
    eventSelect.style.cssText = "width: 100%; padding: 12px; margin: 0 0 25px 0; border: 1px solid #ddd; border-radius: 6px; font-size: 15px; background: white; height: 45px;";
    eventSelect.disabled = true;

    const eventDefaultOption = eventSelect.createEl("option");
    eventDefaultOption.text = "请先选择角色...";

    // 角色选择变化事件
    roleSelect.addEventListener('change', () => {
      const selectedRole = roleSelect.value;

      // 清空并重新填充场景选择
      scenarioSelect.innerHTML = '';
      eventSelect.innerHTML = '';

      if (selectedRole && roleScenarioConfig[selectedRole]) {
        scenarioSelect.disabled = false;
        eventSelect.disabled = false;

        // 添加场景选项
        const scenarioDefault = scenarioSelect.createEl("option");
        scenarioDefault.value = "";
        scenarioDefault.text = "请选择场景类型...";
        scenarioDefault.disabled = true;
        scenarioDefault.selected = true;

        roleScenarioConfig[selectedRole].scenarios.forEach(scenario => {
          const option = scenarioSelect.createEl("option");
          option.value = scenario.value;
          option.text = scenario.text;
        });

        // 添加事件选项
        const eventDefault = eventSelect.createEl("option");
        eventDefault.value = "";
        eventDefault.text = "请选择事件背景...";
        eventDefault.disabled = true;
        eventDefault.selected = true;

        roleScenarioConfig[selectedRole].events.forEach(event => {
          const option = eventSelect.createEl("option");
          option.value = event.value;
          option.text = event.text;
        });
      } else {
        scenarioSelect.disabled = true;
        eventSelect.disabled = true;

        const scenarioDisabled = scenarioSelect.createEl("option");
        scenarioDisabled.text = "请先选择角色...";

        const eventDisabled = eventSelect.createEl("option");
        eventDisabled.text = "请先选择角色...";
      }
    });

    const buttonContainer = container.createDiv();
    buttonContainer.style.cssText = "display: flex; gap: 15px; justify-content: center; margin-top: 35px; padding-top: 20px; border-top: 1px solid #eee;";

    // 生成按钮
    const generateBtn = buttonContainer.createEl("button", { text: "🎯 生成场景" });
    generateBtn.style.cssText = "background: #4CAF50; color: white; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 15px; font-weight: bold; min-width: 120px;";
    generateBtn.onclick = () => {
      if (!roleSelect.value || !scenarioSelect.value || !eventSelect.value) {
        new Notice("请完成所有选择项");
        return;
      }

      const selections = {
        role: roleSelect.value,
        scenario: scenarioSelect.value,
        event: eventSelect.value
      };
      modal.close();
      resolve(selections);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 15px; min-width: 120px;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    modal.open();
  });
}

exports.default = {
  entry: generateSpecificScenario,
  name: "generateSpecificScenario",
  description: `交互式数据合规场景生成器 - 自定义角色、场景和事件

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥。==

  使用方法：
  \`generateSpecificScenario('你的密钥')\`

  也可以指定其他付费模型：
  \`generateSpecificScenario('你的密钥', 'glm-4-plus')\`

  🎯 交互式选择：

  👥 **对话角色选择**：
  - 🏢 CEO/高管 - 战略决策者
  - 💻 IT技术人员 - 系统开发维护
  - 💼 销售人员 - 客户关系管理
  - 👥 HR人事 - 员工数据管理
  - ⚖️ 法务顾问 - 法律合规专家
  - 🙋 客户/用户 - 数据主体
  - 🏛️ 监管机构 - 执法部门
  - 🤝 供应商/合作伙伴 - 第三方

  📋 **场景类型选择**：
  - 💬 咨询会议 - 专业建议提供
  - 📚 内部培训 - 知识传授
  - 🔍 合规审查 - 风险评估
  - 🚨 事件响应 - 危机处理
  - 🤝 商务谈判 - 协议磋商
  - 🏛️ 监管沟通 - 官方对接

  🎭 **事件背景选择**：
  - 🔓 数据泄露事件 - 安全事故处理
  - 🚀 新系统上线 - 合规评估
  - 🌍 跨境数据传输 - 国际合规
  - 📞 用户投诉处理 - 权利保护
  - 🔍 监管检查 - 合规验证
  - 📄 合同谈判 - 条款协商
  - 👨‍🏫 员工培训 - 意识提升
  - 📋 政策更新 - 制度调整

  ✨ **功能特点**：
  - 🎯 完全自定义的场景组合
  - 📋 AI根据选择生成具体练习
  - 📚 专业的数据合规词汇和表达
  - ⚠️ 真实的挑战和应对策略
  - ✅ 明确的成功评估标准
  - 📁 自动创建独立练习文件

  🎮 **使用流程**：
  1. 运行脚本弹出选择界面
  2. 选择对话角色、场景类型、事件背景
  3. AI生成定制化的具体练习场景
  4. 自动创建练习文件并打开
  `,
};
