---
关系: GDPR（核心法规） → 规范控制者与处理者（角色） → 通过SCCs约束双方 → 双方需维护数据处理记录 → 控制者通过年度审计监督处理者
类型: 合规机制
相关法律:
  - GDPR第44-49条
已学: true
---
# 控制者与处理者

GDPR（核心法规） → 规范**控制者与处理者**（角色） → 通过**SCCs**约束双方 → 双方需维护**数据处理记录** → 控制者通过**年度审计**监督处理者
[[BCR vs SCCs为什么有时要同时用SCCs和BCR？]]
这个关系图展示了与欧盟《通用数据保护条例》（GDPR）相关的几个关键概念及其相互关系，具体如下：

1. **GDPR_General_Data_Protection_Regulation**  
    GDPR是核心法规，为其他概念提供法律基础，涉及个人数据处理的合规要求。
    
2. **data_controller_and_data_processor_under_GDPR_Article_47_and_48**
    
    - **数据控制者（Data Controller）** 和 **数据处理者（Data Processor）** 是GDPR中定义的两类角色，分别对数据处理的合法性和安全性负责。
        
    - **第47条和第48条** 可能涉及跨境数据传输的规则（需确认具体条款内容，因GDPR实际条款编号可能不同）。
        
3. **standard_contractual_clauses**  
    **标准合同条款（SCCs）** 是欧盟批准的模板合同，用于确保跨境数据传输符合GDPR要求，通常由控制者和处理者签署。
    
4. **data_processing_records**  
    GDPR要求控制者和处理者**记录数据处理活动**（如目的、数据类型、接收方等），以证明合规性。
    
5. **audit_data_processors_annually**  
    控制者需**每年审计数据处理者**的操作，确保其遵守GDPR和合同条款（如SCCs）。
    

---

**关系总结**：

- GDPR（核心法规） → 规范**控制者与处理者**（角色） → 通过**SCCs**约束双方 → 双方需维护**数据处理记录** → 控制者通过**年度审计**监督处理者。
    
- 图中可能省略箭头，但逻辑上这些概念层层递进，共同构成GDPR合规框架。
    

**注意**：若条款编号（47、48）有误，需参考GDPR实际条文（如第28条“处理者义务”或第44-49条“跨境传输”）。
参考GDPR实际条文（如第28条“处理者义务”或第44-49条“跨境传输”）。
[[data_controller_and_data_processor_under_GDPR_Article_47_and_48]]

