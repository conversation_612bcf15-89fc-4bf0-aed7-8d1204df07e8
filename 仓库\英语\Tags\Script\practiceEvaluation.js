// 防止重复弹窗的全局变量
let isEvaluationModalOpen = false;

async function practiceEvaluation() {
  // 防止重复弹窗
  if (isEvaluationModalOpen) {
    new Notice("评价窗口已打开，请先完成当前评价");
    return;
  }
  
  const { currentFile } = this;
  const file = currentFile;
  
  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习')) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }
  
  try {
    isEvaluationModalOpen = true;
    
    // 显示评价选择弹窗
    const evaluation = await showEvaluationModal();
    if (!evaluation) {
      new Notice("已取消评价");
      return;
    }
    
    // 计算综合评分
    const totalScore = calculateTotalScore(evaluation.fluency, evaluation.feeling);
    const scoreEvaluation = getScoreEvaluation(totalScore);
    const emoji = getScoreEmoji(totalScore);

    // 更新文件属性
    await app.fileManager.processFrontMatter(file, (frontmatter) => {
      frontmatter['流利程度'] = evaluation.fluency;
      frontmatter['练习感受'] = evaluation.feeling;
      frontmatter['综合评分'] = totalScore;
      frontmatter['评价'] = emoji;
      frontmatter['评价时间'] = new Date().toLocaleString('zh-CN');
    });

    new Notice(scoreEvaluation);

    return {
      fluency: evaluation.fluency,
      feeling: evaluation.feeling,
      score: totalScore,
      evaluation: scoreEvaluation,
      emoji: emoji
    };
    
  } catch (error) {
    new Notice("评价过程中发生错误");
    console.error("评价错误:", error);
  } finally {
    isEvaluationModalOpen = false;
  }
}

// 计算综合评分
function calculateTotalScore(fluency, feeling) {
  const fluencyScores = {
    "卡壳": 3,
    "一般": 6,
    "流利": 9
  };

  const feelingScores = {
    "需改进": 2,
    "一般": 5,
    "良好": 8
  };

  const fluencyScore = fluencyScores[fluency] || 0;
  const feelingScore = feelingScores[feeling] || 0;

  // 加权平均：流利程度占60%，练习感受占40%
  const totalScore = Math.round(fluencyScore * 0.6 + feelingScore * 0.4);
  return Math.min(10, Math.max(1, totalScore)); // 确保在1-10范围内
}

// 根据评分获取评价
function getScoreEvaluation(score) {
  if (score >= 9) return "★卓越★";
  if (score >= 8) return "◆优秀◆";
  if (score >= 7) return "●良好●";
  if (score >= 6) return "▲不错▲";
  if (score >= 5) return "■一般■";
  if (score >= 4) return "▼加油▼";
  if (score >= 3) return "◇努力◇";
  return "※重练※";
}

// 根据评分获取表情包
function getScoreEmoji(score) {
  if (score >= 9) return "🏆";      // 9-10分：冠军
  if (score >= 8) return "🎉";      // 8分：庆祝
  if (score >= 7) return "😊";      // 7分：开心
  if (score >= 6) return "👍";      // 6分：不错
  if (score >= 5) return "😐";      // 5分：一般
  if (score >= 4) return "😕";      // 4分：有点失望
  if (score >= 3) return "😰";      // 3分：担心
  return "😭";                      // 1-2分：难过
}

// 显示评价选择弹窗
async function showEvaluationModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("📝 练习评价");

    // 确保弹窗关闭时重置状态
    modal.onClose = () => {
      isEvaluationModalOpen = false;
    };

    const container = modal.contentEl.createDiv();
    container.style.cssText = "padding: 25px; width: 500px;";
    
    // 流利程度选择
    const fluencyLabel = container.createEl("h3", { text: "🗣️ 流利程度" });
    fluencyLabel.style.cssText = "margin: 0 0 15px 0; font-size: 16px; color: #333;";
    
    const fluencyContainer = container.createDiv();
    fluencyContainer.style.cssText = "display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 30px;";
    
    const fluencyOptions = [
      { value: "卡壳", text: "😰 卡壳", color: "#f44336" },
      { value: "一般", text: "😐 一般", color: "#ff9800" },
      { value: "流利", text: "😊 流利", color: "#4caf50" }
    ];
    
    let selectedFluency = "";
    const fluencyButtons = [];
    
    fluencyOptions.forEach(option => {
      const btn = fluencyContainer.createEl("button", { text: option.text });
      btn.style.cssText = `padding: 12px; border: 2px solid #ddd; border-radius: 8px; background: white; cursor: pointer; font-size: 14px; transition: all 0.2s;`;
      
      btn.onclick = () => {
        // 重置所有按钮
        fluencyButtons.forEach(b => {
          b.style.borderColor = "#ddd";
          b.style.backgroundColor = "white";
        });
        
        // 高亮选中按钮
        btn.style.borderColor = option.color;
        btn.style.backgroundColor = option.color + "20";
        selectedFluency = option.value;
      };
      
      fluencyButtons.push(btn);
    });
    
    // 练习感受选择
    const feelingLabel = container.createEl("h3", { text: "💭 练习感受" });
    feelingLabel.style.cssText = "margin: 0 0 15px 0; font-size: 16px; color: #333;";
    
    const feelingContainer = container.createDiv();
    feelingContainer.style.cssText = "display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 30px;";
    
    const feelingOptions = [
      { value: "需改进", text: "😔 需改进", color: "#f44336" },
      { value: "一般", text: "🙂 一般", color: "#ff9800" },
      { value: "良好", text: "🎯 良好", color: "#4caf50" }
    ];
    
    let selectedFeeling = "";
    const feelingButtons = [];
    
    feelingOptions.forEach(option => {
      const btn = feelingContainer.createEl("button", { text: option.text });
      btn.style.cssText = `padding: 12px; border: 2px solid #ddd; border-radius: 8px; background: white; cursor: pointer; font-size: 14px; transition: all 0.2s;`;
      
      btn.onclick = () => {
        // 重置所有按钮
        feelingButtons.forEach(b => {
          b.style.borderColor = "#ddd";
          b.style.backgroundColor = "white";
        });
        
        // 高亮选中按钮
        btn.style.borderColor = option.color;
        btn.style.backgroundColor = option.color + "20";
        selectedFeeling = option.value;
      };
      
      feelingButtons.push(btn);
    });
    
    // 按钮区域
    const buttonContainer = container.createDiv();
    buttonContainer.style.cssText = "display: flex; gap: 15px; justify-content: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "✅ 保存评价" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 15px; font-weight: bold; min-width: 120px;";
    
    let isProcessing = false; // 防止重复点击
    
    confirmBtn.onclick = () => {
      if (isProcessing) return;
      
      if (!selectedFluency || !selectedFeeling) {
        new Notice("请完成所有评价项");
        return;
      }
      
      isProcessing = true;
      confirmBtn.textContent = "保存中...";
      confirmBtn.style.background = "#ccc";
      
      const evaluation = {
        fluency: selectedFluency,
        feeling: selectedFeeling
      };
      
      // 延迟一点关闭，确保状态更新
      setTimeout(() => {
        modal.close();
        resolve(evaluation);
      }, 100);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 12px 25px; border: none; border-radius: 6px; cursor: pointer; font-size: 15px; min-width: 120px;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    modal.open();
  });
}

exports.default = {
  entry: practiceEvaluation,
  name: "practiceEvaluation",
  description: `口语练习评价和评分脚本

  功能：
  - 🗣️ 流利程度选择：卡壳/一般/流利
  - 💭 练习感受选择：需改进/一般/良好
  - 📊 自动计算综合评分（1-10分）
  - 💾 自动保存到文件属性中
  - ⏰ 记录评价时间
  - 🎯 显示简洁的评分结果

  使用方法：
  1. 在口语练习笔记中运行：\`practiceEvaluation()\`
  2. 选择流利程度和练习感受
  3. 点击"保存评价"完成
  4. 自动计算并显示评分结果

  保存的属性：
  - 流利程度：选择的流利程度
  - 练习感受：选择的练习感受
  - 综合评分：自动计算的总分
  - 评价：对应的表情包
  - 评价时间：评价的时间戳

  评分算法：
  - 流利程度权重：60%（卡壳=3分，一般=6分，流利=9分）
  - 练习感受权重：40%（需改进=2分，一般=5分，良好=8分）
  - 综合评分：加权平均后四舍五入（1-10分）

  显示结果：
  - ★卓越★ (9-10分)
  - ◆优秀◆ (8分)
  - ●良好● (7分)
  - ▲不错▲ (6分)
  - ■一般■ (5分)
  - ▼加油▼ (4分)
  - ◇努力◇ (3分)
  - ※重练※ (1-2分)
  `,
};
