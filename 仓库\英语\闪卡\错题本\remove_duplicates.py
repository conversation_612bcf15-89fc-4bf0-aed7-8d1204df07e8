import os
import hashlib

def get_file_hash(filepath):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(filepath, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def find_duplicate_files(folder):
    """查找文件夹中的重复文件"""
    hashes = {}
    duplicates = []
    
    for root, _, files in os.walk(folder):
        for file in files:
            if file.endswith('.md'):
                filepath = os.path.join(root, file)
                file_hash = get_file_hash(filepath)
                
                if file_hash in hashes:
                    duplicates.append((filepath, hashes[file_hash]))
                else:
                    hashes[file_hash] = filepath
    
    return duplicates

def remove_duplicates(duplicates):
    """删除重复文件"""
    for dup in duplicates:
        try:
            os.remove(dup[0])
            print(f"已删除重复文件: {dup[0]}")
        except Exception as e:
            print(f"删除文件 {dup[0]} 时出错: {e}")

if __name__ == "__main__":
    folder = os.path.dirname(os.path.abspath(__file__))
    print(f"正在扫描文件夹: {folder}")
    
    duplicates = find_duplicate_files(folder)
    
    if duplicates:
        print("\n找到以下重复文件:")
        for dup in duplicates:
            print(f"{dup[0]} 与 {dup[1]} 内容相同")
        
        confirm = input("\n确认要删除这些重复文件吗? (y/n): ")
        if confirm.lower() == 'y':
            remove_duplicates(duplicates)
            print("\n重复文件删除完成")
        else:
            print("\n取消删除操作")
    else:
        print("\n未找到重复文件")