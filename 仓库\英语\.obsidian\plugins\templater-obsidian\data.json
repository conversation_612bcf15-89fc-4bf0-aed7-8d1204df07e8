{"command_timeout": 5, "templates_folder": "02-Areas/003-项目/Obsidian Templates", "templates_pairs": [["", ""]], "trigger_on_file_creation": true, "auto_jump_to_cursor": true, "enable_system_commands": false, "shell_path": "", "user_scripts_folder": "", "enable_folder_templates": true, "folder_templates": [{"folder": "Daily Notes", "template": "02-Areas/003-项目/Obsidian Templates/Daily Notes.md"}, {"folder": "01-Projects", "template": "02-Areas/003-项目/Obsidian Templates/New Project.md"}, {"folder": "02-Areas", "template": "02-Areas/003-项目/Obsidian Templates/New Area.md"}, {"folder": "03-Resources", "template": "02-Areas/003-项目/Obsidian Templates/New Resource.md"}], "enable_file_templates": false, "file_templates": [{"regex": ".*", "template": ""}], "syntax_highlighting": true, "syntax_highlighting_mobile": false, "enabled_templates_hotkeys": [""], "startup_templates": [""], "enable_ribbon_icon": true}