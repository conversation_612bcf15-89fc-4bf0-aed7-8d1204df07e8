# README

## 📁 文件夹结构

```
英语练习插件/
├── 英语练习插件.html          # 🎮 主练习界面 (双击打开)
├── 笔记数据提取器.py          # 🔧 数据提取工具
├── 练习数据.json             # 📊 从笔记提取的数据
├── 练习配置.json             # ⚙️ 练习参数配置
├── 英语练习插件使用说明.md     # 📖 详细使用指南
├── 插件改进说明.md           # 🔄 改进内容说明
├── 使用演示.md              # 🎬 使用演示和示例
└── README.md               # 📋 本文件
```

## 🚀 快速开始

### 1. 立即使用
双击 `英语练习插件.html` 即可开始练习！

### 2. 更新数据（可选）
如果您添加了新的笔记，可以运行：
```bash
python 笔记数据提取器.py
```

## 🎯 主要功能

- **🎲 随机抽签**: 场景、模板、关键词随机组合
- **📝 模板填空**: 基于您的17个场景短语模板
- **🔤 关键词造句**: 使用指定关键词构造句子
- **🎬 情景对话**: 模拟真实工作场景
- **✏️ 错误纠正**: 基于您的错题本
- **🎯 自选关键词**: 6大类别可选关键词
- **📊 真实分级**: 基础/中级/高级有实际区别
- **📈 学习追踪**: 完整的进度记录

## 📊 数据来源

所有练习内容100%来自您的笔记库：
- **场景短语**: 从 `闪卡/短语/场景短语映射表.md` 提取
- **模板结构**: 从 `错题本/*.md` 提取
- **变量数据**: 从 `闪卡/变量/*.md` 提取
- **错误模式**: 从错题本的 `common_errors` 字段提取

## 🎮 使用方法

1. **打开插件**: 双击 `英语练习插件.html`
2. **选择设置**:
   - 场景: 随机/开会/合规/技术/法律
   - 难度: 基础🟢/中级🟡/高级🔴
   - 模式: 模板填空/关键词造句/情景对话/错误纠正
   - 关键词: 随机🎲/自选🎯
3. **开始练习**: 点击"🚀 开始练习"

## 📖 详细文档

- **使用指南**: 查看 `英语练习插件使用说明.md`
- **改进说明**: 查看 `插件改进说明.md`
- **使用演示**: 查看 `使用演示.md`

## 🔄 数据更新

当您的笔记有更新时：
1. 在此文件夹中运行 `python 笔记数据提取器.py`
2. 刷新练习页面
3. 新数据自动生效

## 💡 使用技巧

- **每日坚持**: 建议每天练习15-30分钟
- **循序渐进**: 从基础难度开始，逐步提高
- **多模式练习**: 轮换使用不同练习模式
- **自选关键词**: 针对薄弱环节进行专项练习

## 🎉 开始您的英语提升之旅！

现在您拥有了一个完全基于自己笔记的智能英语练习系统。

**记住**: Practice makes perfect! 🚀
