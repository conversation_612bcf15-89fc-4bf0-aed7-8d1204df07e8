/* set the styles */

.password-second-confirm {
  font-weight: bold;
}

.password-disclaimer {
  font-weight: bold;
}

.encryptionmethod-second-confirm {
  font-weight: bold;
}

.settings-auth-related {
  border-top: 1px solid var(--background-modifier-border);
  padding-top: 18px;
}

.settings-percentage-custom-hide {
  display: none;
}

.settings-encryption-method-hide {
  display: none;
}

.s3-disclaimer {
  font-weight: bold;
}
.s3-hide {
  display: none;
}

.dropbox-disclaimer {
  font-weight: bold;
}
.dropbox-hide {
  display: none;
}

.dropbox-auth-button-hide {
  display: none;
}

.dropbox-revoke-auth-button-hide {
  display: none;
}

.onedrive-disclaimer {
  font-weight: bold;
}
.onedrive-hide {
  display: none;
}

.onedrive-auth-button-hide {
  display: none;
}

.onedrive-revoke-auth-button-hide {
  display: none;
}

.onedrivefull-allow-to-use-hide {
  display: none;
}

.onedrivefull-disclaimer {
  font-weight: bold;
}
.onedrivefull-hide {
  display: none;
}

.onedrivefull-auth-button-hide {
  display: none;
}

.onedrivefull-revoke-auth-button-hide {
  display: none;
}

.webdav-disclaimer {
  font-weight: bold;
}
.webdav-hide {
  display: none;
}

.webdav-customheaders-textarea {
  font-family: monospace;
}

.webdis-disclaimer {
  font-weight: bold;
}
.webdis-hide {
  display: none;
}

.googledrive-disclaimer {
  font-weight: bold;
}
.googledrive-hide {
  display: none;
}

.googledrive-allow-to-use-hide {
  display: none;
}

.googledrive-auth-button-hide {
  display: none;
}

.googledrive-revoke-auth-button-hide {
  display: none;
}

.box-disclaimer {
  font-weight: bold;
}
.box-hide {
  display: none;
}

.box-allow-to-use-hide {
  display: none;
}

.box-auth-button-hide {
  display: none;
}

.box-revoke-auth-button-hide {
  display: none;
}

.pcloud-disclaimer {
  font-weight: bold;
}
.pcloud-hide {
  display: none;
}

.pcloud-allow-to-use-hide {
  display: none;
}

.pcloud-auth-button-hide {
  display: none;
}

.pcloud-revoke-auth-button-hide {
  display: none;
}

.yandexdisk-disclaimer {
  font-weight: bold;
}
.yandexdisk-hide {
  display: none;
}

.yandexdisk-allow-to-use-hide {
  display: none;
}

.yandexdisk-auth-button-hide {
  display: none;
}

.yandexdisk-revoke-auth-button-hide {
  display: none;
}

.koofr-disclaimer {
  font-weight: bold;
}
.koofr-hide {
  display: none;
}

.koofr-allow-to-use-hide {
  display: none;
}

.koofr-auth-button-hide {
  display: none;
}

.koofr-revoke-auth-button-hide {
  display: none;
}

.azureblobstorage-disclaimer {
  font-weight: bold;
}
.azureblobstorage-hide {
  display: none;
}

.azureblobstorage-allow-to-use-hide {
  display: none;
}

.qrcode-img {
  width: 350px;
  height: 350px;
}

.ignorepaths-textarea {
  font-family: monospace;
}

.logtohttpserver-warning {
  color: red;
  font-weight: bolder;
}

.setting-need-wrapping .setting-item-control {
  /* flex-wrap: wrap; */
  display: grid;
}

.pro-disclaimer {
  font-weight: bold;
}
.pro-hide {
  display: none;
}

.pro-auth-button-hide {
  display: none;
}

.pro-revoke-auth-button-hide {
  display: none;
}
