---
类型: 设计原则机制
相关法律:
  - GDPR第25条
适用场景: 系统设计和产品开发阶段
关键理念: 事前预防、全生命周期、最小化处理
关系: GDPR Article 25 → 要求隐私设计 → 在开发阶段嵌入保护 → 默认最高隐私设置 → 持续评估优化
---
# 隐私设计与默认隐私原则

1. **[[privacy_by_design_and_by_default_under_GDPR_Article_25]]**  
   GDPR第25条规定的隐私设计和默认隐私

2. **[[Privacy_by_Design_principles]]**  
   隐私设计的七项基本原则

3. **[[data_minimization_principle_under_GDPR_Article_51c]]**  
   数据最小化原则在设计中的体现

4. **[[IoT_data_processing_under_GDPR_with_privacy_by_design]]**  
   物联网设备的隐私设计实施

5. **[[mobile_application_data_processing_under_GDPR_with_privacy_by_design]]**  
   移动应用的隐私设计要求

---
**关系总结**：
GDPR Article 25 → 要求隐私设计 → 在开发阶段嵌入保护 → 默认最高隐私设置 → 持续评估优化

**属性**：
- 类型：设计原则机制
- 法律依据：GDPR第25条
- 适用场景：系统设计和产品开发阶段
- 关键理念：事前预防、全生命周期、最小化处理
