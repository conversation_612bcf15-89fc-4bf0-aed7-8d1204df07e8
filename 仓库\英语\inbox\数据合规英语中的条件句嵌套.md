---
title: 数据合规英语中的条件句嵌套
source: "[[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]"
tags:
  - 数据合规
  - 条件句
  - 嵌套
keywords:
  - 条件句
  - 嵌套
  - 严谨
created: 2025-07-31
type: 原子笔记
模板结构: IF [条件1] + AND/OR [条件2], THEN [主体] + SHALL [动作] + UNLESS [例外]
已学: true
---

# 数据合规英语中的条件句嵌套

使用严谨的条件句嵌套，如 'IF [条件1] + AND/OR [条件2], THEN [主体] + SHALL [动作] + UNLESS [例外].'

---

## 元信息
- **来源笔记**: [[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]
- **创建时间**: 2025/8/1 04:34:09
- **标签**: #数据合规 #条件句 #嵌套
- **关键词**: 条件句, 嵌套, 严谨

## 相关链接
- 返回原笔记: [[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]
