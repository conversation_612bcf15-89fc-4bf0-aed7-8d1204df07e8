---
title: "场景-短语映射表"
correct_expression: "情景类型 → 触发短语组 → 输出模板"
usage_scenario: "场景化表达"
common_errors: "场景与短语不匹配或模板使用错误"
error_type: "短语 - 场景映射"
tags: ["场景映射", "触发机制", "模板应用", "情景表达"]
difficulty: "高级"
frequency: "高频"
concept_type: "场景化写作必备技能"
映射逻辑: "情景识别 → 短语激活 → 模板填充"
模板结构: "情景类型_→_触发短语组_→_输出模板"
---

# 场景短语映射表

## 中文翻译
情景与短语的对应关系表

## 使用场景
快速响应不同情景的表达需求、提高写作效率

## 核心映射关系

| 情景类型 | 触发短语组 | 输出模板 |
|----------|------------|----------|
| **定期审计** | at least annually + thoroughly | "We conduct __ audits __ to ensure..." |
| **数据泄露响应** | immediately after + revoke | "__ detecting a breach, __ all affected credentials" |
| **跨境数据传输** | subject to + SCCs | "Data transfer is __ the __ requirements" |
| **系统实施** | prior to implementation + examine | "__ __, teams must __ all configurations" |
| **合规检查** | in accordance with + ensure | "Processing __ Article 6 to __ compliance" |
| **安全监控** | every 24 hours + monitor | "__ __ system performance and security" |
| **风险评估** | by means of + assess | "__ automated tools, __ potential vulnerabilities" |

## 使用方法

### 步骤1: 识别情景
确定当前需要表达的具体情景类型

### 步骤2: 激活短语组
根据情景类型自动调用对应的短语组合

### 步骤3: 填充模板
将具体内容填入预设的输出模板

## 实际应用示例

### 定期审计情景
- **触发**: 需要描述审计频率和质量
- **短语组**: "at least annually" + "thoroughly"
- **输出**: "We conduct security audits at least annually and thoroughly review all access controls to ensure compliance"

### 数据泄露响应情景
- **触发**: 需要描述紧急响应程序
- **短语组**: "immediately after" + "revoke"
- **输出**: "Immediately after detecting a breach, revoke all affected credentials and notify relevant authorities"

## 记忆要点
- 建立情景与短语的自动关联
- 熟练掌握常用模板结构
- 根据具体需求灵活调整
- 保持表达的专业性和准确性# 场景短语映射表
