// 原意表达翻译器 - 简化版
async function originalIntentSimple(token) {
  if (!token) {
    new Notice("❌ 请传入AI密钥参数");
    return;
  }

  const { currentFile } = this;
  const file = currentFile;

  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !file.basename.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }

  // 显示简单输入界面
  const userInput = await showSimpleInputModal();
  if (!userInput) {
    new Notice("已取消原意翻译");
    return;
  }

  try {
    new Notice("🤖 AI正在翻译您的原意...", 2000);
    
    // 调用AI翻译
    const translation = await translateWithAI(token, userInput);
    
    if (translation) {
      // 直接添加到笔记末尾
      await addTranslationToNote(file, userInput, translation);
      new Notice("✅ 原意翻译已添加到笔记！");
    } else {
      new Notice("❌ AI翻译失败");
    }
    
  } catch (error) {
    new Notice(`❌ 原意翻译失败: ${error.message}`);
    console.error("原意翻译错误:", error);
  }
}

// 显示简单输入模态框
async function showSimpleInputModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("💭 原意表达翻译器");

    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";

    // 说明文字
    const description = container.createEl("div");
    description.innerHTML = `
      <p style="color: #666; margin-bottom: 15px;">
        💡 输入您原本想说的中文，AI帮您翻译成地道英文！
      </p>
    `;

    // 常用表达快速选择
    const quickOptions = [
      "数据泄露是一个严重的安全问题",
      "这对公司来说是一个重大挑战", 
      "我们需要加强网络安全措施",
      "这个问题需要立即解决",
      "合规要求变得越来越严格",
      "我们必须遵守相关法规"
    ];

    container.createEl("h4", { text: "🚀 快速选择：" });
    const quickContainer = container.createDiv();
    quickContainer.style.cssText = "display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin: 10px 0 15px 0;";

    const textInput = container.createEl("textarea");
    
    quickOptions.forEach(option => {
      const btn = quickContainer.createEl("button", { text: option });
      btn.style.cssText = "padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; cursor: pointer; text-align: left; font-size: 12px;";
      btn.onclick = () => {
        textInput.value = option;
        textInput.focus();
      };
      btn.onmouseover = () => btn.style.background = "#e3f2fd";
      btn.onmouseout = () => btn.style.background = "#f9f9f9";
    });

    // 输入框
    container.createEl("h4", { text: "✏️ 或输入您的原意：" });
    textInput.placeholder = "请输入您原本想表达的中文意思...";
    textInput.style.cssText = "width: 100%; padding: 10px; margin: 5px 0 15px 0; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; height: 80px; resize: vertical;";

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "15px";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "🤖 AI翻译" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;";
    confirmBtn.onclick = () => {
      const input = textInput.value.trim();
      if (!input) {
        new Notice("请输入您的原意");
        return;
      }
      modal.close();
      resolve(input);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    modal.open();
    setTimeout(() => textInput.focus(), 100);
  });
}

// AI翻译
async function translateWithAI(token, chineseText) {
  const prompt = `你是一个专业的中英翻译专家，请将以下中文翻译成地道的英文。

中文原意：${chineseText}

要求：
1. 提供正式商务且地道自然的英文表达
2. 适合口语练习和商务沟通
3. 提供2个不同的表达方式

请以JSON格式返回：
{
  "primaryTranslation": "主要翻译（最推荐的表达）",
  "alternativeTranslation": "备选翻译",
  "keyPoints": "重点提示和注意事项"
}`;

  try {
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 800,
      }),
    });

    const result = response.json;
    const aiContent = result.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new Error("AI返回结果为空");
    }

    // 尝试解析JSON
    try {
      const jsonMatch = aiContent.match(/```json\n([\s\S]*?)\n```/) || aiContent.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : aiContent;
      
      const parsedResult = JSON.parse(jsonStr);
      return parsedResult;
    } catch (parseError) {
      console.error("JSON解析失败:", parseError);
      
      // 如果JSON解析失败，返回简单格式
      return {
        primaryTranslation: aiContent.substring(0, 100),
        alternativeTranslation: "",
        keyPoints: "请注意语法和用词准确性"
      };
    }

  } catch (error) {
    console.error("AI翻译调用失败:", error);
    throw error;
  }
}

// 添加翻译到笔记（包含属性更新）
async function addTranslationToNote(file, originalText, translation) {
  try {
    const content = await app.vault.read(file);

    // 提取YAML前置数据
    const yamlMatch = content.match(/^(---\n[\s\S]*?\n---)/);
    if (!yamlMatch) {
      throw new Error("未找到YAML前置数据");
    }

    let yamlContent = yamlMatch[1];
    const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');

    // 处理属性值，确保YAML格式正确
    const safeOriginalText = originalText.replace(/"/g, '\\"').replace(/\n/g, ' ');
    const safePrimaryTranslation = translation.primaryTranslation.replace(/"/g, '\\"').replace(/\n/g, ' ');

    // 更新或添加YAML属性
    if (yamlContent.includes('原本想说:')) {
      yamlContent = yamlContent.replace(/原本想说: ".*?"/g, `原本想说: "${safeOriginalText}"`);
    } else {
      yamlContent = yamlContent.replace(/\n---$/, `\n原本想说: "${safeOriginalText}"\n---`);
    }

    if (yamlContent.includes('AI翻译:')) {
      yamlContent = yamlContent.replace(/AI翻译: ".*?"/g, `AI翻译: "${safePrimaryTranslation}"`);
    } else {
      yamlContent = yamlContent.replace(/\n---$/, `\nAI翻译: "${safePrimaryTranslation}"\n---`);
    }

    // 生成正文内容
    const translationSection = `

## 💭 原意表达

### 您原本想说：
${originalText}

### AI推荐翻译：
**主要表达**: ${translation.primaryTranslation}

${translation.alternativeTranslation ?
`**备选表达**: ${translation.alternativeTranslation}` : ''}

### 重点提示：
${translation.keyPoints}

---
`;

    // 合并更新后的内容
    const updatedContent = yamlContent + '\n' + mainContent + translationSection;
    await app.vault.modify(file, updatedContent);

  } catch (error) {
    console.error("添加翻译到笔记失败:", error);
    throw error;
  }
}

exports.default = {
  entry: originalIntentSimple,
  name: "originalIntentSimple", 
  description: `💭 原意表达翻译器 - 简化版

🎯 核心特点：
- 💭 输入您原本想说的中文意思
- 🤖 AI智能翻译成地道英文
- 📝 直接添加到笔记末尾
- 🚀 简单易用，稳定可靠

🎮 使用方法：
originalIntentSimple("your_api_key_here")

💡 功能特色：
- 快速选择常用表达
- 自定义输入原意
- 提供主要和备选翻译
- 重点提示和注意事项

🔧 输出内容：
- 原本想说的中文
- AI推荐的英文翻译
- 重点提示和学习要点

适用于口语练习后，想知道原意的正确英文表达！

==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==
  `
};
