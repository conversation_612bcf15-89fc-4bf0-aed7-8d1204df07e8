// 原意表达翻译器 - 批量文件生成版
async function originalIntentSimple(token) {
  if (!token) {
    new Notice("❌ 请传入AI密钥参数");
    return;
  }

  const { currentFile } = this;
  const file = currentFile;

  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !file.basename.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }

  try {
    // 从当前笔记中提取原本想说的内容
    const originalTexts = await extractOriginalTextsFromNote(file);

    if (!originalTexts || originalTexts.length === 0) {
      new Notice("❌ 未在笔记中找到原本想说的内容");
      return;
    }

    // 智能筛选最有价值的10条内容
    const limitedTexts = selectBestTexts(originalTexts, 10);

    if (originalTexts.length > 10) {
      new Notice(`⚠️ 找到 ${originalTexts.length} 条内容，智能筛选出最有价值的 ${limitedTexts.length} 条`, 3000);
    }

    new Notice(`📚 开始处理 ${limitedTexts.length} 条原本想说内容...`, 3000);

    let successCount = 0;
    let failCount = 0;

    // 批量处理每条原本想说内容
    for (let i = 0; i < limitedTexts.length; i++) {
      const originalItem = limitedTexts[i];
      const originalText = originalItem.text || originalItem; // 兼容旧格式
      const problemType = originalItem.type || '表达问题';

      try {
        new Notice(`🤖 正在处理 ${i + 1}/${limitedTexts.length}: ${originalText.substring(0, 20)}...`, 2000);

        // 调用AI翻译
        const translation = await translateWithAI(token, originalText);

        if (translation) {
          // 为每条内容创建单独的文件
          await createSeparateFile(originalText, translation, file.basename, problemType);
          successCount++;
        } else {
          failCount++;
        }

        // 添加延迟避免API限制
        if (i < originalTexts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        console.error(`处理原意失败: ${originalText}`, error);
        failCount++;
      }
    }

    new Notice(`✅ 批量处理完成！成功: ${successCount}, 失败: ${failCount}`);

  } catch (error) {
    new Notice(`❌ 批量处理失败: ${error.message}`);
    console.error("原意翻译错误:", error);
  }
}

// 智能筛选最有价值的文本
function selectBestTexts(texts, maxCount) {
  if (texts.length <= maxCount) {
    return texts;
  }

  // 为每个文本计算价值分数
  const scoredTexts = texts.map(item => {
    const text = item.text || item;
    let score = 0;

    // 长度分数：适中长度更有价值
    if (text.length >= 15 && text.length <= 100) {
      score += 10;
    } else if (text.length >= 10 && text.length <= 150) {
      score += 5;
    }

    // 内容复杂度分数
    if (text.includes('（') && text.includes('）')) score += 8; // 包含解释说明
    if (text.includes('vs') || text.includes('对比')) score += 6; // 包含对比
    if (text.match(/[a-zA-Z]/)) score += 4; // 包含英文
    if (text.includes('问题') || text.includes('挑战')) score += 5; // 问题相关

    // 专业术语分数
    if (text.includes('数据') || text.includes('技术') || text.includes('政策')) score += 6;
    if (text.includes('公司') || text.includes('企业') || text.includes('商业')) score += 4;

    // 避免重复和无意义内容
    if (text.includes('不知道') || text.includes('不会')) score -= 3;
    if (text.length < 10) score -= 5;
    if (text.includes('修改') || text.includes('时间')) score -= 8;

    // 问题类型多样性加分
    const problemType = item.type || identifyProblemType(text);
    if (problemType === '词汇量问题') score += 3;
    if (problemType === '表述结构问题') score += 4;
    if (problemType === '文化表达问题') score += 5;

    return {
      ...item,
      score: score,
      text: text
    };
  });

  // 按分数排序并取前N个
  const sortedTexts = scoredTexts
    .sort((a, b) => b.score - a.score)
    .slice(0, maxCount);

  // 确保问题类型多样性
  const diverseTexts = ensureTypeDiversity(sortedTexts, maxCount);

  return diverseTexts;
}

// 确保问题类型多样性
function ensureTypeDiversity(texts, maxCount) {
  const typeGroups = {};
  const result = [];

  // 按类型分组
  texts.forEach(item => {
    const type = item.type || '表达问题';
    if (!typeGroups[type]) {
      typeGroups[type] = [];
    }
    typeGroups[type].push(item);
  });

  // 从每个类型中选择最好的
  const types = Object.keys(typeGroups);
  let currentIndex = 0;

  while (result.length < maxCount && result.length < texts.length) {
    const currentType = types[currentIndex % types.length];
    if (typeGroups[currentType] && typeGroups[currentType].length > 0) {
      result.push(typeGroups[currentType].shift());
    }
    currentIndex++;

    // 如果所有类型都用完了，跳出循环
    if (types.every(type => !typeGroups[type] || typeGroups[type].length === 0)) {
      break;
    }
  }

  return result;
}

// 识别问题类型
function identifyProblemType(text) {
  // 词汇量问题
  if (text.includes('不知道') || text.includes('不会说') || text.includes('词汇') ||
      text.includes('单词') || text.includes('不懂') || text.includes('没学过')) {
    return '词汇量问题';
  }

  // 表述结构问题
  if (text.includes('怎么说') || text.includes('表达') || text.includes('结构') ||
      text.includes('语法') || text.includes('句型') || text.includes('组织')) {
    return '表述结构问题';
  }

  // 语法问题
  if (text.includes('时态') || text.includes('语法') || text.includes('动词') ||
      text.includes('介词') || text.includes('冠词')) {
    return '语法问题';
  }

  // 发音问题
  if (text.includes('发音') || text.includes('读音') || text.includes('音标')) {
    return '发音问题';
  }

  // 文化差异问题
  if (text.includes('文化') || text.includes('习惯') || text.includes('礼貌') ||
      text.includes('场合') || text.includes('正式')) {
    return '文化表达问题';
  }

  // 默认为表达问题
  return '表达问题';
}

// 从笔记中提取原本想说的内容
async function extractOriginalTextsFromNote(file) {
  try {
    const content = await app.vault.read(file);
    const originalTexts = [];

    // 按行分割内容
    const lines = content.split('\n');

    // 查找原本想说相关的内容
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 匹配各种可能的格式
      if (line.match(/^[-*•]\s*(.+)/) || // 列表项
          line.match(/^\d+\.\s*(.+)/) || // 数字列表
          (line.includes('原本想说') && line.includes(':')) || // 包含"原本想说:"
          (line.includes('想说') && line.length > 10 && !line.includes('#'))) { // 包含"想说"且不是标题

        // 提取实际内容并识别问题类型
        let extractedText = line
          .replace(/^[-*•]\s*/, '') // 移除列表符号
          .replace(/^\d+\.\s*/, '') // 移除数字列表
          .replace(/.*?原本想说[:：]\s*/, '') // 移除"原本想说:"前缀
          .replace(/.*?想说[:：]\s*/, '') // 移除"想说:"前缀
          .replace(/中文_/g, '') // 移除"中文_"前缀
          .replace(/英文_/g, '') // 移除"英文_"前缀
          .replace(/\*+/g, '') // 移除所有星号
          .replace(/[*•]/g, '') // 移除星号和项目符号
          .replace(/\s+/g, ' ') // 将多个空格合并为一个
          .trim();

        // 识别问题类型
        const problemType = identifyProblemType(extractedText);

        // 更严格的过滤条件，避免生成过多重复内容
        if (extractedText.length > 8 &&
            extractedText.length < 200 && // 避免过长的内容
            !extractedText.includes('#') &&
            !extractedText.includes('---') &&
            !extractedText.includes('```') &&
            !extractedText.includes('http') && // 排除链接
            !extractedText.match(/^\d+$/) && // 排除纯数字
            !extractedText.includes('时间') && // 排除时间相关
            !extractedText.includes('修改') && // 排除修改说明
            !extractedText.includes('笔记') && // 排除笔记说明
            (extractedText.includes('_') || extractedText.length > 15)) { // 包含下划线或足够长
          originalTexts.push({
            text: extractedText,
            type: problemType
          });
        }
      }
    }

    // 去重（基于文本内容）
    const uniqueTexts = originalTexts.filter((item, index, self) =>
      index === self.findIndex(t => t.text === item.text)
    );

    return uniqueTexts;

  } catch (error) {
    console.error("提取原本想说内容失败:", error);
    throw error;
  }
}

// 创建单独的文件
async function createSeparateFile(originalText, translation, sourceFileName, problemType = '表达问题') {
  try {
    // 生成安全的文件名
    const safeTitle = originalText
      .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
      .replace(/[_\s]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 30);

    const fileName = `${safeTitle}.md`;
    const folderPath = "英语/口语训练/原意表达";
    const filePath = `${folderPath}/${fileName}`;

    // 检查文件是否已存在
    if (await app.vault.adapter.exists(filePath)) {
      console.log(`跳过已存在的文件: ${fileName}`);
      return;
    }

    // 获取当前日期信息
    const today = new Date();
    const fullDateString = today.toISOString().split('T')[0];
    const timeString = today.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });

    // 处理属性值，确保YAML格式正确
    const safeOriginalText = originalText.replace(/"/g, '\\"').replace(/\n/g, ' ');
    const safePrimaryTranslation = translation.primaryTranslation.replace(/"/g, '\\"').replace(/\n/g, ' ');

    // 生成文件内容
    const fileContent = `---
记录日期: "${fullDateString}"
记录时间: "${timeString}"
原本想说: "${safeOriginalText}"
AI翻译: "${safePrimaryTranslation}"
问题类型: "${problemType}"
翻译类型: "原意表达"
来源练习: "[[${sourceFileName}]]"
---

# ${originalText}

## 💭 原意表达

### 您原本想说：
${originalText}

### AI推荐翻译：
**主要表达**: ${translation.primaryTranslation}

${translation.alternativeTranslation ?
`**备选表达**: ${translation.alternativeTranslation}` : ''}

### 重点提示：
${translation.keyPoints}

---
来源练习: [[${sourceFileName}]]
`;

    // 确保文件夹存在
    try {
      await app.vault.createFolder(folderPath);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }

    // 创建文件
    await app.vault.create(filePath, fileContent);

  } catch (error) {
    console.error("创建单独文件失败:", error);
    throw error;
  }
}

// 显示简单输入模态框
async function showSimpleInputModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("💭 原意表达翻译器");

    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";

    // 说明文字
    const description = container.createEl("div");
    description.innerHTML = `
      <p style="color: #666; margin-bottom: 15px;">
        💡 输入您原本想说的中文，AI帮您翻译成地道英文！
      </p>
    `;

    // 常用表达快速选择
    const quickOptions = [
      "数据泄露是一个严重的安全问题",
      "这对公司来说是一个重大挑战", 
      "我们需要加强网络安全措施",
      "这个问题需要立即解决",
      "合规要求变得越来越严格",
      "我们必须遵守相关法规"
    ];

    container.createEl("h4", { text: "🚀 快速选择：" });
    const quickContainer = container.createDiv();
    quickContainer.style.cssText = "display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin: 10px 0 15px 0;";

    const textInput = container.createEl("textarea");
    
    quickOptions.forEach(option => {
      const btn = quickContainer.createEl("button", { text: option });
      btn.style.cssText = "padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9; cursor: pointer; text-align: left; font-size: 12px;";
      btn.onclick = () => {
        textInput.value = option;
        textInput.focus();
      };
      btn.onmouseover = () => btn.style.background = "#e3f2fd";
      btn.onmouseout = () => btn.style.background = "#f9f9f9";
    });

    // 输入框
    container.createEl("h4", { text: "✏️ 或输入您的原意：" });
    textInput.placeholder = "请输入您原本想表达的中文意思...";
    textInput.style.cssText = "width: 100%; padding: 10px; margin: 5px 0 15px 0; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; height: 80px; resize: vertical;";

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "15px";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "🤖 AI翻译" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;";
    confirmBtn.onclick = () => {
      const input = textInput.value.trim();
      if (!input) {
        new Notice("请输入您的原意");
        return;
      }
      modal.close();
      resolve(input);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    modal.open();
    setTimeout(() => textInput.focus(), 100);
  });
}

// AI翻译
async function translateWithAI(token, chineseText) {
  const prompt = `你是一个专业的中英翻译专家，请将以下中文翻译成地道的英文。

中文原意：${chineseText}

要求：
1. 提供正式商务且地道自然的英文表达
2. 适合口语练习和商务沟通
3. 提供2个不同的表达方式

请以JSON格式返回：
{
  "primaryTranslation": "主要翻译（最推荐的表达）",
  "alternativeTranslation": "备选翻译",
  "keyPoints": "重点提示和注意事项"
}`;

  try {
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 800,
      }),
    });

    const result = response.json;
    const aiContent = result.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new Error("AI返回结果为空");
    }

    // 尝试解析JSON
    try {
      const jsonMatch = aiContent.match(/```json\n([\s\S]*?)\n```/) || aiContent.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : aiContent;
      
      const parsedResult = JSON.parse(jsonStr);
      return parsedResult;
    } catch (parseError) {
      console.error("JSON解析失败:", parseError);
      
      // 如果JSON解析失败，返回简单格式
      return {
        primaryTranslation: aiContent.substring(0, 100),
        alternativeTranslation: "",
        keyPoints: "请注意语法和用词准确性"
      };
    }

  } catch (error) {
    console.error("AI翻译调用失败:", error);
    throw error;
  }
}

// 添加翻译到笔记（包含属性更新）
async function addTranslationToNote(file, originalText, translation) {
  try {
    const content = await app.vault.read(file);

    // 提取YAML前置数据
    const yamlMatch = content.match(/^(---\n[\s\S]*?\n---)/);
    if (!yamlMatch) {
      throw new Error("未找到YAML前置数据");
    }

    let yamlContent = yamlMatch[1];
    const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');

    // 处理属性值，确保YAML格式正确
    const safeOriginalText = originalText.replace(/"/g, '\\"').replace(/\n/g, ' ');
    const safePrimaryTranslation = translation.primaryTranslation.replace(/"/g, '\\"').replace(/\n/g, ' ');

    // 更新或添加YAML属性
    if (yamlContent.includes('原本想说:')) {
      yamlContent = yamlContent.replace(/原本想说: ".*?"/g, `原本想说: "${safeOriginalText}"`);
    } else {
      yamlContent = yamlContent.replace(/\n---$/, `\n原本想说: "${safeOriginalText}"\n---`);
    }

    if (yamlContent.includes('AI翻译:')) {
      yamlContent = yamlContent.replace(/AI翻译: ".*?"/g, `AI翻译: "${safePrimaryTranslation}"`);
    } else {
      yamlContent = yamlContent.replace(/\n---$/, `\nAI翻译: "${safePrimaryTranslation}"\n---`);
    }

    // 生成正文内容
    const translationSection = `

## 💭 原意表达

### 您原本想说：
${originalText}

### AI推荐翻译：
**主要表达**: ${translation.primaryTranslation}

${translation.alternativeTranslation ?
`**备选表达**: ${translation.alternativeTranslation}` : ''}

### 重点提示：
${translation.keyPoints}

---
`;

    // 合并更新后的内容
    const updatedContent = yamlContent + '\n' + mainContent + translationSection;
    await app.vault.modify(file, updatedContent);

  } catch (error) {
    console.error("添加翻译到笔记失败:", error);
    throw error;
  }
}

exports.default = {
  entry: originalIntentSimple,
  name: "originalIntentSimple",
  description: `💭 原意表达翻译器 - 批量文件生成版

🎯 核心特点：
- 📚 自动识别笔记中的原本想说内容
- 🤖 AI智能翻译成地道英文
- 📁 为每条内容生成单独文件
- 🏷️ 自动添加属性标签

🎮 使用方法：
originalIntentSimple("your_api_key_here")

💡 功能特色：
- 智能识别列表项和原意内容
- 批量处理多条原意
- 生成独立的翻译文件
- 包含完整的属性信息

🔧 输出内容：
- 每条原意生成独立的 .md 文件
- 文件属性包含"原本想说"和"AI翻译"
- 提供主要和备选翻译
- 重点提示和学习要点

📁 文件存储位置：
英语/口语训练/原意表达/

🔍 识别格式：
- 列表项：- 内容 或 * 内容
- 数字列表：1. 内容
- 标记格式：原本想说: 内容

适用于已有原意内容的笔记，批量生成翻译文件！

==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==
  `
};
