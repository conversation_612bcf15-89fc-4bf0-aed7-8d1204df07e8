async function createPracticeNote() {
  const { currentFile } = this;
  const file = currentFile;
  const title = file.basename;
  
  // 检查是否是具体场景文件
  if (!file.path.includes('具体练习') && !file.path.includes('练习场景')) {
    new Notice("请在具体练习场景文件中使用此脚本");
    return;
  }
  
  // 获取当前日期，生成文件名格式 MMDD
  const today = new Date();
  const month = (today.getMonth() + 1).toString().padStart(2, '0');
  const day = today.getDate().toString().padStart(2, '0');
  const dateString = `${month}${day}`; // 0802格式
  
  const fullDateString = today.toISOString().split('T')[0]; // 2025-08-01格式
  const timeString = today.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit' 
  }); // HH:mm格式
  
  // 创建练习笔记文件名，格式：080201.md
  const practiceFolder = "英语/口语训练/口语练习";

  // 查找当天已有的练习笔记数量
  const allFiles = app.vault.getMarkdownFiles();
  const todayFiles = allFiles.filter(file =>
    file.path.includes(practiceFolder) &&
    file.basename.startsWith(dateString)
  );

  // 生成序号（从01开始）
  const sequenceNumber = (todayFiles.length + 1).toString().padStart(2, '0');
  const practiceFileName = `${dateString}${sequenceNumber}.md`;
  const practiceFilePath = `${practiceFolder}/${practiceFileName}`;

  // 创建练习笔记文件
  await createPracticeFile(practiceFilePath, practiceFileName, title, fullDateString, timeString);
}

// 创建练习笔记文件
async function createPracticeFile(filePath, fileName, scenarioTitle, fullDate, time) {
  // 生成练习笔记内容
  const practiceContent = `---
练习日期: "${fullDate}"
练习时间: "${time}"
流利程度: ""
练习感受: ""
遇到的问题: ""
改进点: ""
场景来源: "[[${scenarioTitle}]]"
练习类型: "口语练习"
---

# ${fileName.replace('.md', '')}
`;

  try {
    // 确保文件夹存在
    const practiceFolder = "英语/口语训练/口语练习";
    try {
      await app.vault.createFolder(practiceFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 创建练习笔记文件
    await app.vault.create(filePath, practiceContent);
    
    new Notice(`✅ 练习笔记已创建: ${fileName}`);
    
    // 打开新创建的文件
    const createdFile = app.vault.getAbstractFileByPath(filePath);
    if (createdFile) {
      await app.workspace.getLeaf().openFile(createdFile);
    }
    
    // 在原场景文件中添加练习链接
    await addPracticeLinkToScenario(scenarioTitle, fileName.replace('.md', ''));
    
  } catch (error) {
    new Notice(`❌ 创建练习笔记失败: ${error.message}`);
    console.error("创建练习笔记错误:", error);
  }
}

// 在场景文件中添加练习链接
async function addPracticeLinkToScenario(scenarioTitle, practiceFileName) {
  try {
    // 查找场景文件
    const allFiles = app.vault.getMarkdownFiles();
    const scenarioFile = allFiles.find(f => f.basename === scenarioTitle);
    
    if (!scenarioFile) {
      console.log("未找到场景文件，跳过添加链接");
      return;
    }
    
    // 读取场景文件内容
    const content = await app.vault.read(scenarioFile);
    
    // 检查是否已有练习记录部分
    const practiceLink = `- [[${practiceFileName}]] - ${new Date().toLocaleDateString('zh-CN')}`;
    
    if (content.includes('## 🔄 练习记录')) {
      // 如果已有练习记录部分，添加到该部分
      const updatedContent = content.replace(
        /## 🔄 练习记录\n/,
        `## 🔄 练习记录\n\n${practiceLink}\n`
      );
      await app.vault.modify(scenarioFile, updatedContent);
    } else {
      // 如果没有练习记录部分，在文件末尾添加
      const updatedContent = content + `\n\n## 🔄 练习记录\n\n${practiceLink}\n`;
      await app.vault.modify(scenarioFile, updatedContent);
    }
    
    new Notice("✅ 已在场景文件中添加练习链接");
    
  } catch (error) {
    console.error("添加练习链接失败:", error);
  }
}



exports.default = {
  entry: createPracticeNote,
  name: "createPracticeNote",
  description: `创建口语练习笔记

  功能：
  - 📝 基于当前具体场景生成练习笔记
  - 📅 自动使用当天日期作为文件名 (YYMMDD格式)
  - 🏷️ 包含完整的练习属性和评估框架
  - 🔗 自动在场景文件中添加练习链接
  - 📁 保存到 英语/口语训练/口语练习 文件夹

  使用方法：
  1. 打开任意具体练习场景文件
  2. 运行脚本：\`createPracticeNote()\`
  3. 自动创建今日练习笔记并打开

  笔记属性：
  - 练习日期：当天日期
  - 练习时间：当前时间
  - 流利程度：待填写
  - 练习感受：待填写
  - 遇到的问题：待填写
  - 改进点：待填写
  - 场景来源：链接到原场景

  笔记结构：
  - 📅 练习信息
  - 🎯 练习目标
  - 🗣️ 练习过程
  - 📊 练习评估
  - 🔍 问题与改进
  - 📈 进度记录
  - 🔗 相关链接

  特殊功能：
  - 如果当天练习笔记已存在，可选择覆盖或创建新版本
  - 自动在原场景文件中添加练习记录链接
  - 支持多次练习记录（添加时间后缀）
  `,
};
