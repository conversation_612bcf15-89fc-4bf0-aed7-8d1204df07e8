---
title: "[用户] has the right to [动作] [时限]"
correct_expression: "[用户] has the right to [动作] [时限]"
usage_scenario: "用户权利声明"
common_errors: "忽略时限要求或权利范围"
error_type: "主干法 - 用户权利框架"
tags: ["用户权利", "时限要求", "GDPR合规", "隐私保护"]
difficulty: "中级"
frequency: "高频"
concept_type: "隐私法必备句型"
用户_options: ["User", "Data Subject", "Employee", "Customer", "Individual", "Person", "Consumer"]
动作_options: ["access", "delete", "rectify", "restrict", "object", "portability", "withdraw consent", "request information", "file complaint"]
时限_options: ["within 30 days", "without undue delay", "within one business day", "upon request", "within 24 hours", "no later than 72 hours", "immediately"]
相关法律: ["GDPR", "CCPA", "HIPAA", "PIPL", "LGPD", "DPA 2018"]
---

# [用户] has the right to [动作] [时限]

## 中文翻译
[用户]有权在____时限内____。

## 使用场景
隐私政策、用户权利条款、GDPR合规文档

## 示例
- User has the right to access within 30 days
- Data Subject has the right to delete without undue delay
- Individual has the right to rectify upon request

## 记忆要点
- GDPR赋予数据主体的基本权利
- 时限要求通常为30天或"不得无故拖延"
- 是隐私政策中的核心条款