# 英语练习插件使用说明

## 📋 概述

这是一个基于您的笔记库的智能英语练习插件，可以帮助您：
- 🎲 随机抽取场景进行练习
- 📝 根据模板结构填空练习
- 🔤 使用关键词造句练习
- 🎬 模拟真实情景对话
- ✏️ 纠正常见表达错误

## 🚀 快速开始

### 1. 文件准备
确保以下文件在同一目录下：
- `英语练习插件.html` - 主练习界面
- `笔记数据提取器.py` - 数据提取工具
- 您的笔记库文件夹结构

### 2. 数据提取（可选）
如果想要最新的笔记数据，运行：
```bash
python 笔记数据提取器.py
```
这将生成：
- `练习数据.json` - 提取的笔记数据
- `练习配置.json` - 练习配置文件

### 3. 开始练习
直接打开 `英语练习插件.html` 即可开始练习！

## 🎮 功能详解

### 场景选择
- **🎲 随机场景**: 从所有场景中随机选择
- **🤝 开会场景**: 基于您的开会笔记
- **⚖️ 合规场景**: 基于您的合规笔记  
- **💻 技术场景**: 技术实施相关表达
- **📜 法律场景**: 法律条款和义务表达

### 难度等级
- **🟢 基础**: 简单模板和常用词汇
- **🟡 中级**: 标准合规表达（默认）
- **🔴 高级**: 复杂句式和专业术语

### 练习模式

#### 📝 模板填空
- 基于您笔记中的17个场景短语模板
- 提供关键词提示
- 检查模板结构完整性

**示例**:
```
模板: According_to_[法规文件]_we_[行动]_[结果]_therefore_[目标]_even_[条件]
关键词: EDPB Guidelines, restructured, maintaining, legality
```

#### 🔤 关键词造句
- 提供3-5个必须使用的关键词
- 要求构造完整、专业的句子
- 检查关键词使用情况

**示例**:
```
关键词: pursuant to, implement, AES-256, encryption
要求: 使用所有关键词造句
```

#### 🎬 情景对话
- 模拟真实工作场景
- 提供专业词汇建议
- 评估回应的专业性

**示例**:
```
场景: 向客户解释数据处理的法律依据
情况: 客户询问为什么需要收集个人信息
```

#### ✏️ 错误纠正
- 基于您的错题本
- 识别常见表达错误
- 提供正确的专业表达

**示例**:
```
错误: "Company must do encryption for protect data"
要求: 提供正确的专业表达
```

## 📊 评分系统

### 评分标准
- **专业术语使用** (20分): 使用合规专业词汇
- **语法准确性** (25分): 语法结构正确
- **模板符合度** (25分): 符合标准模板结构
- **语境适当性** (20分): 适合具体场景
- **表达创新性** (10分): 表达的丰富性

### 分数等级
- **90-100分**: 🎉 优秀 - 专业准确
- **70-89分**: 👍 良好 - 基本准确
- **60-69分**: 💪 及格 - 需要改进
- **60分以下**: 📚 需要加强学习

## 📈 统计功能

### 实时统计
- **今日练习次数**: 当天练习的总次数
- **准确率**: 正确答案的百分比
- **连续正确**: 当前连续正确的次数
- **练习时长**: 累计练习时间

### 数据保存
- 统计数据自动保存到浏览器本地存储
- 每30秒自动保存一次
- 关闭浏览器后数据不会丢失

## 🎯 基于您笔记的特色功能

### 1. 场景短语映射
基于您的 `场景短语映射表.md`：
- 自动识别情景类型
- 激活对应的短语组
- 应用正确的输出模板

### 2. 变量数据库
基于您的变量笔记文件夹：
- **法律名称**: GDPR, PIPL, CCPA等
- **主体**: Data controllers, processors等
- **义务**: implement, establish, maintain等
- **技术方案**: AES-256, zero-knowledge proofs等
- **时间表达**: within 72 hours, annually等

### 3. 模板结构
基于您的17个场景短语：
- `According_to_[法规文件]_we_[行动]_[结果]_therefore_[目标]_even_[条件]`
- `Although_[法规条款]_imposes_[限制]_[业务目标]_remains_achievable_through_[解决方案]`
- 等等...

### 4. 错误模式识别
基于您的错题本：
- 常见中式英语错误
- 专业术语误用
- 语法结构问题

## 💡 使用技巧

### 提高练习效果
1. **每日坚持**: 建议每天练习15-30分钟
2. **循序渐进**: 从基础难度开始，逐步提高
3. **多模式练习**: 轮换使用不同练习模式
4. **记录错误**: 注意常犯的错误类型

### 专业表达要点
1. **使用正式语言**: 避免口语化表达
2. **引用法律条款**: 增加表达的权威性
3. **使用被动语态**: 使表达更加正式
4. **注意时态一致**: 保持时态的准确性

### 关键词积累
- **连接词**: pursuant to, in accordance with, therefore
- **义务动词**: shall, must, implement, ensure
- **时间表达**: within, by, no later than
- **条件表达**: provided that, where, when

## 🔧 自定义配置

### 添加新场景
1. 在对应的笔记文件夹中添加新的markdown文件
2. 运行 `笔记数据提取器.py` 重新提取数据
3. 刷新练习页面即可使用新数据

### 修改评分标准
编辑 `练习配置.json` 文件中的 `scoring_criteria` 部分

### 调整难度等级
修改 `difficulty_levels` 中的分数阈值和复杂度设置

## 🐛 常见问题

### Q: 为什么看不到我的最新笔记？
A: 运行 `python 笔记数据提取器.py` 重新提取数据

### Q: 如何重置统计数据？
A: 在浏览器中清除本地存储数据，或按F12打开开发者工具，在Console中输入：
```javascript
localStorage.removeItem('englishPracticeStats');
```

### Q: 可以离线使用吗？
A: 是的，HTML文件可以完全离线使用

### Q: 如何备份练习数据？
A: 练习数据保存在浏览器本地存储中，建议定期导出统计数据

## 📞 技术支持

如果遇到问题或有改进建议，请：
1. 检查文件路径是否正确
2. 确保笔记文件格式符合要求
3. 查看浏览器控制台是否有错误信息

## 🎉 开始您的英语练习之旅！

现在您已经了解了所有功能，快打开 `英语练习插件.html` 开始练习吧！

记住：**Practice makes perfect!** 坚持练习，您的英语合规表达能力一定会显著提升！
