exports.default = {
  name: 'addBulletPointsToHeadingEnd',
  entry: addBulletPointsToHeadingEnd,
  scopes: ['formula', 'button'],
  description: `
  添加无序列表到指定标题的文末
  `
}

const quickAddApi = app.plugins.plugins.quickadd.api;

async function addBulletPointsToHeadingEnd() {
  const file = this.currentFile;
  
  let bulletItems = await quickAddApi.wideInputPrompt('添加无序列表', '请输入列表项，按回车键添加新的项。');
  if (!bulletItems) {
    new Notice('退出添加无序列表');
    return;
  }
  
  let bullets = bulletItems.split('\n')
    .map(item => item.trim())
    .filter(item => item)
    .map(item => `- ${item}`)
    .join('\n');

  let fileHeading = await getFileHeading(file);
  let selectItem = await quickAddApi.suggester(['添加到文末', ...fileHeading], ['', ...fileHeading]);

  await appendBulletsToHeadingEnd(file, bullets, selectItem);
}

async function appendBulletsToHeadingEnd(file, bullets, heading) {
  const fileContent = await app.vault.read(file);
  let bulletsContent;

  if (heading) {
    const headingPattern = new RegExp(`^${heading}$`, 'gm');
    const matches = [...fileContent.matchAll(headingPattern)];
    
    if (matches.length > 0) {
      const lastMatch = matches[matches.length - 1];
      const startIndex = lastMatch.index;
      const endIndex = findContentEndIndex(fileContent, startIndex + heading.length);
      
      const beforeContent = fileContent.substring(0, endIndex);
      const afterContent = fileContent.substring(endIndex);
      
      bulletsContent = `${beforeContent}\n${bullets}${afterContent}`;
    } else {
      bulletsContent = `${fileContent}\n${bullets}`;
    }
  } else {
    bulletsContent = `${fileContent}\n${bullets}`;
  }

  await app.vault.modify(file, bulletsContent);
}

function findContentEndIndex(content, startIndex) {
  // 查找下一个标题或文件末尾
  const nextHeadingIndex = content.indexOf('\n#', startIndex);
  return nextHeadingIndex !== -1 ? nextHeadingIndex : content.length;
}

async function getFileHeading(file) {
  const fileContent = await app.vault.read(file);
  const headingMatch = fileContent.match(/^(#{1,6}) (.*)/gm);
  return headingMatch ? headingMatch.map(heading => heading.trim()) : [];
}