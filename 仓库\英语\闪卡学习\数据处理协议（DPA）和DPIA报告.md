---
决策树: true
关系: 客户上传数据到你的工具 → 你分析后生成报告 → 需签DPA。- 你只能按客户要求处理数据（不能擅自用数据训练模型）；    - 必须保障数据安全（加密、访问控制等）；    - 数据泄露时需及时通知客户。
人话: DPA是“保证书”（保证不乱用数据），DPIA是“可行性报告”（证明数据操作安全）- DPA：你和客户的“数据安全责任合同”，必须签；- DPIA：高风险数据处理前的“自查报告”，按需做。 高风险数据处理前要做“隐私影响评估”（类似安全预检）    - 如果你用AI分析健康数据，或者把数据传到国外服务器 → 先写个报告证明你不会搞砸（DPIA）。    - 政府或客户点头了才能干。                         当你的数据处理活动可能对用户隐私造成高风险时，例如：    - 大规模分析敏感数据（如医疗记录）；    - 使用新技术（如AI模型分析行为数据）；    - 数据跨境传输（如服务器在国外）。
相关法律:
  - "- GDPR第35条（强制要求高风险场景）；- 中国《个人信息保护法》第55条（类似“个人信息保护影响评估”）"
---
# 数据处理协议（DPA）和DPIA报告
### **1. 数据处理协议（DPA，Data Processing Agreement）**


```mermaid
flowchart TD
    A[客户上传数据] --> B{数据含个人信息？}
    B -->|是| C[必须签署DPA]
    B -->|否| D[无需DPA]
    C --> E[核心条款：<br>1. 仅按客户要求处理数据<br>2. 加密存储<br>3. 泄露后72小时内通知]
    E --> F[双方签字生效]
    F --> G[合规数据处理]
```


**人话解释**：

- 只要客户给你个人数据（比如Excel里的用户电话），就必须签DPA。
    
- 合同里写清楚：你只能按客户说的用数据，不能偷偷拿去做别的！



#### **是什么？**

- 一份**法律合同**，明确数据控制方（你的客户）和处理方（你或你的工具）之间的责任。
- **核心内容**：
    - 你只能按客户要求处理数据（不能擅自用数据训练模型）；
    - 必须保障数据安全（加密、访问控制等）；
    - 数据泄露时需及时通知客户。

#### **何时需要？**

- 只要**你替客户处理个人数据**（如分析含用户信息的Excel），即使你是外包方或工具提供商，都必须签DPA。
- **示例**：
    - 客户上传数据到你的工具 → 你分析后生成报告 → 需签DPA。

#### **法律依据**

- 欧盟《通用数据保护条例》（GDPR）第28条；
- 中国《个人信息保护法》第21条（类似要求）。

---

### **2. DPIA报告（数据保护影响评估，Data Protection Impact Assessment）**
**人话解释**：


```mermaid
flowchart TD
    A[启动数据处理项目] --> B{是否高风险？<br>（敏感数据/AI决策/跨境传输）}
    B -->|是| C[必须做DPIA]
    B -->|否| D[无需DPIA]
    C --> E[评估内容：<br>1. 数据有哪些？<br>2. 可能泄露吗？<br>3. 如何保护？]
    E --> F[提交监管机构或客户]
    F --> G[通过后实施]
```




- 如果你用AI分析健康数据，或者把数据传到国外服务器 → 先写个报告证明你不会搞砸（DPIA）。
    
- 政府或客户点头了才能干。
    

---

### **3. DPA vs DPIA 对比表**

|**对比项**|**DPA**|**DPIA**|
|---|---|---|
|**是什么**|你和客户的“数据安全合同”|数据处理前的“风险评估报告”|
|**谁需要**|所有处理他人数据的公司|只有高风险场景（如分析医疗数据）|
|**内容**|责任划分、保密条款|数据流程图、风险点、应对措施|
|**提交给谁**|客户|监管机构/客户|
|**不做的后果**|违约赔偿|罚款（GDPR最高4%全球营收）|

---

### **4. 你的工具如何应对？**

- **DPA**：  
    ✅ 直接套用模板（[点击下载GDPR官方DPA模板](https://gdpr.eu/data-processing-agreement/))。
    
- **DPIA**：  
    ✅ 简化版：列三步：
    
    1. 数据用途（如“分析用户行为”）；
        
    2. 风险（如“可能被黑客攻击”）；
        
    3. 措施（如“数据加密+定期删除”）。
        

---

**一句话总结**：

- **DPA**是“保证书”（保证不乱用数据），**DPIA**是“可行性报告”（证明数据操作安全）。
    
- 前者必须签，后者看情况。
#### **是什么？**

- 一份**风险评估报告**，提前分析你的数据处理活动是否会对用户隐私造成高风险。
- **核心内容**：
    - 你处理哪些数据？（如身份证号、健康记录）；
    - 数据如何存储、传输？是否有泄露风险？
    - 如何降低风险？（如加密、匿名化）。

#### **何时需要？**

- 当你的数据处理活动**可能对用户隐私造成高风险**时，例如：
    - 大规模分析敏感数据（如医疗记录）；
    - 使用新技术（如AI模型分析行为数据）；
    - 数据跨境传输（如服务器在国外）。

#### **法律依据**

- GDPR第35条（强制要求高风险场景）；
- 中国《个人信息保护法》第55条（类似“个人信息保护影响评估”）。

---

### **3. 两者的关键区别**

|**对比项**|数据处理协议（DPA）|DPIA报告|
|---|---|---|
|**性质**|法律合同（双方责任划分）|风险评估报告（自查隐私隐患）|
|**目的**|明确“谁可以怎么用数据”|证明“我的数据处理方式足够安全”|
|**触发条件**|只要处理他人数据就必须签|仅高风险场景需要（如敏感数据/AI分析）|
|**提交对象**|客户或合作方|监管机构（如被抽查时）或客户要求|
|**典型内容**|数据用途、保密条款、泄露响应流程|数据处理流程图、风险点、应对措施|

---

### **4. 你的工具是否需要这两者？**

#### **（1）DPA**

- **必须签**：如果客户上传的数据含个人信息（即使只是试用版），你就要和客户签DPA（通常由客户提供模板）。

#### **（2）DPIA**

- **可能需要**：如果你的工具涉及以下情况：
    - 分析**敏感数据**（健康、财务、精准定位等）；
    - 使用**AI模型自动决策**（如拒绝贷款申请）；
    - 数据**长期存储**或跨境传输。

---

### **5. 实际案例**

#### **场景：你的隐私风险评估工具**

- **DPA**：客户上传Excel（含用户手机号）前，你和客户签协议，承诺不存储数据、仅用于生成报告。
- **DPIA**：如果你的工具用AI模型自动标记高风险用户，且客户是医院（涉及健康数据），则需做DPIA。

---

### **6. 如何低成本合规？**

1. **DPA**：
    - 用标准模板（如GDPR官方DPA模板），修改双方名称即可。
2. **DPIA**：
    - 简化版：列出数据处理步骤+风险控制措施（如“数据仅在浏览器内存处理”）；
    - 工具：用[ICO DPIA模板](https://ico.org.uk/) （免费）。

---

### **一句话总结**

- **DPA**：你和客户的“数据安全责任合同”，**必须签**；
- **DPIA**：高风险数据处理前的“自查报告”，**按需做**。