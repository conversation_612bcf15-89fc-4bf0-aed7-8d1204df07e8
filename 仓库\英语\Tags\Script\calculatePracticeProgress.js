async function calculatePracticeProgress() {
    try {
        const { currentFile } = this;
        const fileName = currentFile.basename;
        
        // 从文件属性中提取日期
        const frontmatter = app.metadataCache.getFileCache(currentFile)?.frontmatter || {};
        const practiceDate = frontmatter['练习日期'] || frontmatter['记录日期'];

        if (!practiceDate) {
            return "⚠️ 文件中未找到'练习日期'或'记录日期'属性";
        }

        // 解析日期字符串
        const currentDate = new Date(practiceDate);
        if (isNaN(currentDate.getTime())) {
            return "⚠️ 日期格式无效，请使用YYYY-MM-DD格式";
        }
        
        // 获取所有口语练习文件，找到最早的练习日期
        const allFiles = app.vault.getMarkdownFiles();
        const practiceFiles = [];

        for (const file of allFiles) {
            // 检查是否是口语练习文件（可以根据路径或内容判断）
            if (file.path.includes('口语') || file.path.includes('练习')) {
                const fileFrontmatter = app.metadataCache.getFileCache(file)?.frontmatter || {};
                const fileDate = fileFrontmatter['练习日期'] || fileFrontmatter['记录日期'];

                if (fileDate) {
                    const parsedDate = new Date(fileDate);
                    if (!isNaN(parsedDate.getTime())) {
                        practiceFiles.push({
                            file: file,
                            date: parsedDate,
                            basename: file.basename
                        });
                    }
                }
            }
        }
        
        if (practiceFiles.length === 0) {
            return "📝 这是你的第一次练习记录！";
        }

        // 按日期排序，找到最早的练习日期
        practiceFiles.sort((a, b) => a.date - b.date);
        const firstPracticeDate = practiceFiles[0].date;
        const firstPracticeFile = practiceFiles[0].basename;
        
        // 计算距离今天的天数差（负数表示未来，正数表示过去）
        const today = new Date();
        today.setHours(0, 0, 0, 0); // 重置时间为当天0点
        currentDate.setHours(0, 0, 0, 0);

        const timeDiff = today - currentDate;
        const daysSinceLastPractice = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

        // 计算总练习次数
        const totalPractices = practiceFiles.length;

        // 根据距离上次练习的天数给出复习紧迫度评价
        let evaluation = "";
        let tip = "";

        if (daysSinceLastPractice < 0) {
            evaluation = "🔮 未来练习";
            tip = "时间还没到呢！";
        } else if (daysSinceLastPractice === 0) {
            evaluation = "🎯 今日练习";
            tip = "很好！今天就练习了！";
        } else if (daysSinceLastPractice <= 1) {
            evaluation = "✅ 复习及时";
            tip = "昨天刚练过，记忆还很新鲜！";
        } else if (daysSinceLastPractice <= 3) {
            evaluation = "😊 复习较好";
            tip = "3天内的内容，还记得不少！";
        } else if (daysSinceLastPractice <= 7) {
            evaluation = "⚠️ 需要复习";
            tip = "一周了，该复习一下了！";
        } else if (daysSinceLastPractice <= 14) {
            evaluation = "😰 急需复习";
            tip = "两周没碰，很多都忘了吧？";
        } else if (daysSinceLastPractice <= 30) {
            evaluation = "🚨 严重生疏";
            tip = "一个月了！赶紧复习起来！";
        } else if (daysSinceLastPractice <= 90) {
            evaluation = "💀 基本遗忘";
            tip = "三个月了，估计都忘光了...";
        } else {
            evaluation = "🪦 完全遗忘";
            tip = "这么久了，重新开始吧！";
        }
        
        // 紧迫度评价
        let urgencyEval = "";
        if (daysSinceLastPractice <= 1) {
            urgencyEval = "🟢 状态良好";
        } else if (daysSinceLastPractice <= 3) {
            urgencyEval = "🟡 稍有生疏";
        } else if (daysSinceLastPractice <= 7) {
            urgencyEval = "🟠 需要复习";
        } else if (daysSinceLastPractice <= 14) {
            urgencyEval = "🔴 急需复习";
        } else {
            urgencyEval = "⚫ 严重生疏";
        }

        // 生成详细统计
        const stats = `
📊 **复习统计**
- 练习日期: ${practiceDate}
- 距今天数: ${daysSinceLastPractice}天
- 总练习次数: ${totalPractices}次
- 最早练习: ${firstPracticeFile}
        `.trim();

        return evaluation;
        
    } catch (error) {
        console.error("练习进度计算错误:", error);
        return "⚠️ 计算失败";
    }
}

exports.default = {
    name: "calculatePracticeProgress",
    description: `口语练习复习提醒脚本

    功能：
    - 📅 从文件属性中读取'练习日期'或'记录日期'
    - ⏰ 计算距离今天的天数（判断复习紧迫度）
    - 🚨 根据时间间隔给出复习提醒和评价
    - 📊 统计总练习次数
    - 💡 提供复习建议

    复习紧迫度等级：
    - 🎯 今日练习 (0天) - 很好！
    - ✅ 复习及时 (1天) - 记忆新鲜
    - 😊 复习较好 (2-3天) - 还记得不少
    - ⚠️ 需要复习 (4-7天) - 该复习了
    - 😰 急需复习 (8-14天) - 很多都忘了
    - 🚨 严重生疏 (15-30天) - 赶紧复习
    - 💀 基本遗忘 (31-90天) - 都忘光了
    - 🪦 完全遗忘 (90天+) - 重新开始

    使用方法：
    calculatePracticeProgress()

    注意：文件必须包含'练习日期'或'记录日期'属性，格式为YYYY-MM-DD
    `,
    entry: calculatePracticeProgress
};
