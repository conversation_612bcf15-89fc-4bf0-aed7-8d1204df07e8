module.exports = async function insertTaskToProject(params) {
    const { quickAddApi } = params;
    
    // 1. 获取所有符合条件的文件
    const eligibleFiles = [];
    const files = app.vault.getMarkdownFiles();
    
    for (const file of files) {
        const cache = app.metadataCache.getFileCache(file);
        if (!cache?.frontmatter) continue;
        
        // 检查tags是否包含project
        const hasProjectTag = cache.frontmatter.tags && 
                            (cache.frontmatter.tags.includes("project") || 
                             cache.frontmatter.tags === "project");
        
        // 检查任务状态是否符合要求
        const validStatus = cache.frontmatter["任务状态"] && 
                          ["🚀Doing", "📥Inbox"].includes(cache.frontmatter["任务状态"]);
        
        if (hasProjectTag && validStatus) {
            eligibleFiles.push({
                path: file.path,
                stat: app.vault.getFileByPath(file.path)?.stat // 获取文件状态信息
            });
        }
    }
    
    if (eligibleFiles.length === 0) {
        new Notice("⚠️ 没有找到符合条件的文件（需同时满足：tags包含project且任务状态为🚀Doing/📥Inbox）");
        return;
    }
    
    // 2. 按最后编辑时间排序（最近编辑的在前）
    eligibleFiles.sort((a, b) => {
        return b.stat.mtime - a.stat.mtime; // 降序排序
    });
    
    // 3. 让用户选择文件（显示文件名和最后编辑时间）
    const selectedFile = await quickAddApi.suggester(
        (fileObj) => {
            const fileName = fileObj.path.split("/").pop();
            const editTime = new Date(fileObj.stat.mtime).toLocaleString();
            return `${fileName} (最后编辑: ${editTime})`;
        },
        eligibleFiles
    );
    
    if (!selectedFile) return;
    
    // 4. 调用 Tasks API 创建任务
    const tasksPlugin = app.plugins.getPlugin('obsidian-tasks-plugin');
    if (!tasksPlugin) {
        new Notice("⚠️ 未找到 Tasks 插件");
        return;
    }
    
    const taskLine = await tasksPlugin.apiV1.createTaskLineModal();
    
    if (taskLine) {
        // 5. 在选定的文件中追加任务
        const file = app.vault.getAbstractFileByPath(selectedFile.path);
        const content = await app.vault.read(file);
        await app.vault.modify(file, content + "\n" + taskLine);
        new Notice(`✅ 任务已添加到 [[${selectedFile.path.split("/").pop()}]]`);
    }
};