async function smartCorpusExtractor() {
  const { currentFile } = this;
  const file = currentFile;
  
  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !file.basename.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }
  
  try {
    // 读取当前笔记内容
    const content = await app.vault.read(file);
    
    // 解析笔记内容
    const analysisResult = await analyzeOralPracticeNote(content, file.basename);
    
    if (!analysisResult) {
      new Notice("❌ 无法分析笔记内容");
      return;
    }
    
    // 显示分析结果并让用户确认
    const userConfirmed = await showAnalysisModal(analysisResult);
    
    if (userConfirmed) {
      // 创建语料笔记
      await createSmartCorpusNotes(analysisResult, file.basename);
      
      // 更新原笔记的属性
      await updatePracticeNoteProperties(file, analysisResult);
      
      new Notice("✅ 智能语料提取完成！");
    } else {
      new Notice("已取消语料提取");
    }
    
  } catch (error) {
    new Notice(`❌ 提取失败: ${error.message}`);
    console.error("智能语料提取错误:", error);
  }
}

// 分析口语练习笔记
async function analyzeOralPracticeNote(content, fileName) {
  // 提取YAML前置数据
  const yamlMatch = content.match(/^---\n([\s\S]*?)\n---/);
  let yamlData = {};
  
  if (yamlMatch) {
    const yamlContent = yamlMatch[1];
    yamlContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split(':');
      if (key && valueParts.length > 0) {
        const value = valueParts.join(':').trim().replace(/^["']|["']$/g, '');
        yamlData[key.trim()] = value;
      }
    });
  }
  
  // 提取正文内容
  const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');
  
  // 智能分析内容
  const analysis = {
    fileName: fileName,
    yamlData: yamlData,
    extractedCorpus: [],
    identifiedProblems: [],
    suggestedImprovements: [],
    originalExpressions: [],
    correctedExpressions: []
  };
  
  // 1. 提取精准语料短语（不要完整句子）
  const keyPhrases = extractKeyPhrases(mainContent);
  keyPhrases.forEach(phrase => {
    analysis.extractedCorpus.push({
      english: phrase.phrase,
      type: phrase.type,
      difficulty: determineDifficulty(phrase.phrase),
      context: phrase.context
    });
  });
  
  // 3. 分析问题和改进点
  analysis.identifiedProblems = analyzeProblems(mainContent, yamlData);
  analysis.suggestedImprovements = analyzeSuggestedImprovements(mainContent, yamlData);
  
  return analysis;
}

// 提取精准语料短语
function extractKeyPhrases(content) {
  const phrases = [];

  // 1. 提取形容词+名词组合 (如: critical issue, major challenge)
  const adjNounPatterns = [
    /\b(critical|major|important|serious|significant|urgent)\s+(issue|problem|challenge|concern|matter)\b/gi,
    /\b(data|security|privacy|cyber)\s+(breach|leak|incident|violation|attack)\b/gi,
    /\b(compliance|regulatory|legal|contractual)\s+(framework|requirement|obligation|standard)\b/gi
  ];

  adjNounPatterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        phrases.push({
          phrase: match.toLowerCase(),
          type: "形容词短语",
          context: "专业表达"
        });
      });
    }
  });

  // 2. 提取动词短语 (如: pose a challenge, raise concerns)
  const verbPhrases = [
    /\b(pose|present|create|cause)\s+(a|an)?\s*(major|significant)?\s*(challenge|problem|issue|concern)\b/gi,
    /\b(raise|address|tackle|resolve)\s+(concerns?|issues?|problems?)\b/gi,
    /\b(implement|establish|maintain|ensure)\s+(measures?|standards?|compliance|security)\b/gi
  ];

  verbPhrases.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        phrases.push({
          phrase: match.toLowerCase(),
          type: "动词短语",
          context: "行动表达"
        });
      });
    }
  });

  // 3. 提取介词短语 (如: in today's world, in accordance with)
  const prepPhrases = [
    /\bin\s+(today's|the\s+modern|contemporary)\s+world\b/gi,
    /\bin\s+accordance\s+with\b/gi,
    /\bpursuant\s+to\b/gi,
    /\bwith\s+regard\s+to\b/gi
  ];

  prepPhrases.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        phrases.push({
          phrase: match.toLowerCase(),
          type: "介词短语",
          context: "连接表达"
        });
      });
    }
  });

  // 4. 从括号中的解释提取关键词
  const explanations = content.match(/（([^）]+)）/g);
  if (explanations) {
    explanations.forEach(exp => {
      const text = exp.replace(/[（）]/g, '');
      if (text.includes('比') && text.includes('强')) {
        // 提取被比较的词汇
        const strongerWords = text.match(/[""]([^""]+)[""].*比.*[""]([^""]+)[""].*强/);
        if (strongerWords) {
          phrases.push({
            phrase: strongerWords[1],
            type: "强化词汇",
            context: "语气增强"
          });
        }
      }
    });
  }

  // 去重
  const uniquePhrases = [];
  phrases.forEach(phrase => {
    if (!uniquePhrases.some(p => p.phrase === phrase.phrase)) {
      uniquePhrases.push(phrase);
    }
  });

  return uniquePhrases;
}

// 分析问题
function analyzeProblems(content, yamlData) {
  const problems = [];
  
  // 从流利程度判断
  if (yamlData['流利程度'] === '卡壳') {
    problems.push("表达不够流利，存在卡壳现象");
  }
  
  // 从评分判断
  const score = parseInt(yamlData['综合评分']);
  if (score <= 4) {
    problems.push("整体表达质量需要提升");
  }
  
  // 从内容分析语法问题
  if (content.includes('修正后')) {
    problems.push("原始表达存在语法或用词错误");
  }
  
  // 分析具体错误类型
  if (content.includes('单数') || content.includes('复数')) {
    problems.push("单复数使用不当");
  }
  
  if (content.includes('时态')) {
    problems.push("时态使用错误");
  }
  
  if (content.includes('介词')) {
    problems.push("介词搭配不准确");
  }
  
  return problems;
}

// 分析改进建议
function analyzeSuggestedImprovements(content, yamlData) {
  const improvements = [];
  
  // 从内容提取改进建议
  if (content.includes('更地道')) {
    improvements.push("学习更地道的英语表达方式");
  }
  
  if (content.includes('更正式')) {
    improvements.push("掌握正式场合的专业表达");
  }
  
  if (content.includes('critical') && content.includes('important')) {
    improvements.push("学会使用更强烈的形容词来增强表达力度");
  }
  
  // 根据场景来源提供建议
  if (yamlData['场景来源']) {
    improvements.push(`针对${yamlData['场景来源']}场景进行专项练习`);
  }
  
  // 根据评分提供建议
  const score = parseInt(yamlData['综合评分']);
  if (score <= 3) {
    improvements.push("建议增加基础语法和词汇练习");
  } else if (score <= 6) {
    improvements.push("重点练习句式结构和表达流畅度");
  } else {
    improvements.push("继续提升表达的自然度和专业性");
  }
  
  return improvements;
}

// 提取关键术语
function extractKeyTerms(content) {
  const terms = [];
  
  // 专业术语模式
  const professionalTerms = [
    'data breach', 'data security', 'compliance', 'regulation',
    'privacy', 'protection', 'assessment', 'framework',
    'implementation', 'monitoring', 'audit', 'governance'
  ];
  
  professionalTerms.forEach(term => {
    if (content.toLowerCase().includes(term.toLowerCase())) {
      terms.push({
        term: term,
        difficulty: 'intermediate',
        context: '专业术语'
      });
    }
  });
  
  // 提取引号中的短语
  const quotedPhrases = content.match(/"([^"]{10,50})"/g);
  if (quotedPhrases) {
    quotedPhrases.forEach(phrase => {
      const cleanPhrase = phrase.replace(/"/g, '');
      if (!terms.some(t => t.term === cleanPhrase)) {
        terms.push({
          term: cleanPhrase,
          difficulty: determineDifficulty(cleanPhrase),
          context: '重点短语'
        });
      }
    });
  }
  
  return terms;
}

// 判断难度等级
function determineDifficulty(expression) {
  const wordCount = expression.split(' ').length;
  const hasComplexWords = /\b(pursuant|accordance|implementation|assessment|governance)\b/i.test(expression);
  
  if (wordCount <= 3 && !hasComplexWords) return 'basic';
  if (wordCount <= 6 || hasComplexWords) return 'intermediate';
  return 'advanced';
}

// 显示分析结果模态框
async function showAnalysisModal(analysis) {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("🤖 智能语料分析结果");
    
    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";
    container.style.maxHeight = "70vh";
    container.style.overflowY = "auto";
    
    // 显示提取的语料
    if (analysis.extractedCorpus.length > 0) {
      container.createEl("h3", { text: "📚 提取的语料 (" + analysis.extractedCorpus.length + "个)" });
      const corpusList = container.createEl("ul");
      analysis.extractedCorpus.forEach(corpus => {
        const li = corpusList.createEl("li");
        li.innerHTML = `<strong>"${corpus.english}"</strong> <span style="color: #666;">[${corpus.type}]</span>`;
      });
    }
    
    // 显示识别的问题
    if (analysis.identifiedProblems.length > 0) {
      container.createEl("h3", { text: "⚠️ 识别的问题" });
      const problemsList = container.createEl("ul");
      analysis.identifiedProblems.forEach(problem => {
        problemsList.createEl("li", { text: problem });
      });
    }
    
    // 显示改进建议
    if (analysis.suggestedImprovements.length > 0) {
      container.createEl("h3", { text: "💡 改进建议" });
      const improvementsList = container.createEl("ul");
      analysis.suggestedImprovements.forEach(improvement => {
        improvementsList.createEl("li", { text: improvement });
      });
    }
    
    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "20px";
    
    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "✅ 创建语料笔记" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";
    confirmBtn.onclick = () => {
      modal.close();
      resolve(true);
    };
    
    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(false);
    };
    
    modal.open();
  });
}

// 创建智能语料笔记
async function createSmartCorpusNotes(analysis, practiceFileName) {
  const today = new Date();
  const fullDateString = today.toISOString().split('T')[0];
  const timeString = today.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  const corpusFolder = "英语/口语训练/语料卡";

  // 确保文件夹存在
  try {
    await app.vault.createFolder(corpusFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }

  // 为每个提取的语料创建笔记
  for (let i = 0; i < analysis.extractedCorpus.length; i++) {
    const corpus = analysis.extractedCorpus[i];

    // 生成安全的文件名
    const safeTitle = corpus.english
      .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
      .replace(/[_\s]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 40);

    const fileName = `${safeTitle}_${practiceFileName.substring(0, 6)}.md`;
    const filePath = `${corpusFolder}/${fileName}`;

    // 检查文件是否已存在
    if (await app.vault.adapter.exists(filePath)) {
      continue; // 跳过已存在的文件
    }

    // 生成语料笔记内容
    const corpusContent = generateCorpusContent(corpus, analysis, practiceFileName, fullDateString, timeString);

    try {
      await app.vault.create(filePath, corpusContent);
      new Notice(`✅ 创建语料: ${corpus.english}`);
    } catch (error) {
      console.error(`创建语料笔记失败: ${fileName}`, error);
    }
  }
}

// 生成语料笔记内容
function generateCorpusContent(corpus, analysis, practiceFileName, fullDate, time) {
  // 根据分析结果生成翻译和意义
  const translation = generateTranslation(corpus.english);
  const meaning = generateMeaning(corpus, analysis);
  const errorPoint = generateErrorPoint(corpus, analysis);

  return `---
记录日期: "${fullDate}"
记录时间: "${time}"
翻译: "${translation}"
具体意义: "${meaning}"
出错点: "${errorPoint}"
语料类型: "${corpus.type}"
难度等级: "${corpus.difficulty}"
来源练习: "[[${practiceFileName}]]"
---

# ${corpus.english}

## 📝 语料详情

**原文**: ${corpus.english}
**翻译**: ${translation}
**类型**: ${corpus.type}
**难度**: ${corpus.difficulty}

## 💡 具体意义

${meaning}

## ⚠️ 学习要点

${errorPoint}

## 🎯 使用场景

${corpus.context || '专业商务沟通'}

## 📚 相关表达

${generateRelatedExpressions(corpus.english)}

## 🔄 复习计划

- [ ] 第1天：理解含义和用法
- [ ] 第3天：造句练习
- [ ] 第7天：情景对话练习
- [ ] 第14天：综合运用测试

---
来源练习: [[${practiceFileName}]]
`;
}

// 生成翻译
function generateTranslation(english) {
  const translations = {
    'data breach': '数据泄露',
    'data security': '数据安全',
    'compliance': '合规',
    'critical issue': '关键问题',
    'major challenge': '重大挑战',
    'important issue': '重要问题',
    'modern world': '现代世界',
    'today\'s world': '当今世界'
  };

  return translations[english.toLowerCase()] || '待翻译';
}

// 生成具体意义
function generateMeaning(corpus, analysis) {
  let meaning = `${corpus.english} 是一个${corpus.difficulty === 'advanced' ? '高级' : corpus.difficulty === 'intermediate' ? '中级' : '基础'}的${corpus.type}。`;

  if (corpus.english.includes('breach')) {
    meaning += '在数据保护和网络安全领域中经常使用，表示安全防护被突破的情况。';
  } else if (corpus.english.includes('critical') || corpus.english.includes('major')) {
    meaning += '这是一个强调程度的表达，比普通的形容词更有力度，适用于正式场合。';
  } else if (corpus.english.includes('compliance')) {
    meaning += '在商务和法律环境中广泛使用，表示遵守规定、标准或法律要求。';
  }

  return meaning;
}

// 生成错误点
function generateErrorPoint(corpus, analysis) {
  let errorPoint = '';

  // 根据分析结果生成错误点
  if (analysis.identifiedProblems.includes('单复数使用不当')) {
    errorPoint += '注意单复数的正确使用。';
  }

  if (analysis.identifiedProblems.includes('表达不够流利')) {
    errorPoint += '需要多练习以提高表达流畅度。';
  }

  if (corpus.difficulty === 'advanced') {
    errorPoint += '这是高级表达，需要注意使用场合和语境。';
  }

  return errorPoint || '注意发音和语调，确保在适当场合使用。';
}

// 生成相关表达
function generateRelatedExpressions(english) {
  const relatedMap = {
    'data breach': '• data leak (数据泄漏)\n• security incident (安全事件)\n• privacy violation (隐私违规)',
    'critical issue': '• major concern (主要关切)\n• serious problem (严重问题)\n• urgent matter (紧急事项)',
    'modern world': '• contemporary society (当代社会)\n• digital age (数字时代)\n• current era (当前时代)'
  };

  return relatedMap[english.toLowerCase()] || '• 相关表达待补充';
}

// 更新练习笔记属性
async function updatePracticeNoteProperties(file, analysis) {
  try {
    const content = await app.vault.read(file);

    // 提取YAML前置数据
    const yamlMatch = content.match(/^(---\n[\s\S]*?\n---)/);
    if (!yamlMatch) return;

    let yamlContent = yamlMatch[1];

    // 更新遇到的问题
    const problemsText = analysis.identifiedProblems.join('；');
    yamlContent = yamlContent.replace(
      /遇到的问题: ".*?"/,
      `遇到的问题: "${problemsText}"`
    );

    // 更新改进点
    const improvementsText = analysis.suggestedImprovements.join('；');
    yamlContent = yamlContent.replace(
      /改进点: ".*?"/,
      `改进点: "${improvementsText}"`
    );

    // 替换原内容中的YAML部分
    const updatedContent = content.replace(/^---\n[\s\S]*?\n---/, yamlContent);

    await app.vault.modify(file, updatedContent);
    new Notice("✅ 已更新练习笔记属性");

  } catch (error) {
    console.error("更新练习笔记属性失败:", error);
  }
}

exports.default = {
  entry: smartCorpusExtractor,
  name: "smartCorpusExtractor",
  description: `🤖 智能语料提取器 2.0

功能特点：
- 🔍 自动分析口语练习笔记内容
- 📚 智能提取关键语料和表达
- ⚠️ 自动识别学习问题
- 💡 生成个性化改进建议
- 🏷️ 自动填充笔记属性
- 📊 语料难度智能分级

提取内容：
- 修正后的正确表达
- 建议的自然表达
- 专业术语和关键词汇
- 重点短语和句式

分析维度：
- 流利程度评估
- 语法错误识别
- 表达质量分析
- 改进方向建议

使用方法：
1. 打开口语练习笔记
2. 运行 smartCorpusExtractor()
3. 查看智能分析结果
4. 确认创建语料笔记

自动更新：
- 填充"遇到的问题"属性
- 填充"改进点"属性
- 创建对应语料卡片
- 建立笔记间链接
  `
};
