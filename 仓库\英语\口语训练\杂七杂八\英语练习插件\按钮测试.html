<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn.active {
            background: #28a745;
            color: white;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-family: monospace;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>🧪 英语练习插件按钮功能测试</h1>
    
    <div class="test-section">
        <h3>🎭 场景选择测试</h3>
        <button class="btn btn-primary active" onclick="selectScenario('random')">🎲 随机场景</button>
        <button class="btn btn-secondary" onclick="selectScenario('meeting')">🤝 开会场景</button>
        <button class="btn btn-secondary" onclick="selectScenario('compliance')">⚖️ 合规场景</button>
        <button class="btn btn-secondary" onclick="selectScenario('technical')">💻 技术场景</button>
        <button class="btn btn-secondary" onclick="selectScenario('legal')">📜 法律场景</button>
        <div class="status" id="scenarioStatus">当前场景: random</div>
    </div>
    
    <div class="test-section">
        <h3>📊 难度等级测试</h3>
        <button class="btn btn-secondary" onclick="selectDifficulty('基础')">🟢 基础</button>
        <button class="btn btn-primary active" onclick="selectDifficulty('中级')">🟡 中级</button>
        <button class="btn btn-secondary" onclick="selectDifficulty('高级')">🔴 高级</button>
        <div class="status" id="difficultyStatus">当前难度: 中级</div>
    </div>
    
    <div class="test-section">
        <h3>🎯 练习模式测试</h3>
        <button class="btn btn-primary active" onclick="selectMode('template')">📝 模板填空</button>
        <button class="btn btn-secondary" onclick="selectMode('keywords')">🔤 关键词造句</button>
        <button class="btn btn-secondary" onclick="selectMode('scenario')">🎬 情景对话</button>
        <button class="btn btn-secondary" onclick="selectMode('correction')">✏️ 错误纠正</button>
        <div class="status" id="modeStatus">当前模式: template</div>
    </div>
    
    <div class="test-section">
        <h3>🔤 关键词选择测试</h3>
        <button class="btn btn-secondary" onclick="toggleKeywordSelection()">
            <span id="keywordToggleText">🎲 随机关键词</span>
        </button>
        
        <div id="keywordSelection" class="hidden" style="margin-top: 15px;">
            <p>选择关键词类型：</p>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('连接词')">连接词</button>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('时间表达')">时间表达</button>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('义务动词')">义务动词</button>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('技术术语')">技术术语</button>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('法律术语')">法律术语</button>
            <button class="btn btn-secondary" onclick="toggleKeywordCategory('合规术语')">合规术语</button>
            
            <div id="selectedKeywordsDisplay" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; min-height: 40px;">
                <small style="color: #6c757d;">已选择的关键词将显示在这里</small>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>🚀 开始练习测试</h3>
        <button class="btn btn-primary" onclick="startPractice()" style="font-size: 16px; padding: 15px 30px;">
            🚀 开始练习
        </button>
        <div class="status" id="practiceStatus">点击开始练习按钮测试功能</div>
    </div>

    <script>
        // 全局变量
        let currentScenario = 'random';
        let currentDifficulty = '中级';
        let currentMode = 'template';
        let selectedOptionalKeywords = [];
        
        // 可选关键词数据
        const optionalKeywords = {
            "连接词": ["pursuant to", "in accordance with", "therefore"],
            "时间表达": ["within", "by", "immediately"],
            "义务动词": ["shall", "must", "implement"],
            "技术术语": ["encryption", "authentication", "authorization"],
            "法律术语": ["adequacy decisions", "SCCs", "BCRs"],
            "合规术语": ["compliance", "regulatory", "mandatory"]
        };

        // 选择场景
        function selectScenario(scenario) {
            console.log('选择场景:', scenario);
            currentScenario = scenario;
            updateActiveButton('scenario', scenario);
            document.getElementById('scenarioStatus').textContent = `当前场景: ${scenario}`;
        }

        // 选择难度
        function selectDifficulty(difficulty) {
            console.log('选择难度:', difficulty);
            currentDifficulty = difficulty;
            updateActiveButton('difficulty', difficulty);
            document.getElementById('difficultyStatus').textContent = `当前难度: ${difficulty}`;
        }

        // 选择模式
        function selectMode(mode) {
            console.log('选择模式:', mode);
            currentMode = mode;
            updateActiveButton('mode', mode);
            document.getElementById('modeStatus').textContent = `当前模式: ${mode}`;
        }

        // 更新按钮状态
        function updateActiveButton(type, value) {
            let containerSelector = '';
            
            switch(type) {
                case 'scenario':
                    containerSelector = '.test-section:nth-child(2)';
                    break;
                case 'difficulty':
                    containerSelector = '.test-section:nth-child(3)';
                    break;
                case 'mode':
                    containerSelector = '.test-section:nth-child(4)';
                    break;
                default:
                    console.warn('未知的按钮类型:', type);
                    return;
            }
            
            const container = document.querySelector(containerSelector);
            if (!container) {
                console.error('找不到容器:', containerSelector);
                return;
            }
            
            const buttons = container.querySelectorAll('.btn');
            console.log(`${type} - 找到${buttons.length}个按钮`);
            
            buttons.forEach((btn, index) => {
                btn.classList.remove('active');
                
                const btnText = btn.textContent.trim();
                const onclick = btn.getAttribute('onclick') || '';
                
                if (btnText.includes(value) || onclick.includes(`'${value}'`)) {
                    btn.classList.add('active');
                    console.log(`激活${type}按钮: "${btnText}"`);
                }
            });
        }

        // 切换关键词选择
        function toggleKeywordSelection() {
            const keywordSelection = document.getElementById('keywordSelection');
            const toggleText = document.getElementById('keywordToggleText');
            
            if (keywordSelection.classList.contains('hidden')) {
                keywordSelection.classList.remove('hidden');
                toggleText.textContent = '🎯 自选关键词';
            } else {
                keywordSelection.classList.add('hidden');
                toggleText.textContent = '🎲 随机关键词';
                selectedOptionalKeywords = [];
                updateSelectedKeywordsDisplay();
                // 重置所有关键词按钮
                document.querySelectorAll('#keywordSelection .btn').forEach(btn => {
                    btn.classList.remove('active');
                });
            }
        }

        // 切换关键词类别
        function toggleKeywordCategory(category) {
            const button = event.target;
            const keywords = optionalKeywords[category];
            
            if (button.classList.contains('active')) {
                button.classList.remove('active');
                selectedOptionalKeywords = selectedOptionalKeywords.filter(k => !keywords.includes(k));
            } else {
                button.classList.add('active');
                selectedOptionalKeywords = [...new Set([...selectedOptionalKeywords, ...keywords])];
            }
            
            updateSelectedKeywordsDisplay();
        }

        // 更新已选择关键词显示
        function updateSelectedKeywordsDisplay() {
            const display = document.getElementById('selectedKeywordsDisplay');
            
            if (selectedOptionalKeywords.length === 0) {
                display.innerHTML = '<small style="color: #6c757d;">已选择的关键词将显示在这里</small>';
            } else {
                display.innerHTML = selectedOptionalKeywords.map(keyword => 
                    `<span style="background: #007bff; color: white; padding: 4px 8px; margin: 2px; border-radius: 12px; font-size: 12px;">${keyword}</span>`
                ).join('');
            }
        }

        // 开始练习
        function startPractice() {
            const status = document.getElementById('practiceStatus');
            status.innerHTML = `
                <strong>练习设置:</strong><br>
                场景: ${currentScenario}<br>
                难度: ${currentDifficulty}<br>
                模式: ${currentMode}<br>
                关键词: ${selectedOptionalKeywords.length > 0 ? selectedOptionalKeywords.join(', ') : '随机生成'}
            `;
            console.log('开始练习', { currentScenario, currentDifficulty, currentMode, selectedOptionalKeywords });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 按钮测试页面已加载');
        });
    </script>
</body>
</html>
