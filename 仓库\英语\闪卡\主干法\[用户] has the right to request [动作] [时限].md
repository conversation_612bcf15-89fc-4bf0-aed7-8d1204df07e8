---
title: "[用户] has the right to request [动作] [时限]"
correct_expression: "[用户] has the right to request [动作] [时限]"
usage_scenario: "用户权利框架"
common_errors: "混淆主动权利和请求权利"
error_type: "主干法 - 用户权利框架"
tags: ["用户请求权", "时限要求", "隐私权利", "GDPR合规"]
difficulty: "中级"
frequency: "高频"
concept_type: "隐私法必备句型"
用户_options: ["User", "Data Subject", "Employee", "Customer", "Individual", "Person", "Consumer"]
动作_options: ["deletion", "access", "rectification", "restriction", "portability", "information", "correction", "objection", "withdrawal", "complaint"]
时限_options: ["without undue delay", "within 30 days", "within one business day", "upon request", "within 24 hours", "no later than 72 hours", "immediately", "within the fiscal year"]
相关法律: ["GDPR", "CCPA", "HIPAA", "PIPL", "LGPD", "DPA 2018"]
---

# [用户] has the right to request [动作] [时限]

## 中文翻译
[用户]有权在____时限内请求____。

## 使用场景
隐私政策、用户权利条款、GDPR合规文档中的请求权利

## 示例
- User has the right to request deletion without undue delay
- Data Subject has the right to request access within 30 days
- Consumer has the right to request information upon request

## 记忆要点
- request强调用户的主动请求权
- 与直接权利相比，需要用户主动发起
- 时限通常从请求提出时开始计算