async function batchAI(token, promptText, options = {}) {
  const model = options.model || "GLM-4-Flash"; // 智谱清言模型
  const outputFormat = options.format || "markdown";
  const language = options.language || "中文";
  const propertyName = options.property || "AI结果";
  const autoAction = options.action || null; // 可预设动作：append, newfile, frontmatter, copy

  if (!promptText || !token) {
    new Notice("请设置密钥和提示词");
    return;
  }
  
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 获取文件的frontmatter属性
  const frontmatter = app.metadataCache.getFileCache(file)?.frontmatter || {};
  const tags = frontmatter.tags || [];
  
  // 构建文件信息
  let fileInfo = `文件标题：${title}\n`;
  if (tags.length > 0) fileInfo += `文件标签：${tags.join(", ")}\n`;
  if (fileContent) fileInfo += `文件内容：\n${fileContent}\n`;
  
  // 构建最终提示词
  const finalPrompt = `${promptText}\n\n请基于以下文件信息完成任务：\n${fileInfo}\n\n要求：\n- 输出格式：${outputFormat}\n- 使用语言：${language}\n- 请直接输出结果，不需要额外解释`;

  // 显示处理中提示
  new Notice("🤖 AI正在处理中...", 3000);

  try {
    // 调用AI
    var requestOptions = {
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "user",
            content: finalPrompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    };
    
    const response = await obsidian.requestUrl(requestOptions);
    const result = response.json;
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("❌ AI返回结果为空");
      return;
    }

    const content = result.choices[0].message?.content;
    if (!content) {
      new Notice("❌ AI返回内容为空");
      return;
    }
    
    // 如果设置了自动动作，直接执行
    if (autoAction) {
      await executeAction(autoAction, content, file, title, propertyName);
      return content; // 返回结果供批量处理使用
    }
    
    // 否则询问用户如何处理结果
    const action = await showResultModal(content);
    if (action) {
      await executeAction(action, content, file, title, propertyName);
    }
    
    return content;
    
  } catch (error) {
    new Notice(`❌ AI处理失败: ${error.message}`);
    console.error("AI处理错误:", error);
    return null;
  }

  // 执行用户选择的动作
  async function executeAction(action, content, file, title, propertyName) {
    switch (action) {
      case "append":
        const currentContent = await app.vault.read(file);
        const newContent = currentContent + "\n\n## AI处理结果\n\n" + content;
        await app.vault.modify(file, newContent);
        new Notice("✅ 结果已追加到当前文件");
        break;
        
      case "newfile":
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
        const newFileName = `AI处理结果_${timestamp}.md`;
        const newFileContent = `# AI处理结果\n\n**来源文件**: [[${title}]]\n**处理时间**: ${new Date().toLocaleString()}\n**提示词**: ${promptText}\n\n---\n\n${content}`;
        await app.vault.create(newFileName, newFileContent);
        new Notice(`✅ 已创建新文件：${newFileName}`);
        break;
        
      case "frontmatter":
        app.fileManager.processFrontMatter(file, (frontmatter) => {
          frontmatter[propertyName] = content.trim();
        });
        new Notice(`✅ 结果已保存到属性：${propertyName}`);
        break;
        
      case "copy":
        await navigator.clipboard.writeText(content);
        new Notice("✅ 结果已复制到剪贴板");
        break;
    }
  }

  // 显示结果选择模态框
  async function showResultModal(content) {
    return new Promise((resolve) => {
      const modal = new obsidian.Modal(app);
      modal.titleEl.setText("🤖 AI处理完成");
      
      const container = modal.contentEl.createDiv();
      container.createEl("h3", { text: "处理结果预览：" });
      
      const preview = container.createEl("div", {
        attr: { 
          style: "max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 15px; margin: 10px 0; background: #f9f9f9; border-radius: 5px; font-family: monospace; white-space: pre-wrap;" 
        }
      });
      preview.textContent = content;
      
      container.createEl("h4", { text: "请选择如何处理结果：" });
      
      const buttonContainer = container.createDiv();
      buttonContainer.style.display = "grid";
      buttonContainer.style.gridTemplateColumns = "1fr 1fr";
      buttonContainer.style.gap = "10px";
      buttonContainer.style.marginTop = "15px";
      
      const buttons = [
        { text: "📝 追加到当前文件", action: "append", style: "background: #4CAF50; color: white;" },
        { text: "📄 创建新文件", action: "newfile", style: "background: #2196F3; color: white;" },
        { text: "🏷️ 保存到属性", action: "frontmatter", style: "background: #FF9800; color: white;" },
        { text: "📋 复制到剪贴板", action: "copy", style: "background: #9C27B0; color: white;" }
      ];
      
      buttons.forEach(btn => {
        const button = buttonContainer.createEl("button", { text: btn.text });
        button.style.cssText = btn.style + " padding: 10px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;";
        button.onclick = () => {
          modal.close();
          resolve(btn.action);
        };
      });
      
      const cancelContainer = container.createDiv();
      cancelContainer.style.textAlign = "center";
      cancelContainer.style.marginTop = "15px";
      
      const cancelBtn = cancelContainer.createEl("button", { text: "❌ 取消" });
      cancelBtn.style.cssText = "background: #f44336; color: white; padding: 8px 20px; border: none; border-radius: 5px; cursor: pointer;";
      cancelBtn.onclick = () => {
        modal.close();
        resolve(null);
      };
      
      modal.open();
    });
  }
}

// 批量处理函数
async function batchProcessFiles(token, promptText, fileList, options = {}) {
  const results = [];
  const delay = options.delay || 1000; // 默认延迟1秒
  
  new Notice(`🚀 开始批量处理 ${fileList.length} 个文件...`);
  
  for (let i = 0; i < fileList.length; i++) {
    const file = fileList[i];
    new Notice(`📝 处理中 (${i + 1}/${fileList.length}): ${file.basename}`);
    
    try {
      // 临时设置当前文件
      const originalFile = this.currentFile;
      this.currentFile = file;
      
      // 处理文件
      const result = await batchAI.call(this, token, promptText, { ...options, action: "frontmatter" });
      
      results.push({
        file: file.basename,
        success: true,
        result: result
      });
      
      // 恢复原始文件
      this.currentFile = originalFile;
      
      // 延迟避免API限制
      if (i < fileList.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
    } catch (error) {
      results.push({
        file: file.basename,
        success: false,
        error: error.message
      });
    }
  }
  
  // 显示批量处理结果
  const successCount = results.filter(r => r.success).length;
  const failCount = results.length - successCount;
  
  new Notice(`✅ 批量处理完成！成功: ${successCount}, 失败: ${failCount}`);
  
  return results;
}

// 预设提示词模板
const promptTemplates = {
  summarize: "请总结以下内容的核心要点，用简洁明了的语言概括主要信息",
  translate: "请将以下内容翻译成英文，保持原意和语调",
  rewrite: "请改写以下内容，使其更加生动有趣，同时保持原意",
  seo: "请为以下内容生成SEO友好的标题和描述，包含关键词",
  outline: "请为以下内容生成详细的大纲结构",
  keywords: "请提取以下内容的关键词和标签",
  questions: "请基于以下内容生成5个深度思考问题",
  action: "请基于以下内容生成具体的行动计划和步骤"
};

// 快捷函数
async function quickSummarize(token) {
  return await batchAI.call(this, token, promptTemplates.summarize, { action: "frontmatter", property: "摘要" });
}

async function quickTranslate(token) {
  return await batchAI.call(this, token, promptTemplates.translate, { action: "frontmatter", property: "英文翻译" });
}

async function quickRewrite(token) {
  return await batchAI.call(this, token, promptTemplates.rewrite, { action: "newfile" });
}

// 使用示例：
// batchAI("你的密钥", "请总结这篇文章的要点");
// batchAI("你的密钥", "翻译成英文", { format: "text", language: "English", action: "copy" });
// quickSummarize("你的密钥");

exports.default = {
  entry: batchAI,
  name: "batchAI",
  description: `批量AI助手 - 支持预设提示词的智谱清言API工具

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==

  🚀 主要改进：
  - ✅ 直接传入提示词，无需每次手动输入
  - ✅ 支持批量处理多个文件
  - ✅ 预设常用模板（总结、翻译、改写等）
  - ✅ 可预设输出动作，实现全自动处理
  - ✅ 更好的错误处理和进度提示

  📖 使用方法：
  \`batchAI('你的密钥', '提示词')\`
  \`batchAI('你的密钥', '提示词', { format: 'text', action: 'copy' })\`

  🎯 快捷函数：
  - \`quickSummarize('密钥')\` - 快速总结
  - \`quickTranslate('密钥')\` - 快速翻译
  - \`quickRewrite('密钥')\` - 快速改写

  ⚙️ 选项参数：
  - model: AI模型 (默认: GLM-4-Flash)
  - format: 输出格式 (markdown/text)
  - language: 输出语言 (中文/English)
  - property: 属性名 (默认: AI结果)
  - action: 自动动作 (append/newfile/frontmatter/copy)

  💡 使用场景：
  - 批量总结多篇文章
  - 批量翻译文档
  - 批量生成SEO标题
  - 批量提取关键词
  - 任何需要重复AI处理的任务
  `,
};
