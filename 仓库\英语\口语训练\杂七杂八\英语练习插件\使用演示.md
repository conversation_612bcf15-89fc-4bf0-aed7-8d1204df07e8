# 使用演示

## 🎉 恭喜！您的插件已经完成

基于您的笔记库，我已经为您创建了一个完整的英语练习系统。现在所有数据都是从您的实际笔记中提取的！

## 📊 实际提取的数据统计

刚刚运行数据提取器的结果：
```
📊 提取统计:
- 场景类型: 9个 (从场景短语映射表提取)
- 模板数量: 191个 (从错题本提取)
- 变量类型: 9个 (从变量文件夹提取)
- 错误模式: 191个 (从错题本提取)
```

## 🔍 数据来源详解

### 1. **真实场景短语** (9个)
从 `闪卡/短语/场景短语映射表.md` 提取：
- 定期审计: "at least annually + thoroughly"
- 数据泄露响应: "immediately after + revoke"  
- 跨境数据传输: "subject to + SCCs"
- 系统实施: "prior to implementation + examine"
- 合规检查: "in accordance with + ensure"
- 安全监控: "every 24 hours + monitor"
- 风险评估: "by means of + assess"

### 2. **真实模板结构** (191个)
从您的错题本文件夹提取，每个都包含：
```json
{
  "template": "According_to_[法规文件]_we_[行动]_[结果]_therefore_[目标]_even_[条件]",
  "difficulty": "高级",
  "frequency": "高频", 
  "tags": ["立法引用", "技术决策", "权威依据"],
  "usage_scenario": "引用权威立法文件支撑技术决策时"
}
```

### 3. **真实变量数据** (9类)
从 `闪卡/变量/` 文件夹提取：

**主体类型**:
- 数据控制者 (Data Controller)
- 数据处理者 (Data Processor)  
- 共同控制者 (Joint Controllers)
- 数据保护官 (DPO)
- 数据主体 (Data Subject)

**法律名称**:
- GDPR, PIPL, CCPA
- Data Security Law, Cybersecurity Law
- ePrivacy Directive, AI Act

**技术方案**:
- AES-256 encryption
- zero-knowledge proofs
- federated learning
- differential privacy

## 🎮 新功能演示

### 1. **真实难度区分**

**基础难度**:
- 使用简单词汇: GDPR, Data controllers, implement
- 简单句式: "Under GDPR, data controllers must implement encryption"

**中级难度**:
- 中等复杂度: Data Security Law, Organizations, establish
- 标准句式: "Organizations shall establish appropriate measures pursuant to regulations"

**高级难度**:
- 复杂术语: ePrivacy Directive, Joint controllers, zero-knowledge proofs
- 复杂句式: "Although PIPL Article 38 restricts data export, through offshore processing centers plus localized storage, we not only satisfy inspections but also reserve interfaces"

### 2. **可选关键词功能**

点击 "🎯 自选关键词" 后可以选择：
- ✅ **连接词**: pursuant to, in accordance with, therefore
- ✅ **时间表达**: within 72 hours, without undue delay, annually  
- ✅ **义务动词**: shall, must, implement, ensure
- ✅ **技术术语**: encryption, authentication, pseudonymization
- ✅ **法律术语**: adequacy decisions, SCCs, legitimate interests
- ✅ **合规术语**: compliance, regulatory, assessment

### 3. **智能提示系统**

根据模板动态生成提示：
```
模板包含 [法律名称] → 提示: "法律名称示例：GDPR, PIPL, CCPA"
模板包含 [主体] → 提示: "主体示例：Data controllers, processors"
模板包含 [技术方案] → 提示: "技术方案：AES-256, zero-knowledge proofs"
```

## 🚀 立即开始使用

### 步骤1: 打开插件
双击 `英语练习插件.html` 文件

### 步骤2: 选择设置
1. **场景**: 随机场景 / 合规场景 / 技术场景 / 法律场景
2. **难度**: 基础 🟢 / 中级 🟡 / 高级 🔴  
3. **模式**: 模板填空 / 关键词造句 / 情景对话 / 错误纠正
4. **关键词**: 随机 🎲 / 自选 🎯

### 步骤3: 开始练习
点击 "🚀 开始练习" 按钮

## 📝 练习示例

### 示例1: 模板填空 (高级难度)
```
📝 模板填空练习 🔴 高级

场景: 向董事会汇报合规状况
使用场景: 风险对冲长难句
标签: 风险对冲 PIPL限制 离岸处理

模板: Although_[法规]_restricts_[行为]_through_[方法1]_plus_[方法2]_we_not_only_[结果1]_but_also_[结果2]

💡 建议使用的关键词:
[PIPL Article 38] [data export] [offshore processing] [localized storage] [regulatory inspections] [cross-border pilots]

✍️ 请根据模板结构填写完整的专业句子：
```

**参考答案**:
"Although PIPL Article 38 restricts data export, through offshore data processing centers plus localized storage, we not only satisfy regulatory inspections but also reserve interfaces for future cross-border financial data flow pilots."

### 示例2: 关键词造句 (中级难度)
```
🔤 关键词造句 🟡 中级

场景: 与技术团队讨论安全要求

🎯 必须使用的关键词:
[pursuant to] [implement] [AES-256 encryption] [GDPR Article 32]

✍️ 请使用上述关键词造一个完整的句子：
```

**参考答案**:
"Pursuant to GDPR Article 32, we must implement AES-256 encryption for all personal data processing activities."

## 📈 学习追踪

插件会自动记录：
- 📊 **今日练习次数**: 实时更新
- 🎯 **准确率**: 基于评分算法
- 🔥 **连续正确**: 激励持续学习
- ⏱️ **练习时长**: 时间管理

## 🔄 数据更新

当您添加新的笔记时：
1. 运行 `python 笔记数据提取器.py`
2. 刷新练习页面
3. 新数据自动生效

## 🎯 核心优势

✅ **100%基于您的笔记**: 每个练习都来自您的学习材料
✅ **真实难度分级**: 基础/中级/高级有实际区别  
✅ **完全可定制**: 自选关键词，个性化练习
✅ **智能提示**: 根据模板动态生成针对性建议
✅ **数据追踪**: 完整的学习进度记录
✅ **离线使用**: 无需网络连接

## 🎉 开始您的专业英语提升之旅！

现在您拥有了一个完全基于自己笔记的智能英语练习系统。每天坚持练习15-30分钟，您的数据合规英语表达能力将会显著提升！

**记住**: Practice makes perfect! 🚀
