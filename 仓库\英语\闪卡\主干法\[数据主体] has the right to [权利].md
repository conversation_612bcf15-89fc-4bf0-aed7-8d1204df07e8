---
title: "[数据主体] has the right to [权利]"
correct_expression: "[数据主体] has the right to [权利]"
usage_scenario: "用户权利框架"
common_errors: "权利范围描述不准确或遗漏重要权利"
error_type: "主干法 - 用户权利框架"
tags: ["数据主体权利", "GDPR权利", "隐私权利", "用户保护"]
difficulty: "中级"
frequency: "高频"
concept_type: "隐私法必备句型"
数据主体_options: ["Data Subject", "Individual", "Person", "User", "Customer", "Employee", "Consumer", "Participant", "Stakeholder", "Natural Person"]
权利_options: ["access", "rectification", "erasure", "restriction of processing", "data portability", "object to processing", "withdraw consent", "not to be subject to automated decision-making", "information", "complaint", "compensation"]
GDPR权利: ["Right of access (Article 15)", "Right to rectification (Article 16)", "Right to erasure (Article 17)", "Right to restriction (Article 18)", "Right to data portability (Article 20)", "Right to object (Article 21)"]
法律基础: ["GDPR Chapter III", "CCPA Consumer Rights", "PIPL Individual Rights"]
相关法律: ["GDPR", "CCPA", "HIPAA", "PIPL", "LGPD", "DPA 2018"]
---

# [数据主体] has the right to [权利]

## 中文翻译
[数据主体]有权____。

## 使用场景
GDPR合规、隐私政策、用户权利声明中的基本权利表述

## 示例
- Data Subject has the right to access
- Individual has the right to rectification
- Person has the right to erasure

## 记忆要点
- GDPR Chapter III规定的基本权利
- 数据主体是GDPR的核心概念
- 权利表述必须准确且完整