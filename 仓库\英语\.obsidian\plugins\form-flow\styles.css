.form--PoupupContainer{max-width:500px;max-height:500px;overflow-y:auto;box-shadow:var(--form-shadow);background-color:var(--background-primary);padding:8px 16px;user-select:text;white-space:pre-wrap;border-radius:var(--radius-m)}.form--SelectionPopup{z-index:9999;position:relative;padding:8px 16px;background-color:var(--background-primary)}.form--SelectionPopupBody{padding:0 16px 8px;min-height:32px}.form--SelectionPopupContent{line-height:1.5;letter-spacing:.02em}button.form--SelectionPopupCopyButton{position:absolute;top:4px;right:4px;opacity:0;cursor:pointer}.form--SelectionPopup:hover .form--SelectionPopupCopyButton{opacity:1;transition:opacity .2s ease-in-out}.form--Toast{display:flex;align-items:flex-start;justify-content:space-between;gap:.5rem;padding:.75rem;background-color:var(--background-primary);color:var(--text-normal);font-size:var(--font-ui-small);border-radius:var(--radius-m);box-shadow:var(--form-shadow);z-index:1000;position:fixed;top:calc(var(--safe-area-inset-top) + 1rem);left:50%;transform:translate(-50%) translateY(-10px);transition:opacity .3s ease-in-out,transform .3s ease-in-out;min-width:320px;max-width:90vw;border:1px solid var(--background-modifier-border);opacity:0}.form--Toast.visible{opacity:1;transform:translate(-50%) translateY(0)}.form--Toast__content{flex:1;display:flex;align-items:center;gap:.75rem}.form--Toast__iconContainer{display:flex;align-items:center;justify-content:center;flex-shrink:0;height:20px}.form--Toast__typeIcon{width:20px;height:20px;flex-shrink:0;display:block}.form--Toast__message{flex:1;display:flex;flex-direction:column;justify-content:center;min-height:20px}.form--Toast__message strong{margin:0;display:block;line-height:1.4}.form--Toast__message p{margin:.25rem 0 0;font-size:.9em;color:var(--text-muted);line-height:1.4;word-break:break-word}.form--Toast__spinner{animation:form--Toast__spin 1.5s linear infinite}@keyframes form--Toast__spin{to{transform:rotate(360deg)}}.form--Toast.success .form--Toast__typeIcon{color:var(--color-green)}.form--Toast.error .form--Toast__typeIcon{color:var(--color-red)}.form--Toast.info .form--Toast__typeIcon{color:var(--text-accent)}.form--Toast.warning .form--Toast__typeIcon{color:var(--color-orange)}.form--Toast.loading .form--Toast__typeIcon{color:var(--text-accent)}button.form--Toast__close{background:transparent;border:none;padding:0;margin-left:.5rem;cursor:pointer;width:20px;height:20px;display:flex;align-items:center;justify-content:center;border-radius:var(--radius-s);color:var(--text-muted);transition:background-color .2s ease;box-shadow:none}button.form--Toast__close:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}@media (max-width: 768px){.form--Toast{min-width:80%;max-width:92%;padding:.625rem .75rem}}.form--CpsFormPreviewBody{margin-bottom:1rem}.form--CpsFormPreviewFooter{display:flex;flex-direction:column;align-items:flex-end;gap:.6rem;margin-bottom:.5rem}.form--CpsFormSubmitButton{min-width:120px}.form--CpsFormSubmitButtonKey{opacity:.8;margin-left:8px}button.form--CpsFormSubmitButton .form--loadingSpinner{color:var(--text-on-accent)}button.form--FormTypeSelectButton{--button-background-color: var(--color-base-20);box-shadow:none;background-color:transparent;border:none;outline:none;cursor:pointer;padding:3px 6px;gap:4px;height:auto;font-size:var(--font-ui-small);background-color:var(--select-button-background-color, --color-base-20);color:var(--select-button-text-color, --text-muted)}button.form--FormTypeSelectButton .form--FormTypeSelectButtonLabel{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}button.form--FormTypeSelectButton .lucide,.form--FormTypeSelectOption .lucide{width:16px;height:16px}@media (max-width: 600px){button.form--FormTypeSelectButton .form--FormTypeSelectButtonLabel{display:none}}button.form--FormTypeSelectButton:hover{background-color:var(--background-modifier-hover);color:var(--text-accent)}button.form--FormTypeSelectButton:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--FormTypeSelectOptions{display:flex;flex-direction:column;gap:4px;padding:.5rem;background-color:var(--background-primary);box-shadow:var(--form-shadow);font-size:var(--font-ui-small);z-index:var(--form--modal-layer);width:max(var(--radix-popover-trigger-width),220px);height:min(var(--radix-popover-content-available-height),300px);overflow-y:auto;border-radius:var(--radius-m)}.form--FormTypeSelectOption{display:flex;align-items:center;gap:.5rem;padding:4px 8px;border-radius:var(--radius-m);cursor:pointer}.form--FormTypeSelectOption[data-highlighted]{background-color:var(--background-modifier-hover)}.form--FormTypeSelectOption[data-selected=true]{color:var(--text-accent)}.form--FormTypeSelectOption:hover{background-color:var(--background-modifier-hover)}.form--ActionFlow{display:flex;flex-direction:column;align-items:center;padding:1.5rem 1rem;margin:.5rem 0;background-color:var(--background-secondary);border-radius:8px;width:100%}.form--ActionFlowStartNode .form--ActionFlowNodeContent{background-color:var(--interactive-accent);color:var(--text-on-accent);padding:.5rem 1rem;border-radius:16px;font-weight:500;font-size:.9rem;margin-bottom:.5rem}.form--ActionFlowNodes{display:flex;flex-direction:column;align-items:center;width:100%;max-width:320px}.form--ActionFlowNode{display:flex;flex-direction:column;align-items:center;width:100%;margin-bottom:.5rem}.form--ActionFlowNodeContent{background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:8px;padding:1rem;width:100%;box-shadow:0 2px 4px #0000000d}.form--ActionFlowNodeType{font-weight:600;font-size:.9rem;margin-bottom:.25rem;color:var(--text-normal)}.form--ActionFlowNodeTitle{font-size:.85rem;color:var(--text-muted);word-break:break-word}.form--ActionFlowArrow{display:flex;align-items:center;justify-content:center;height:24px;color:var(--text-muted);margin:.25rem 0}.form--ActionFlowEmpty{display:flex;align-items:center;justify-content:center;color:var(--text-muted);font-style:italic;padding:2rem;text-align:center}.form--MultipleCombobox{position:relative;width:100%;min-width:180px;font-size:var(--font-ui-small);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);background-color:var(--background-primary);cursor:pointer;transition:border-color .15s ease,box-shadow .15s ease}.form--MultipleComboboxChevron{flex-shrink:0;color:var(--text-muted);margin-left:4px;transition:transform .2s ease}.form--MultipleComboboxChevron-open{transform:rotate(180deg)}.form--MultipleComboboxContent{width:max(var(--radix-popover-trigger-width),300px);max-height:var(--combobox-max-height, 400px);overflow:hidden;background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);box-shadow:var(--shadow-s);animation-duration:.2s;z-index:var(--form--modal-layer)}.form--MultipleComboboxTrigger{display:flex;align-items:center;justify-content:space-between;padding:0 6px;width:100%;min-height:var(--input-height);color:var(--text-color);background-color:var(--interactive-normal);box-shadow:var(--input-shadow);border:none;border-radius:var(--radius-s)}.form--MultipleComboboxTrigger:hover{box-shadow:var(--input-shadow-hover);background-color:var(--dropdown-background-hover)}.form--MultipleComboboxTrigger:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:1px;box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--MultipleComboboxHeader{border-bottom:1px solid var(--background-modifier-border);background-color:var(--color-base-10)}.form--MultipleComboboxChips{padding:0 4px;min-height:var(--input-height);display:flex;flex-wrap:wrap;align-items:center;gap:4px}.form--MultipleComboboxChip{font-size:var(--font-ui-smaller);background-color:hsl(var(--interactive-accent-hsl),.1);color:hsl(var(--interactive-accent-hsl),1);padding:0 4px;border-radius:var(--radius-m);display:inline-flex;align-items:center;height:24px;user-select:none}button.form--MultipleComboboxChipButton{all:unset;background-color:transparent;box-shadow:none;border:none;padding:0;width:16px;height:16px;margin-left:2px;color:var(--text-faint);cursor:pointer;display:inline-flex;align-items:center;justify-content:center;opacity:1}button.form--MultipleComboboxChipButton:hover{background-color:rgba(var(--color-red-rgb),.1);color:rgba(var(--color-red-rgb),.85);border-radius:4px}input.form--MultipleComboboxInput{all:unset;background-color:transparent;color:var(--text-normal);font-size:var(--font-ui-small);padding:2px 4px;flex:1;min-width:40px;min-height:34px}input.form--MultipleComboboxInput:focus{outline:none;box-shadow:none}.form--MultipleComboboxOptions{max-height:300px;overflow-y:auto;padding:4px}.form--MultipleComboboxOption{display:flex;flex-direction:column;padding:6px 8px;border-radius:var(--radius-s);cursor:pointer;user-select:none}.form--MultipleComboboxOption:hover,.form--MultipleComboboxOption[data-active=true]{background-color:var(--background-modifier-hover)}.form--MultipleComboboxOptionLabel{display:flex;align-items:center;gap:8px;color:var(--text-normal)}.form--MultipleComboboxOptionCheckbox{color:var(--interactive-accent);flex-shrink:0;display:flex;align-items:center}.form--MultipleComboboxOptionIcon{display:inline-flex;align-items:center;justify-content:center;margin-right:2px;color:var(--text-muted)}.form--MultipleComboboxPlaceholder{color:var(--text-faint);padding-left:4px}.form--MultipleComboboxNoResults{padding:4px 12px;text-align:center;color:var(--text-muted);font-size:var(--font-ui-small)}.form--MultipleComboboxCreatePrompt{font-size:var(--font-ui-smaller);color:var(--interactive-accent);font-style:italic;display:inline-block;margin-top:4px}.form--Combobox{display:flex;flex-direction:column;gap:var(--size-4-1);width:100%}.form--ComboboxInput{width:100%;height:var(--input-height);padding:var(--size-4-1) var(--size-4-2);background-color:var(--background-secondary);color:var(--text-normal);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);font-size:var(--font-ui-small)}.form--ComboboxInput:focus{box-shadow:0 0 0 2px var(--background-modifier-border-focus);border-color:var(--interactive-accent);outline:none}.form--ComboboxInput:disabled{opacity:.6;cursor:not-allowed;background-color:var(--background-secondary-alt)}.form--ComboboxContent{z-index:var(--form--modal-layer);width:var(--radix-popover-trigger-width);background-color:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);overflow:hidden;box-shadow:var(--shadow-s);max-height:var(--combobox-max-height, 400px)}.form--ComboboxOptionsList{scrollbar-width:thin;scrollbar-color:var(--scrollbar-thumb-bg) var(--scrollbar-bg);max-height:300px;overflow-y:auto;padding:4px}.form--ComboboxOptionsList::-webkit-scrollbar{width:8px}.form--ComboboxOptionsList::-webkit-scrollbar-track{background:var(--scrollbar-bg)}.form--ComboboxOptionsList::-webkit-scrollbar-thumb{background-color:var(--scrollbar-thumb-bg);border-radius:4px}.form--ComboboxOption{display:flex;align-items:center;flex-direction:column;padding:var(--size-4-1) var(--size-4-2);cursor:pointer;border-radius:var(--radius-xs);user-select:none;height:32px;max-height:32px;overflow:hidden}.form--ComboboxOption:hover,.form--ComboboxOption[data-active=true]{background-color:var(--background-modifier-hover)}.form--ComboboxOptionLabel{display:flex;align-items:center;gap:var(--size-4-1);width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.form--ComboboxOptionIcon{display:flex;align-items:center;justify-content:center;flex-shrink:0}.form--PasswordInputContainer{display:flex;align-items:center;flex-wrap:nowrap;gap:8px;-webkit-app-region:no-drag;background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);color:var(--text-normal);font-family:inherit;border-radius:var(--input-radius);outline:none}.form--PasswordInputContainer[data-focused=true]{border-color:var(--background-modifier-border-focus);transition:box-shadow .15s ease-in-out,border .15s ease-in-out}.form--PasswordInputContainer[data-focused=true]:focus-visible,.form--PasswordInputContainer[data-focused=true]{box-shadow:0 0 0 2px var(--background-modifier-border-focus)}.form--PasswordInputContainer .form--PasswordInput{background-color:transparent;flex:1;border:none;outline:none;box-shadow:none;height:var(--input-height)}.form--PasswordInputContainer .form--PasswordInput:focus-visible{box-shadow:none}button.form--PasswordToggle{all:unset;display:inline-flex;align-items:center;justify-content:center;border-radius:var(--radius-m);cursor:pointer;user-select:none;padding:2px 4px;color:var(--text-faint)}.form--PasswordToggleIcon{display:flex;align-items:center;justify-content:center;width:16px;height:16px}@media (any-hover: hover){button.form--PasswordToggle:hover{background-color:var(--background-modifier-hover);color:var(--text-muted)}}button.form--PasswordToggle:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}button.form--FileListControlTrigger{display:flex;align-items:center;justify-content:flex-start;gap:4px;padding:4px 6px;position:relative;overflow:hidden;width:100%;height:auto;min-height:30px}.form--FileListControlTriggerArrow{width:20px;min-width:20px;color:var(--text-faint)}.form--FileListControlTriggerItems{display:flex;align-items:center;justify-content:flex-start;gap:4px;flex-grow:1;overflow:hidden;flex-wrap:wrap}.form--FileListControlTriggerItem{display:flex;align-items:center;justify-content:space-between;gap:2px;border-radius:var(--radius-s);font-size:var(--font-ui-smaller);padding:2px;color:var(--text-accent);background-color:hsl(var(--interactive-accent-hsl),.15)}.form--FileListControlTriggerItemClose{border-radius:var(--radius-s);padding:2px 4px;color:var(--text-faint);cursor:pointer;display:flex;align-items:center}.form--FileListControlTriggerItemClose:hover{color:rgba(var(--color-red-rgb),1);background-color:rgba(var(--color-red-rgb),.1)}.form--FileListControlContent{display:flex;flex-direction:column;z-index:var(--form--modal-layer);background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--shadow-s);padding:8px 12px;animation:fadeIn .2s ease-out;overflow-y:auto;width:max(var(--radix-popover-trigger-width),280px);max-height:min(360px,var(--radix-popover-content-available-height))}input.form--FileListControlContentInput{width:100%;box-shadow:none;border:none;padding:8px}input.form--FileListControlContentInput:focus-visible{box-shadow:none}.form--FileListControlContentList{flex:1;overflow-y:auto;padding:8px 4px;gap:4px}.form--FileListControlContentItem{padding:var(--size-4-1) var(--size-4-2);cursor:pointer;color:var(--text-normal);font-size:var(--font-ui-small);border-radius:var(--input-radius);margin-bottom:4px}.form--FileListControlContentItem:hover,.form--FileListControlContentItem[data-highlighted=true]{background-color:var(--background-modifier-hover);color:var(--text-normal)}.form--FileListControlContentTip{padding:4px;font-size:var(--font-ui-small);color:var(--text-muted)}.form--FileListControlContentTipInfo{background-color:rgba(var(--color-green-rgb),.1);color:rgba(var(--color-green-rgb),.85);padding:4px;border-radius:var(--radius-s)}.form--RadioSelect{display:flex;gap:1rem;font-size:var(--font-ui-small);flex-wrap:wrap}.form--RadioSelect .form--RadioSelectOption input[type=radio]{margin:0}.form--RadioSelectOption{display:flex;gap:.5rem;align-items:center;padding:2px 4px;border-radius:4px;transition:all .2s ease}.form--RadioSelectOption:has(input:focus-visible){outline:2px solid hsl(var(--interactive-accent-hsl),1);outline-offset:2px;background-color:hsl(var(--interactive-accent-hsl),.2)}.form--RadioSelectOption:hover{background-color:hsl(var(--interactive-accent-hsl),.1)}.form--RadioSelectOption input:checked+span{font-weight:500}.form--ListBoxContainer{position:relative;width:100%}.form--ListBox{width:100%;font-size:var(--font-ui-small)}.form--ListBoxButton{display:flex;align-items:center;justify-content:space-between;min-width:150px;padding:2px 28px 2px 8px;background-color:var(--background-secondary);border:none;border-radius:var(--radius-s);color:var(--text-normal);cursor:default;text-align:left;position:relative;height:auto;min-height:30px;overflow:hidden;width:100%}.form--ListBoxButton .lucide{color:var(--text-muted);transition:transform .2s ease-in-out;position:absolute;right:8px;top:50%;transform:translateY(-50%);flex-shrink:0}.form--ListBoxButton:hover{box-shadow:var(--input-shadow-hover);background-color:var(--dropdown-background-hover)}.form--ListBoxButton:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:1px}.form--ListBoxOptionsHeader{display:flex;align-items:center;padding:var(--size-4-2);background-color:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border);color:var(--text-muted);gap:4px}button.form--ListBoxOptionsHeaderButton{all:unset;display:flex;align-items:center;justify-content:center;background:transparent;cursor:pointer;transition:all .2s ease;box-shadow:none;border:none;padding:var(--size-2-2) var(--size-2-2);border-radius:var(--radius-s)}button.form--ListBoxOptionsHeaderButton:hover{background-color:var(--background-modifier-hover)}.form--ListBoxOptionsHeaderLabel{font-size:var(--font-ui-small);color:var(--text-faint)}.form--ListBoxOptions{position:absolute;z-index:var(--form--modal-layer);margin-top:4px;max-height:300px;min-width:160px;width:var(--radix-dropdown-menu-trigger-width);overflow-y:auto;border-radius:var(--radius-s);box-shadow:var(--shadow-s);background-color:var(--background-primary);border:1px solid var(--background-modifier-border)}.form--ListBoxOption{display:flex;align-items:center;padding:8px 12px;cursor:pointer;color:var(--text-normal);outline:none}.form--ListBoxOption:hover{background-color:var(--background-modifier-hover)}.form--ListBoxOption[data-selected=true]{background-color:var(--background-modifier-hover-hover)}.form--ListBoxOption[data-highlighted]{background-color:var(--background-modifier-hover);color:var(--text-normal);font-weight:500}.form--ListBoxOptionContent{display:flex;align-items:center}.form--ListBoxOptionIcon{display:inline-flex;align-items:center;margin-right:8px;color:var(--text-muted)}.form--ListBoxOption[data-selected=true],.form--ListBoxOption[data-selected=true] .form--ListBoxOptionIcon{color:var(--interactive-accent)}.form--ListBoxSelections{display:flex;flex-wrap:wrap;gap:4px;max-width:calc(100% - 24px);align-items:center;padding:4px 0;overflow:visible}.form--ListBoxTag{display:inline-flex;padding:2px 6px;font-size:var(--font-ui-smaller);background-color:var(--color-base-20);color:hsl(var(--interactive-accent-hsl),1);border-radius:var(--radius-s);white-space:nowrap;max-width:120px;overflow:hidden;text-overflow:ellipsis;font-weight:500;flex-shrink:0;margin:1px}.form--ListBoxMoreCount{display:inline-flex;align-items:center;justify-content:center;padding:1px 6px;font-size:var(--font-ui-smaller);color:var(--text-muted);white-space:nowrap}.form--ListBoxPlaceholder{color:var(--text-muted);font-size:var(--font-ui-small)}.form--ListBoxBulkActions{display:none}.form--ListBoxSelectionSummary{display:flex;align-items:center;gap:4px;padding:4px 8px;font-size:var(--font-ui-smaller);color:var(--text-muted);background-color:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border)}button.form--ListBoxToggleAll{background:none;border:none;outline:none;box-shadow:none;padding:4px;margin:0;cursor:pointer;display:inline-flex;align-items:center;justify-content:center;color:var(--interactive-accent);border-radius:var(--radius-s)}button.form--ListBoxToggleAll:hover{background-color:var(--background-modifier-hover)}.form--ListBoxToggleAll:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:1px}.form--ListBox[disabled] .form--ListBoxButton,.form--ListBox[disabled] .form--ListBoxButton:hover{opacity:.6;cursor:not-allowed;background-color:var(--background-modifier-form-field)}.form--ListBoxSelectionSummary{display:flex}.form--CpsForm{display:flex;flex-direction:column}.form--CpsForm[data-layout=vertical]{display:flex;flex-direction:column;gap:1rem}input[type=time]{-webkit-app-region:no-drag;background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);color:var(--text-normal);font-family:inherit;padding:var(--size-4-1) var(--size-4-2);font-size:var(--font-ui-small);border-radius:var(--input-radius);outline:none}.form--CpsFormItemInfo{flex:1 1 auto}.form--CpsFormItemInfoName{color:var(--text-normal);font-size:var(--font-ui-small);line-height:1.8}.form--CpsFormItemInfoDescription{color:var(--text-muted);font-size:var(--font-ui-smaller);line-height:var(--line-height-tight);margin-bottom:.5rem;margin-top:.5rem}.form--CpsFormItemInfoName[data-required=true]:after{content:"*";color:var(--text-error);margin-left:4px}.form--CpsFormItemControl{display:flex;gap:.5rem;flex:1}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem{display:flex;align-items:center;flex-direction:row;padding:.5em 0}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem>.form--CpsFormItemControl{justify-content:flex-end}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem>.form--CpsFormItemControl>*:first-child{flex-grow:0}.form--CpsForm[data-layout=vertical]>.form--CpsFormItem{display:flex;flex-direction:column;align-items:initial}.form--CpsForm[data-layout=vertical]>.form--CpsFormItem>.form--CpsFormItemControl{justify-content:flex-start}.form--CpsForm[data-layout=vertical]>.form--CpsFormItem>.form--CpsFormItemControl>*:first-child{flex-grow:1}.form--CpsForm[data-layout=vertical]>.form--CpsFormItem>.form--CpsFormItemControl>input[type=checkbox]{flex-grow:0}@media screen and (max-width: 768px){.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem{display:flex;flex-direction:column;align-items:initial}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem>.form--CpsFormItemControl{justify-content:flex-start;width:100%}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem>.form--CpsFormItemControl>*:first-child{flex-grow:1}.form--CpsForm[data-layout=horizontal]>.form--CpsFormItem>.form--CpsFormItemControl>input[type=checkbox]{flex-grow:0}}.form--CpsFormButtonLoading{display:inline-flex;align-items:center;justify-content:center;gap:4px}.form--CpsFormButtonLoadingDot{width:4px;height:4px;border-radius:50%;background-color:var(--text-on-accent);opacity:0;animation:dotPulse 1.4s infinite ease-in-out}.form--CpsFormButtonLoadingDot:nth-child(1){animation-delay:0s}.form--CpsFormButtonLoadingDot:nth-child(2){animation-delay:.2s}.form--CpsFormButtonLoadingDot:nth-child(3){animation-delay:.4s}@keyframes dotPulse{0%,80%,to{opacity:0;transform:scale(.8)}40%{opacity:1;transform:scale(1)}}.form--CpsFormButtonLoadingDot:first-child{animation-delay:0s}.form--CpsFormButtonLoadingDot:last-child{animation-delay:.75s}.form--CpsFormButtonLoadingLine{flex:1;height:2px;margin:0 6px;background-color:var(--text-on-accent);position:relative;overflow:hidden}.form--CpsFormButtonLoadingLine:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(90deg,transparent 0%,rgba(255,255,255,.8) 50%,transparent 100%);animation:linePulse 1.5s infinite ease-in-out}@keyframes dotPulse{0%,to{transform:scale(1);opacity:.7}50%{transform:scale(1.5);opacity:1}}@keyframes linePulse{0%{transform:translate(-100%)}to{transform:translate(100%)}}.form--CpsFormButtonLoadingRing{position:absolute;width:100%;height:100%;border:2px solid var(--text-on-accent);border-radius:50%;opacity:0;animation:ringPulse 1.5s infinite ease-out}.form--CpsFormButtonLoadingRing:nth-child(1){animation-delay:0s}.form--CpsFormButtonLoadingRing:nth-child(2){animation-delay:.5s}.form--CpsFormButtonLoadingRing:nth-child(3){animation-delay:1s}.form--CpsFormButtonLoadingCore{width:30%;height:30%;background-color:var(--text-on-accent);border-radius:50%;animation:corePulse 1.5s infinite ease-in-out}@keyframes ringPulse{0%{transform:scale(.3);opacity:.8}to{transform:scale(1);opacity:0}}@keyframes corePulse{0%,to{transform:scale(1)}50%{transform:scale(.8)}}.form--CalloutBlock{display:flex;flex-direction:column;gap:var(--size-4-3);padding:var(--size-4-4) var(--size-4-4);border-radius:var(--radius-m);width:100%;color:var(--text-muted);position:relative}.form--CalloutBlock.callout{border:1px solid rgba(var(--callout-color),.35)}.form--CalloutTitle{display:flex;gap:var(--size-4-1);align-items:center;width:100%}.form--CalloutBlockCloseButton{position:absolute;top:2px;right:2px}.form--CpsFormModal .form--CpsFormDataView .form--CpsFormPreviewBody{flex:1;overflow-y:auto;max-height:calc(80vh - 128px);padding:4px 8px}.form--CpsFormEditView{--form--setting-control-background-color: var(--background-primary);--form--setting-control-box-shadow: rgba(0, 0, 0, .1) 0px 1px 3px 0px, rgba(0, 0, 0, .06) 0px 1px 2px 0px;--form--setting-control-border: none}.form--CpsFormEditView button.form--AddButton{background-color:transparent;box-shadow:none;border:none;outline:none;color:var(--text-muted);padding:4px 8px;border-radius:var(--radius-m);border:1px dashed var(--background-modifier-border);transition:all .2s ease}.form--CpsFormEditView button.form--AddButton:hover{background-color:var(--color-base-20);color:var(--text-normal);font-weight:500;cursor:pointer}.form--CpsFormEditView button.form--AddButton:focus-visible{background-color:var(--color-base-30);box-shadow:0 0 0 2px var(--color-accent);color:var(--text-accent)}.form--CpsFormEditView button.form--AddButton:active{background-color:var(--color-base-30)}.form--CpsFormEditView button.clickable-icon,button.form--Btn{background-color:transparent;box-shadow:none;outline:none;height:24px;font-size:var(--font-ui-smaller);color:var(--text-muted);padding:4px 8px;cursor:pointer;gap:4px}.form--CpsFormEditView button.clickable-icon .lucide,button.form--Btn .lucide{height:14px;width:14px}.form--CpsFormEditView button.clickable-icon[data-type=danger],button.form--Btn[data-type=danger]{color:var(--color-base-50)}.form--CpsFormEditView button.clickable-icon[data-type=danger]:hover,button.form--Btn[data-type=danger]:hover{background-color:rgba(var(--color-red-rgb),.1);color:rgba(var(--color-red-rgb),.85)}.form--CpsFormEditView button.clickable-icon:hover,button.form--Btn:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.form--TabGroup{width:100%;display:flex;flex-direction:column;max-height:inherit;border-radius:var(--radius-m);overflow:hidden}.form--TabGroup[data-orientation=vertical]{flex-direction:row}.form--TabList{display:flex;background-color:transparent;color:var(--text-normal);border-bottom:1px solid var(--background-modifier-border);overflow-x:auto;-webkit-overflow-scrolling:touch;scrollbar-width:none;min-height:32px}.form--TabList::-webkit-scrollbar{display:none}.form--TabList[data-orientation=vertical]{flex-direction:column;min-width:150px;max-width:220px;width:25%;height:100%;border-bottom:none;border-right:1px solid var(--background-modifier-border);position:relative;overflow-y:auto;overflow-x:hidden}button.form--Tab{all:unset;display:flex;align-items:center;justify-content:center;height:32px;min-height:32px;padding:0 var(--size-4-2);cursor:pointer;position:relative;color:var(--text-muted);font-size:var(--font-ui-small);transition:color .15s ease,background-color .15s ease;user-select:none;white-space:nowrap;font-weight:var(--font-normal);flex-shrink:0;box-shadow:none;outline:none}button.form--Tab:focus-visible{box-shadow:none;outline:none}.form--Tab .form--TabTitle{line-height:1.4;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 var(--size-4-1);max-width:180px}.form--Tab[data-state=active]{color:var(--text-normal);background-color:var(--background-primary);font-weight:var(--font-medium);position:relative}.form--Tab[data-state=active]:after{content:"";position:absolute;bottom:0;left:0;right:0;height:2px;background-color:var(--text-normal)}.form--Tab:hover{color:var(--text-normal);background-color:var(--background-modifier-hover)}.form--Tab:focus-visible{box-shadow:inset 0 0 0 2px var(--background-modifier-border-focus);outline:none}.form--TabList[data-orientation=vertical] .form--Tab{justify-content:flex-start;padding:var(--size-4-1) var(--size-4-3);height:auto;min-height:36px;border-left:3px solid transparent}.form--TabList[data-orientation=vertical] .form--Tab[data-state=active]{border-left:3px solid var(--interactive-accent)}.form--TabList[data-orientation=vertical] .form--Tab[data-state=active]:after{display:none}.form--TabPanels{flex:1;padding:var(--size-4-3);border-radius:0 0 var(--radius-m) var(--radius-m);background-color:var(--background-primary);overflow:auto;width:100%}.form--TabGroup[data-orientation=vertical] .form--TabPanels{margin-top:0;border-radius:0 var(--radius-m) var(--radius-m) 0;height:100%}.form--TabPanel{outline:none;animation:fadeIn .2s ease}@keyframes fadeIn{0%{opacity:.8}to{opacity:1}}.form--TabList[data-orientation=vertical] .form--TabResizeBar{position:absolute;top:0;right:0;width:4px;height:100%;opacity:0;background-color:var(--interactive-accent);cursor:ew-resize;transition:opacity .15s ease;z-index:10}.form--TabList[data-orientation=vertical] .form--TabResizeBar:hover,.form--TabList[data-orientation=vertical] .form--TabResizeBar:active{opacity:.7}@media (max-width: 768px){.form--TabGroup[data-orientation=vertical]{flex-direction:column}.form--TabList[data-orientation=vertical]{flex-direction:row;min-width:100%;max-width:100%;width:100%;height:auto;border-right:none;border-bottom:1px solid var(--background-modifier-border);overflow-x:auto;overflow-y:hidden}.form--TabList[data-orientation=vertical] .form--Tab{border-left:none;border-bottom:3px solid transparent;padding:var(--size-4-2) var(--size-4-3)}.form--TabList[data-orientation=vertical] .form--Tab[data-state=active]{border-left:none;border-bottom:3px solid var(--interactive-accent)}.form--TabList[data-orientation=vertical] .form--TabResizeBar{display:none}.form--TabGroup[data-orientation=vertical] .form--TabPanels{border-radius:0 0 var(--radius-m) var(--radius-m)}.form--Tab .form--TabTitle{max-width:140px}}@media (pointer: coarse){.form--Tab{padding:var(--size-4-2) var(--size-4-3);min-height:44px}.form--TabList{min-height:44px}}button.form--DropdownLabel{display:flex;align-items:center;gap:6px;cursor:pointer;user-select:none;box-shadow:none;border-radius:var(--radius-s);background-color:transparent}button.form--DropdownLabel:hover{background-color:var(--background-modifier-hover)}.form--DropdownLabelText{font-size:var(--font-ui-small);color:var(--text-normal)}.form--DropdownMenus{background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--form-shadow);padding:8px;animation-duration:.2s;animation-timing-function:cubic-bezier(.16,1,.3,1);will-change:transform,opacity;border:1px solid var(--background-modifier-border);z-index:var(--form-overlay-layer);overflow-y:auto;min-width:min(var(--radix-popover-content-available-width),280px);max-width:var(--radix-popover-content-available-width);max-height:min(400px,var(--radix-dropdown-menu-content-available-height))}.form--DropdownMenuItem{display:flex;align-items:center;gap:8px;padding:8px 12px;border-radius:var(--radius-s);cursor:pointer;user-select:none;font-size:var(--font-ui-small);color:var(--text-normal);outline:none}.form--DropdownMenuItem:focus,.form--DropdownMenuItem:hover{background-color:var(--background-modifier-hover)}.form--DropdownMenuItem:active{background-color:var(--background-modifier-active)}.form--DropdownMenuItemIcon{display:flex;align-items:center;justify-content:center;flex-shrink:0;color:var(--text-muted);width:16px;height:16px}.form--DropdownMenuArrow{fill:var(--background-primary)}.form--FormVariableQuotePanel{display:flex;flex-direction:column;gap:.5rem;border-radius:.25rem}.form--FormVariables{display:flex;gap:.5rem;padding:4px;flex-wrap:wrap;align-items:center}.form--FormVariable{display:flex;align-items:center;justify-content:center;padding:.25rem .5rem;border-radius:.25rem;background-color:var(--background-primary-alt);color:var(--text-muted);font-size:var(--font-ui-small);border:1px solid var(--background-modifier-border);cursor:pointer;transition:background-color .2s ease,color .2s ease}.form--InternalFormVariable{color:var(--text-faint)}.form--FormVariable:hover{background-color:var(--background-modifier-hover);color:var(--text-accent)}.form--FormInternalVariables{display:flex;flex-direction:column;border-radius:var(--radius-m);box-shadow:var(--form-shadow);padding:8px;background-color:var(--background-primary);gap:.5rem;z-index:var(--form--modal-layer)}.form--FormInternalVariable{display:flex;flex-direction:column;gap:4px;padding:2px 4px;border-radius:var(--radius-m)}.form--FormInternalVariable[data-highlighted],.form--FormInternalVariable:hover{background-color:var(--background-modifier-hover)}.form--FormInternalVariableName{font-size:var(--font-ui-small);color:var(--text-normal);font-weight:500}.form--FormInternalVariableDescription{font-size:var(--font-ui-smaller);color:var(--text-faint)}button.form--FormInternalVariablesButton{background-color:transparent;box-shadow:none;border:1px solid var(--background-modifier-border);height:28px;color:var(--text-muted);cursor:pointer}button.form--FormInternalVariablesButton:hover{color:var(--text-accent);border-color:var(--interactive-accent);background-color:var(--background-primary)}button.form--FormInternalVariablesButton:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:1px}button.form--Select2Trigger{height:var(--input-height);font-size:var(--font-ui-small);font-family:inherit;font-weight:var(--input-font-weight);color:var(--text-normal);line-height:var(--line-height-tight);max-width:100%;box-sizing:border-box;margin:0;border:0;box-shadow:var(--input-shadow);border-radius:var(--input-radius);-webkit-appearance:none;appearance:none;background-color:var(--dropdown-background);background-repeat:no-repeat,repeat;background-position:var(--dropdown-background-position);background-size:var(--dropdown-background-size);background-blend-mode:hard-light;justify-content:space-between;align-items:center}button.form--Select2Trigger:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}button.form--Select2Trigger:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--Select2TriggerIcon{display:inline-flex;align-items:center;color:var(--text-muted);margin-left:16px}.form--Select2Content{display:flex;flex-direction:column;gap:4px;padding:.5rem;background-color:var(--background-primary);box-shadow:var(--form-shadow);font-size:var(--font-ui-small);z-index:var(--form--modal-layer);width:max(var(--radix-select-trigger-width),220px);height:min(var(--radix-select-content-available-height),300px);overflow-y:auto;border-radius:var(--radius-m)}.form--Select2Item{display:flex;align-items:center;gap:.5rem;padding:0 8px;line-height:1;height:24px;border-radius:var(--radius-s);cursor:pointer;font-size:var(--font-ui-smaller)}.form--Select2Item[data-highlighted],.form--Select2Item:hover{background-color:var(--background-modifier-hover)}.form--Select2ScrollButton{display:flex;align-items:center;justify-content:center;width:100%;height:24px;border-radius:var(--radius-s);cursor:pointer;color:var(--text-muted)}.form--Select2ScrollButton:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.form--Select2ScrollButton .lucide{width:16px;height:16px}.form--Select2ItemIndicator{display:flex;align-items:center;color:var(--text-muted)}.form--FilterRule button.form--Select2Trigger{background-color:transparent}.form--FilterRule button.form--Select2Trigger:hover{background-color:var(--background-modifier-hover)}.form--FormFilePathSuggestTrigger{-webkit-app-region:no-drag;background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);font-family:inherit;padding:var(--size-4-1) var(--size-4-2);font-size:var(--font-ui-small);border-radius:var(--input-radius);outline:none;min-height:var(--input-height);width:100%;cursor:text;color:var(--text-muted);line-height:1.5}.form--FormFilePathSuggestTrigger:hover{border-color:var(--background-modifier-border-hover);transition:box-shadow .15s ease-in-out,border .15s ease-in-out}.form--FormFilePathSuggestContent{display:flex;flex-direction:column;z-index:var(--form--modal-layer);background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--shadow-s);padding:8px 12px;animation:fadeIn .2s ease-out;overflow-y:auto;min-width:min(var(--radix-popover-content-available-width),280px);max-width:var(--radix-popover-content-available-width);max-height:min(360px,var(--radix-popover-content-available-height))}input.form--FormFilePathSuggestInput{width:100%;box-shadow:none;border:none;padding:8px}input.form--FormFilePathSuggestInput:focus-visible{box-shadow:none}.form--FormFilePathSuggestList{flex:1;overflow-y:auto;width:min(400px,var(--radix-popover-trigger-width));padding:8px 4px;gap:4px}.form--FormFilePathSuggestItem{padding:var(--size-4-1) var(--size-4-2);cursor:pointer;color:var(--text-normal);font-size:var(--font-ui-small);border-radius:var(--input-radius);margin-bottom:4px}.form--FormFilePathSuggestItem:hover,.form--FormFilePathSuggestItem[data-highlighted=true]{background-color:var(--background-modifier-hover);color:var(--text-normal)}.form--FileModalWindow.modal{max-width:min(80vw,960px);height:90vh;max-height:90vh;width:100%;overflow:hidden}@media screen and (max-width: 768px){.form--FileModalWindow.modal{max-width:100vw;width:100vw;max-height:90vh;align-self:flex-end}}.form--FileModalWindow .modal-content,.form--FileModalWindow .form--FileModalWindowContainer{height:100%}.form--FileModalWindowFullScreenButton{transition:opacity .15s ease-in-out;cursor:var(--cursor);position:absolute;top:var(--size-2-3);inset-inline-end:var(--size-2-3);font-size:24px;line-height:20px;height:24px;width:24px;padding:0 var(--size-2-2);border-radius:var(--radius-s);color:var(--text-muted);right:32px;display:flex;align-items:center}.form--FileModalWindowFullScreenButton:hover{background-color:var(--background-modifier-hover)}.form--FileModalWindowFullScreenButton .lucide{width:16px;height:16px;color:var(--text-normal)}.form--codeEditorContainer{position:relative;width:100%;border-radius:6px;overflow:hidden;background-color:var(--background-secondary);border:1px solid var(--background-modifier-border)}.form--codeEditor{height:100%;width:100%;font-family:Menlo,Monaco,Courier New,monospace;font-size:14px}.form--codeEditor .cm-editor{height:100%;outline:none!important}.form--codeEditor .cm-content{padding:10px 0}.form--codeEditor .cm-scroller{overflow:auto}.form--codeEditor .cm-gutters{background-color:var(--background-primary);border-right:1px solid var(--background-modifier-border)}.form--codeEditor .cm-lineNumbers .cm-gutterElement{padding:0 3px 0 5px;background-color:transparent;color:var(--text-faint)}.cm-gutter .cm-activeLineGutter{background-color:transparent}@media (max-width: 768px){.form--codeEditor{font-size:12px}}.form--codeEditor{--editor-background: var(--background-secondary);--editor-text: var(--text-normal);--editor-keyword: var(--color-red);--editor-string: var(--color-green);--editor-comment: var(--color-base-30);--editor-variable: var(--color-yellow);--editor-number: var(--color-cyan);--editor-property: var(--color-blue);--editor-atom: var(--color-blue);--editor-def: var(--color-purple);--editor-function: var(--color-green);--editor-selection: rgba(0, 0, 0, .1);--editor-active-line: rgba(0, 0, 0, .05)}.theme-dark .form--codeEditor{--editor-selection: rgba(255, 255, 255, .2);--editor-active-line: rgba(255, 255, 255, .1)}.form--codeEditor .\37c b,.form--codeEditor .\37c c,.form--codeEditor .\37c d{color:var(--editor-keyword)}.form--codeEditor .\37c e{color:var(--editor-string)}.form--codeEditor .\37c a{color:var(--editor-comment)}.\37c 5{color:var(--editor-text)}.form--codeEditor .\37cm,.form--codeEditor .\37cn{color:var(--editor-variable)}.form--codeEditor .\37c f{color:var(--editor-number)}.form--codeEditor .\37cl{color:var(--editor-property)}.form--codeEditor .\37ci{color:var(--editor-atom)}.form--codeEditor .\37cg{color:var(--editor-def)}.form--codeEditor .\37ck{color:var(--editor-function)}.form--codeEditor .\37ch,.form--codeEditor .\37cj{color:var(--editor-text)}.form--codeEditor .cm-selectionBackground,.form--codeEditor .cm-selectionBackground{background-color:var(--editor-selection)!important}.form--codeEditor .cm-activeLine{background-color:var(--editor-active-line)!important}.form--codeEditor .cm-line.cm-activeLine{background-color:var(--editor-active-line)!important;caret-color:auto!important}.form--codeEditor .cm-matchingBracket{background-color:var(--interactive-accent);color:var(--text-on-accent)}.form--codeEditor .\37c 2 .cm-tooltip{background-color:var(--background-primary);color:var(--text-normal);border-color:var(--background-modifier-border);border-radius:var(--radius-m);padding:var(--size-4-1)}.form--DragHandler{display:flex;align-items:center;justify-content:center;padding:var(--size-2-1);color:var(--text-muted);border-radius:var(--size-2-2);white-space:nowrap;cursor:grab}@media (any-hover: hover){.form--DragHandler:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}}.form--button:active{background-color:var(--background-modifier-hover);color:var(--text-normal);opacity:.8}.form--InteractiveList{display:flex;flex-direction:column;gap:.5rem;font-size:var(--font-ui-small)}.form--InteractiveListTitle{color:var(--text-normal);font-size:var(--font-ui-small);line-height:var(--line-height-tight);margin-bottom:.5rem}.form--InteractiveListItems{display:flex;flex-direction:column;gap:.5rem}.form--InteractiveList .form--AddButton{align-self:flex-start}.form--InteractiveListItem{display:flex;gap:.5rem;border-radius:4px;transition:all .2s ease;position:relative}.form--InteractiveListItemDrag{display:flex;align-items:flex-start;padding-top:4px}.form--InteractiveListItemContent{flex:1;display:flex;gap:.5rem}.form--InteractiveListItemFieldRow{display:flex;flex-direction:column;gap:.25rem;width:100%}.form--InteractiveListItemFieldLabel{font-size:var(--font-ui-smaller)}.form--InteractiveListItemActions{display:flex;gap:.25rem;align-items:flex-start}@media (min-width: 768px){.form--InteractiveListItemContent{flex-wrap:nowrap}.form--InteractiveListItemFieldRow{width:auto;flex-direction:row;flex:1}}.form--CpsFormSelectOptionValueTextarea{flex:1}.form--CpsFormFieldSettingContent{display:flex;flex-direction:column;background-color:var(--background-primary);width:100%;padding:var(--size-4-2) var(--size-4-2);gap:8px}.form--CpsFormFieldSettingContentHeader{display:flex;align-items:center;justify-content:flex-end;gap:8px}.form--CpsFormFieldSettingContentModeGroup{display:inline-flex;gap:var(--size-4-1);border-radius:var(--radius-s);background-color:var(--background-secondary);padding:var(--size-4-1);border:1px solid var(--background-modifier-border)}button.form--CpsFormFieldSettingContentModeItem{all:unset;display:inline-flex;align-items:center;justify-content:center;padding:var(--size-2-1) var(--size-2-3);border-radius:var(--radius-s);color:var(--text-muted);background:transparent;border:none;cursor:pointer;transition:all .2s ease}button.form--CpsFormFieldSettingContentModeItem:hover{background-color:var(--background-modifier-hover)}button.form--CpsFormFieldSettingContentModeItem[data-state=on]{color:var(--text-accent);background-color:hsl(var(--interactive-accent-hsl),.15)}.form--CpsFormFieldSettingContentModeItem svg{width:14px;height:14px}.form--CpsFormFieldSettingControlPreview{display:flex;flex-direction:column;gap:.5rem;background-color:var(--background-primary)}button.form--VisibilityConditionButton{display:inline-flex;align-items:center;justify-content:center;padding:var(--size-2-1) var(--size-2-3);border-radius:var(--radius-s);color:var(--text-faint);font-size:var(--font-ui-small);background:transparent;border:none;cursor:pointer;box-shadow:none;transition:all .2s ease;gap:4px}button.form--VisibilityConditionButton[data-has-condition=true]{color:var(--text-accent)}button.form--VisibilityConditionButton:hover{background-color:var(--background-modifier-hover);color:var(--text-normal)}.form--CpsFormFieldSettingError{background-color:rgba(var(--color-red-rgb),.1);color:rgba(var(--color-red-rgb),.85);padding:4px 8px;font-size:var(--font-ui-small);line-height:1.5;font-weight:500;display:flex;align-items:center;gap:8px;user-select:text}.form--CpsFormFieldSettingDescription{width:100%;display:flex;align-items:center}@media (any-hover: hover){.form--CpsFormFieldSettingDescription:hover{background-color:var(--background-modifier-hover)}}.form--CpsFormFieldSettingDescription input[type=text]{all:unset;width:100%;color:var(--text-faint);font-size:var(--font-ui-smaller);line-height:var(--line-height-tight);padding:4px 12px;flex-grow:1;border-radius:var(--radius-m)}.form--CpsFormFieldSettingDescription input[type=text]:focus{outline:none;background-color:var(--color-base-10)}.form--DialogRoot{display:flex;align-items:center;justify-content:center;position:absolute;top:0;bottom:0;inset-inline-start:0;width:100%;z-index:var(--form-overlay-layer)}.form--DialogOverlay{background-color:#282828a6;position:fixed;inset:0;backdrop-filter:blur(2px)}.form--DialogContent{display:flex;flex-direction:column;border-radius:var(--radius-l);background-color:var(--background-primary);box-shadow:var(--shadow-l);border:var(--prompt-border-width) solid var(--prompt-border-color);z-index:1;position:absolute;top:var(--form--DialogContent-top, 120px);min-width:min(300px,80vw);min-height:var(--form--DialogContent-minHeight, 120px);width:var(--form--DialogContent-width, 700px);max-width:var(--form--DialogContent-maxWidth, 80vw);max-height:var(--form--DialogContent-maxHeight, 85vh);overflow:hidden;z-index:var(--form-overlay-layer);margin:env(safe-area-inset-top,0) env(safe-area-inset-right,0) env(safe-area-inset-bottom,0) env(safe-area-inset-left,0)}.is-mobile .form--DialogRoot{align-items:flex-start}.is-mobile .form--DialogContent{--DialogContent-top: calc(var(--safe-area-inset-top) + var(--header-height)) + 36px;height:calc(var(--viewport-height) - var(--DialogContent-top));--form--DialogContent-width: 100%;--form--DialogContent-maxWidth: 100%;--form--DialogContent-top: 0px;--form--DialogContent-maxHeight: 95vh;top:0;border-radius:var(--radius-l) var(--radius-l) 0px 0px}.form--DialogPanelChildren{flex:1;overflow-y:auto;overflow-x:hidden;padding:var(--size-4-4);position:relative;scroll-behavior:smooth;-webkit-overflow-scrolling:touch;scrollbar-width:thin;scrollbar-color:var(--scrollbar-thumb-bg) var(--scrollbar-bg)}.form--DialogTitle{margin:0;padding:var(--size-4-2) var(--size-4-4) var(--size-4-1) var(--size-4-4);font-size:var(--font-ui-medium);color:var(--text-normal);font-weight:500;flex-shrink:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding-right:44px;min-height:36px}.form--DialogDescription{padding:0 var(--size-4-4) var(--size-4-2);margin:0;color:var(--text-muted);font-size:var(--font-ui-small);flex-shrink:0;max-height:4.5em;overflow-y:auto;line-height:1.5}.form--DialogPanelChildren::-webkit-scrollbar{width:6px}.form--DialogPanelChildren::-webkit-scrollbar-track{background:var(--scrollbar-bg, transparent)}.form--DialogPanelChildren::-webkit-scrollbar-thumb{background-color:var(--scrollbar-thumb-bg, rgba(0, 0, 0, .2));border-radius:3px}button.form--DialogCloseButton{position:absolute;top:8px;right:8px;padding:var(--size-4-1);cursor:pointer;border-radius:var(--radius-m);width:28px;height:28px;box-shadow:none;background-color:transparent;border:none;color:var(--text-muted);display:flex;align-items:center;justify-content:center;z-index:1;transition:all .12s ease}button.form--DialogCloseButton:hover{background-color:var(--background-modifier-hover);color:var(--text-accent);transform:scale(1.05)}button.form--DialogCloseButton:active{transform:scale(.95);background-color:var(--background-modifier-active)}.form--Filter{display:flex;gap:8px}.form--FilterRelation{display:flex;align-items:center;text-align:center;line-height:1.5;vertical-align:middle;height:32px;padding:2px 4px;font-size:var(--font-ui-smaller);white-space:nowrap}.form--FilterRelation .form--Select2Trigger{background-color:transparent;box-shadow:none;padding:2px 4px;cursor:pointer;font-size:var(--font-ui-smaller)}.form--FilterRelation .form--Select2TriggerIcon{margin-left:4px}button.form--FilterRelationButton{height:24px;border:1px solid var(--background-modifier-border)}.form--FilterContent{flex:1}.form--FilterRule{display:flex;gap:8px;align-items:center}.form--FilterGroup{display:flex;flex-direction:column;gap:8px;background-color:var(--color-base-10);padding:4px 8px;border-radius:var(--radius-s);border:1px solid var(--background-modifier-border)}.form--FilterDropdownMenuContent{background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--form-shadow);padding:8px;animation-duration:.2s;animation-timing-function:cubic-bezier(.16,1,.3,1);will-change:transform,opacity;border:1px solid var(--background-modifier-border);z-index:var(--form-overlay-layer);overflow-y:auto;min-width:min(var(--radix-dropdown-menu-content-available-width),200px);max-width:var(--radix-dropdown-menu--content-available-width);max-height:min(400px,var(--radix-dropdown-menu-content-available-height))}.form--FilterDropdownMenuItem{display:flex;align-items:center;gap:8px;padding:6px 12px;border-radius:var(--radius-s);cursor:pointer;user-select:none;font-size:var(--font-ui-smaller);color:var(--text-normal);outline:none}.form--FilterDropdownMenuItem:focus,.form--FilterDropdownMenuItem:hover,.form--FilterDropdownMenuItem[data-highlighted]{background-color:var(--background-modifier-hover)}.form--FilterDropdownMenuItem:active{background-color:var(--background-modifier-active)}.form--FilterDropdownMenuItemIcon{display:flex;align-items:center;justify-content:center;flex-shrink:0;color:var(--text-muted);width:16px;height:16px}.form--FilterDropdownMenuArrow{fill:var(--background-primary)}.form--FilterRoot{padding:4px 12px;display:flex;flex-direction:column;gap:8px;overflow:auto}.form--FilterRootContent{display:flex;flex-direction:column;gap:8px;border-bottom:1px solid var(--background-modifier-border);padding-bottom:8px}.form--FilterRootAdd{position:relative}.form--FilterRootFooter{display:flex;flex-direction:column;gap:4px}button.form--UndoClearFilterButton,button.form--ClearFilterButton{width:100%;background-color:transparent;box-shadow:none;border:none;display:inline-flex;align-items:center;justify-content:flex-start;gap:4px;color:var(--text-muted);cursor:pointer}button.form--ClearFilterButton:hover{background-color:rgba(var(--color-red-rgb),.1);color:rgba(var(--color-red-rgb),.85)}button.form--UndoClearFilterButton:hover{background-color:rgba(var(--color-green-rgb),.1);color:rgba(var(--color-green-rgb),.85)}button.form--PopoverTrigger{display:flex;align-items:center;justify-content:space-between;min-width:150px;padding:2px 28px 2px 8px;background-color:var(--background-secondary);border:none;border-radius:var(--radius-s);color:var(--text-normal);cursor:default;text-align:left;position:relative;height:auto;min-height:30px;overflow:hidden;width:100%}button.form--PopoverTrigger .lucide{color:var(--text-muted);transition:transform .2s ease-in-out;position:absolute;right:8px;top:50%;transform:translateY(-50%);flex-shrink:0}button.form--PopoverTrigger:hover{box-shadow:var(--input-shadow-hover);background-color:var(--dropdown-background-hover)}button.form--PopoverTrigger:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:1px}.form--PopoverContent{z-index:var(--form--modal-layer);background-color:var(--background-primary);border-radius:var(--radius-m);box-shadow:var(--shadow-s);padding:8px 12px;animation:fadeIn .2s ease-out;overflow-y:auto;min-width:min(var(--radix-popover-content-available-width),280px);max-width:var(--radix-popover-content-available-width);max-height:min(400px,var(--radix-popover-content-available-height))}.form--PopoverArrow{fill:var(--background-primary)}@keyframes fadeIn{0%{opacity:0;transform:translateY(4px)}to{opacity:1;transform:translateY(0)}}.form--ConfirmPopover{display:flex;flex-direction:column;gap:8px}.form--ConfirmTitle{margin:0;font-weight:600}.form--ConfirmMessage{font-size:var(--font-ui-smaller);color:var(--text-normal)}.form--ConfirmActions{display:flex;justify-content:flex-end;gap:8px;margin-top:8px}.form--ConfirmButton:hover,.form--CancelButton:hover{background-color:var(--background-modifier-hover)}button.form--FieldTypeSelectButton{box-shadow:none;background-color:transparent;border:none;outline:none;cursor:pointer;padding:2px 6px;gap:4px;height:auto;font-size:var(--font-ui-small);background-color:var(--color-base-20);color:var(--text-muted)}button.form--FieldTypeSelectButton .form--FieldTypeSelectButtonLabel{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}@media (max-width: 600px){button.form--FieldTypeSelectButton .form--FieldTypeSelectButtonLabel{display:none}}button.form--FieldTypeSelectButton:hover{background-color:var(--background-modifier-hover);color:var(--text-accent)}button.form--FieldTypeSelectButton:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--FieldTypeSelectOptions{display:flex;flex-direction:column;gap:4px;padding:.5rem;background-color:var(--background-primary);box-shadow:var(--form-shadow);font-size:var(--font-ui-small);z-index:var(--form--modal-layer)}.form--FieldTypeSelectOption{display:flex;align-items:center;gap:.5rem;padding:4px 8px;border-radius:var(--border-radius);cursor:pointer}.form--FieldTypeSelectOption[data-selected]{color:var(--text-accent)}.form--FieldTypeSelectOption:hover{background-color:var(--background-modifier-hover)}.form--DateFieldDefaultValueControl{display:flex;flex-direction:column;gap:.5rem;align-items:flex-end}@media screen and (max-width: 768px){.form--DateFieldDefaultValueControl{display:flex;flex-direction:column;gap:.5rem;align-items:initial;width:auto;flex-grow:0!important}}.form--ToggleContainer{display:flex;flex-direction:row;align-items:center;gap:var(--size-4-1);padding:var(--size-2-1) var(--size-4-1);font-size:var(--font-ui-small);border:1px solid var(--background-modifier-border);border-radius:var(--radius-m)}.form--ToggleItem{display:flex;flex-direction:row;align-items:center;border-radius:var(--radius-s);padding:var(--size-2-1) var(--size-4-2);color:var(--text-faint);cursor:pointer;white-space:nowrap}.form--ToggleItem.form--ToggleItem_active{background-color:hsl(var(--interactive-accent-hsl),.1);color:hsl(var(--interactive-accent-hsl),1)}@media (any-hover: hover){.form--ToggleItem:hover{background-color:var(--background-modifier-hover)}.form--ToggleItem.form--ToggleItem_active:hover{background-color:var(--background-modifier-active-hover)}}.form--CpsFormFieldSettingHeader{display:flex;align-items:center;cursor:pointer;padding:var(--size-4-1) var(--size-4-2);font-size:var(--font-ui-small);color:var(--text-muted);gap:4px;white-space:nowrap;position:relative;width:100%;border-bottom:1px solid var(--color-base-20);border-radius:var(--radius-m) var(--radius-m) 0 0}.form--CpsFormFieldSettingHeaderControl{display:flex}.form--CpsFormFieldSettingHeaderControl .form--FormTypeSelectButton{color:var(--text-faint)}.form--CpsFormFieldSettingHeader .form--DragHandler{height:30px}.form--CpsFormFieldSettingPopover{display:flex;flex-direction:column;gap:4px;padding:.5rem;background-color:var(--background-primary);box-shadow:var(--form-shadow);font-size:var(--font-ui-small);z-index:var(--form--modal-layer);border-radius:var(--radius-m);position:relative;min-width:min(var(--radix-popover-content-available-width),280px);max-width:var(--radix-popover-content-available-width)}.form--CpsFormFieldSettingPopoverTitle{display:flex;align-items:center;gap:4px;font-size:var(--font-ui-small);color:var(--text-muted);font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;padding:8px 0}button.form--CpsFormFieldSettingPopoverClose{position:absolute;top:.5rem;right:.5rem;width:24px;height:24px;border-radius:50%;cursor:pointer;padding:0}.form--CpsFormFieldSettingPopover .form--CpsFormItemInfoName{font-size:var(--font-ui-smaller)}.form--CpsFormFieldSettingPopover .form--FormTypeSelectButton{background-color:var(--interactive-normal);box-shadow:var(--input-shadow);outline:none;cursor:pointer;gap:4px;width:auto;height:var(--input-height);justify-content:space-between}.form--CpsFormFieldSettingPopover button.form--FormTypeSelectButton .form--FormTypeSelectButtonLabel{display:flex;flex:1}.form--CpsFormFieldsSetting{display:flex;flex-direction:column;gap:8px;width:100%}.form--CpsFormFieldSetting{display:flex;flex-direction:column;position:relative;flex-wrap:wrap;border-radius:var(--radius-m);background-color:var(--form--setting-control-background-color);box-shadow:var(--form--setting-control-box-shadow);border:var(--form--setting-control-border);padding:4px 2px}@media (max-width: 768px){.form--CpsFormFieldSetting{flex-direction:column;padding:4px 6px}.form--CpsFormFieldSetting .form--CpsFormFieldSettingControl{justify-content:flex-start}.form--CpsFormFieldSetting .form--CpsFormFieldSettingControl>*:first-child{flex-grow:1}.form--CpsFormFieldSetting .form--CpsFormFieldSettingControl>input[type=checkbox]{flex-grow:0}}.form--CpsFormFieldSettingControl{display:flex;gap:.5rem;flex:1;justify-content:flex-end;align-items:center}input[type=text].form--CpsFormFieldSettingLabelInlineInput{box-shadow:none;border:none;outline:none;background-color:transparent;min-width:48px;text-overflow:ellipsis;flex:1}input[type=text].form--CpsFormFieldSettingLabelInlineInput:hover{background-color:var(--background-modifier-hover)}input[type=text].form--CpsFormFieldSettingLabelInlineInput:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--CpsFormFieldSettingLabelRequired{color:var(--text-error)}.form--ExtensionEditor{display:flex;flex-direction:column;border:1px solid var(--background-modifier-border);border-radius:4px;overflow:hidden;height:100%;background-color:var(--background-primary)}.form--ExtensionEditorHeader{display:flex;flex-direction:column;padding:12px;border-bottom:1px solid var(--background-modifier-border)}.form--ExtensionEditorHeaderLabel{margin-bottom:8px;font-weight:500;color:var(--text-normal)}.form--ExtensionEditorHeaderInput{display:flex;align-items:center}.form--ExtensionEditorHeaderInput input{flex:1;padding:8px 12px;border:1px solid var(--background-modifier-border);border-radius:4px;background-color:var(--background-primary-alt);color:var(--text-normal)}.form--ExtensionEditorHeaderInput input:focus{border-color:var(--interactive-accent);box-shadow:0 0 0 2px rgba(var(--interactive-accent-rgb),.2)}.form--ExtensionEditorBody{display:flex;flex-direction:column;overflow:hidden;max-height:400px;height:400px}.form--ExtensionEditorContent{display:flex;flex-direction:row;flex:1;overflow:hidden}.form--ExtensionEditorFunctionList{width:200px;overflow-y:auto;border-right:1px solid var(--background-modifier-border);max-height:100%}.form--ExtensionEditorFunctionItem{padding:8px 12px;cursor:pointer;display:flex;flex-direction:row;align-items:center;justify-content:space-between;border-bottom:1px solid var(--background-modifier-border-subtle)}.form--ExtensionEditorFunctionItem:hover{background-color:var(--background-modifier-hover)}.form--ExtensionEditorFunctionItem.active{background-color:var(--background-modifier-hover);border-left:2px solid var(--interactive-accent)}.form--ExtensionEditorFunctionName{font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.form--ExtensionEditorFunctionTag{display:inline-flex;align-items:center;font-size:10px;padding:1px 4px;border-radius:3px;background-color:var(--background-primary-alt);color:var(--text-muted);margin-left:6px;white-space:nowrap}.form--ExtensionEditorFunctionDetail{flex:1;padding:16px;overflow-y:auto;max-height:100%;user-select:text}.form--ExtensionEditorFunctionDetailHeader{display:flex;align-items:center;margin-bottom:16px;padding-bottom:8px;border-bottom:1px solid var(--background-modifier-border)}.form--ExtensionEditorFunctionDetailTitle{font-size:16px;font-weight:600;margin-right:8px;display:flex;align-items:center}.form--ExtensionEditorFunctionDetailPath{font-size:12px;color:var(--text-muted);margin-top:8px;display:flex;align-items:center}.form--ExtensionEditorFunctionDetailContent{color:var(--text-normal);position:relative}.form--ExtensionEditorFunctionDetailContent pre[class*=language-]{position:relative}.form--ExtensionEditorFunctionDetailContent button.copy-code-button{position:absolute;top:2px;right:2px;background-color:transparent;box-shadow:initial}.form--ExtensionEditorFunctionDetailContent button.copy-code-button:hover{background-color:var(--background-modifier-hover)}.form--ExtensionEditorEmptyState{padding:8px 16px}.form--ExtensionEditorEmptyAction{margin-top:16px;display:flex;justify-content:flex-start}.form--ExtensionEditorEmptyAction button{display:flex;align-items:center;gap:8px;padding:6px 12px;border-radius:4px;font-size:14px;background-color:hsl(var(--interactive-accent-hsl),.1);color:hsl(var(--interactive-accent-hsl),1);border:1px solid hsl(var(--interactive-accent-hsl),1)}.form--ExtensionEditorEmptyAction button .lucide{color:hsl(var(--interactive-accent-hsl),1)}.form--ExtensionEditorEmptyAction button.form--button:hover{background-color:hsl(var(--interactive-accent-hsl),1);color:var(--text-on-accent)}.form--ExtensionEditorEmptyAction button.form--button:hover .lucide{color:var(--text-on-accent)}.form--ExtensionTagSelector{display:flex;flex-wrap:wrap;gap:8px;padding:12px;border-bottom:1px solid var(--background-modifier-border);background-color:var(--background-secondary)}.form--ExtensionTag{display:flex;align-items:center;padding:4px 10px;border-radius:14px;font-size:12px;cursor:pointer;background-color:var(--background-modifier-border);color:var(--text-muted);transition:all .2s ease}.form--ExtensionTag:hover{background-color:var(--background-modifier-hover)}.form--ExtensionTag.active{background-color:var(--interactive-accent);color:var(--text-on-accent)}.form--ExtensionTagCount{margin-left:6px;font-size:10px;background-color:#0000001a;border-radius:10px;padding:1px 6px;min-width:16px;text-align:center}.form--ExtensionTag.active .form--ExtensionTagCount{background-color:#fff3}.form--CpsFormActionsSetting{display:flex;flex-direction:column;gap:var(--size-4-2);width:100%}.form--CpsFormActionSetting{border-radius:var(--radius-m);width:100%;position:relative;background-color:var(--form--setting-control-background-color);box-shadow:var(--form--setting-control-box-shadow);border:var(--form--setting-control-border)}.form--CpsFormActionSetting[data-is-valid=false]{border:1px solid rgba(var(--color-red-rgb),.8)}@media (any-hover: hover){.form--CpsFormActionSetting:hover{box-shadow:#0000001a 0 4px 6px -1px,#0000000f 0 2px 4px -1px}}.form--CpsFormActionHeader{display:flex;align-items:center;cursor:pointer;padding:var(--size-4-2) var(--size-4-3);border-bottom:1px solid var(--background-modifier-border);font-size:var(--font-ui-small);color:var(--text-muted);gap:4px}.form--CpsFormActionErrorTips{display:flex;align-items:center;gap:var(--size-4-1);padding:var(--size-4-2) var(--size-4-3);background-color:rgba(var(--color-red-rgb),.1);color:rgba(var(--color-red-rgb),.85);font-size:var(--font-ui-smaller)}.form--CpsFormActionHeaderType{display:flex;align-items:center;gap:var(--size-4-1);background-color:hsl(var(--interactive-accent-hsl),.1);color:hsl(var(--interactive-accent-hsl),1);padding:2px 6px;border-radius:var(--radius-m);font-size:var(--font-ui-smaller);white-space:nowrap}.form--CpsFormActionHeaderTitle{flex-grow:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding-inline-start:8px}.form--CpsFormActionHeaderControl{display:flex;gap:var(--size-4-1)}.form--CpsFormActionContent{padding:var(--size-4-1) var(--size-4-2)}button.form-CpsFormActinoCodeResetButton{background-color:transparent;box-shadow:none;outline:none;height:24px;font-size:var(--font-ui-smaller);color:var(--text-faint)}button.form-CpsFormActinoCodeResetButton:hover{background-color:var(--background-modifier-hover);color:var(--text-normal);font-weight:500;border-radius:var(--radius-m);cursor:pointer}.form--CpsFormActionCondition{display:flex;align-items:center;justify-content:flex-end;padding:var(--size-4-1) var(--size-4-2)}.form--CpsFormSettingGroup{display:flex;flex-direction:column;gap:var(--size-4-2);width:100%}.form--CpsFormSettingGroup .form--SettingGroupHeader{display:flex;align-items:center;color:var(--text-muted);gap:8px;font-size:1rem;font-weight:500}.form--CpsFormSettingGroup .form--SettingGroupHeader .lucide{height:18px;width:18px;color:var(--text-accent)}.form--CpsFormSettingGroup .form--SettingGroupContent{border-radius:var(--radius-m);padding:8px 16px;position:relative;background-color:var(--color-base-10)}@media (max-width: 768px){.form--CpsFormSettingGroup .form--SettingGroupContent{padding:4px 8px}}.form--CpsFormDescription{font-size:var(--font-ui-small);color:var(--text-faint);white-space:pre-line;line-height:1.5;user-select:text}.form--FormFieldLabelDescription{font-size:var(--font-ui-small);color:var(--text-faint);white-space:pre-line;line-height:1.5;user-select:text;text-decoration:none;flex:1}.form--CommandHotkeyLabel{font-size:var(--font-ui-smaller);color:hsl(var(--interactive-accent-hsl),1);background-color:hsl(var(--interactive-accent-hsl),.1);display:flex;align-items:center;justify-content:center;padding:var(--size-4-1) var(--size-4-2);border-radius:var(--radius-m)}.form--CpsFormFileView{max-width:700px;border:1px solid var(--background-modifier-border);border-radius:var(--radius-m);padding:8px 16px;margin:0 auto}@media (max-width: 768px){.form--CpsFormFileView{width:100%;max-width:100%;padding:4px 8px}}.form--CpsFormFileViewHeader{display:flex;gap:.5rem;align-items:center;color:var(--text-muted);width:100%;justify-content:space-between;margin-bottom:1rem}button.form--CpsFormFileViewModeButton{justify-self:flex-end;gap:4px}:not(.is-mobile) .form--CpsFormModal .modal{top:80px;position:absolute}.is-mobile .form--CpsFormModal.modal-container{display:flex;align-items:flex-end}.is-mobile .form--CpsFormModal .modal{--cpsform-modal-top: calc(var(--safe-area-inset-top) + var(--header-height)) + 36px;height:calc(var(--viewport-height) - var(--cpsform-modal-top))}.form--CpsFormModal .form--CpsFormPreviewFooter{margin-bottom:0}.form--CpsFormModalHeader{display:flex;align-items:center;gap:.5rem;font-size:1.05rem;font-weight:500;color:var(--text-muted);margin-bottom:8px}.form--CpsFormModalHeader:hover{cursor:pointer;color:var(--interactive-accent)}.form--CpsFormModalContent{display:flex;flex-direction:column}.form--CpsFormModalContent.form--CpsFormFileView{border-color:transparent;max-width:100%}.form--CpsFormModalContent.form--CpsFormFileView .form--CpsFormPreviewBody{flex:1;overflow-y:auto;max-height:calc(80vh - 96px);padding:4px 8px}.form--CpsFormSuggestionItem{display:flex;justify-content:space-between}.form--CpsFormSuggestionItemMenus{display:flex;gap:.5rem}.form--CpsFormSuggestionItemMenu{font-size:var(--font-ui-smaller);color:var(--text-faint);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);padding:2px 4px}@media (any-hover: hover){.form--CpsFormSuggestionItemMenu{opacity:0}.form--CpsFormSuggestionItemMenu:hover{cursor:pointer;color:var(--text-on-accent);background-color:var(--interactive-accent)}}.form--CpsFormSuggestionItem.is-selected .form--CpsFormSuggestionItemMenu{opacity:1;transition:opacity .2s ease-in-out}.is-mobile .form--CreateFileFormModal .modal{top:calc(var(--safe-area-inset-top) + var(--header-height) + var(--size-4-2));bottom:unset;height:100vh}.form--CreateFileForm{display:flex;flex-direction:column;gap:1rem;padding:.5rem}.form--CreateFileFormItem{display:flex;flex-direction:column;gap:.5rem}.form--CreateFileFormItemLabel{width:100%;flex:1;height:30px;font-size:var(--font-ui-small);color:var(--text-muted)}.form--CreateFileFormItemControl{width:100%}.form--CreateFileFormItemControl>input{width:100%}.form--CreateFileFormFooter{display:flex;flex-direction:row;justify-content:flex-end;gap:1rem}.form--CreateFileFormFilePath{font-size:var(--font-ui-smaller);color:var(--text-faint)}button.form--TextButton{background-color:transparent;color:var(--text-muted);cursor:pointer;outline:none;border:none;box-shadow:none}button.form--TextButton:hover{color:var(--text-accent);background-color:hsl(var(--interactive-accent-hsl),.1)}button.form--TextButton:focus-visible{box-shadow:0 0 0 3px var(--background-modifier-border-focus)}.form--ExtraSettingInfoCard{display:flex;flex-direction:column;gap:8px;padding:12px 16px;border-radius:var(--radius-m);color:var(--text-muted);transform:scale(1);transition:transform .3s;cursor:pointer;border:1px solid transparent;box-shadow:var(--background-modifier-border) 0 1px 3px,var(--background-modifier-border) 0 1px 2px;letter-spacing:.035em}.form--ExtraSettingInfoCard:hover{transform:scale(1.05);transition:transform .3s;border:1px solid var(--background-modifier-border-hover)}.form--ExtraSettingInfoCard:hover button{color:var(--text-accent);border:1px solid var(--text-accent)}.form--ExtraSettingInfoCardTitle{display:flex;align-items:center;gap:8px;font-weight:500;font-size:1rem;color:var(--color-base-80)}.form--ExtraSettingInfoCardContent{font-size:.82rem;color:var(--color-base-60)}.form--ExtraSettingInfoCardAction{position:absolute;top:8px;right:8px;color:var(--text-accent);opacity:0}.form--ExtraSettingInfoCard:hover .form--ExtraSettingInfoCardAction{opacity:1;transition:opacity .3s}.form--Divider{margin:0;padding:0;width:100%;height:1px;background-color:var(--background-modifier-border)}.form--ExtraSettings{display:grid;grid-template-columns:repeat(auto-fill,minmax(180px,1fr));gap:var(--size-4-4)}body{--form-overlay-layer: 19;--form--modal-layer: 99;--form-shadow: rgba(15, 15, 15, .05) 0px 0px 0px 1px, rgba(15, 15, 15, .1) 0px 3px 6px, rgba(15, 15, 15, .2) 0px 9px 24px}.theme-dark{--form-shadow: rgba(0, 0, 0, .2) 0px 12px 28px 0px, rgba(0, 0, 0, .1) 0px 2px 4px 0px, rgba(255, 255, 255, .05) 0px 0px 0px 1px inset}
