        }
        
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 英语合规表达练习器</h1>
            <p>基于您的笔记库进行场景化英语练习</p>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <!-- 场景选择 -->
                <div class="control-group">
                    <h3><span class="icon">🎭</span>场景选择</h3>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="selectScenario('random')">🎲 随机场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('meeting')">🤝 开会场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('compliance')">⚖️ 合规场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('technical')">💻 技术场景</button>
                        <button class="btn btn-secondary" onclick="selectScenario('legal')">📜 法律场景</button>
                    </div>
                </div>
                
                <!-- 难度选择 -->
                <div class="control-group">
                    <h3><span class="icon">📊</span>难度等级</h3>
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="selectDifficulty('基础')">🟢 基础</button>
                        <button class="btn btn-primary active" onclick="selectDifficulty('中级')">🟡 中级</button>
                        <button class="btn btn-secondary" onclick="selectDifficulty('高级')">🔴 高级</button>
                    </div>
                </div>
                
                <!-- 练习模式 -->
                <div class="control-group">
                    <h3><span class="icon">🎯</span>练习模式</h3>
                    <div class="btn-group">
                        <button class="btn btn-primary active" onclick="selectMode('template')">📝 模板填空</button>
                        <button class="btn btn-secondary" onclick="selectMode('keywords')">🔤 关键词造句</button>
                        <button class="btn btn-secondary" onclick="selectMode('scenario')">🎬 情景对话</button>
                        <button class="btn btn-secondary" onclick="selectMode('correction')">✏️ 错误纠正</button>
                    </div>
                </div>

                <!-- 可选关键词 -->
                <div class="control-group">
                    <h3><span class="icon">🔤</span>可选关键词</h3>
                    <div style="margin-bottom: 15px;">
                        <button class="btn btn-secondary" onclick="toggleKeywordSelection()">
                            <span id="keywordToggleText">🎲 随机关键词</span>
                        </button>
                    </div>
                    <div id="keywordSelection" class="hidden">
                        <div style="margin-bottom: 10px;">
                            <small style="color: #6c757d;">选择您想要练习的关键词类型：</small>
                        </div>
                        <div class="btn-group" style="flex-wrap: wrap;">
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('连接词')">连接词</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('时间表达')">时间表达</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('义务动词')">义务动词</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('技术术语')">技术术语</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('法律术语')">法律术语</button>
                            <button class="btn btn-secondary" onclick="toggleKeywordCategory('合规术语')">合规术语</button>
                        </div>
                        <div id="selectedKeywordsDisplay" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; min-height: 40px;">
                            <small style="color: #6c757d;">已选择的关键词将显示在这里</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 开始练习按钮 -->
            <div style="text-align: center; margin-bottom: 30px;">
                <button class="btn btn-success" onclick="startPractice()" style="font-size: 1.2em; padding: 15px 40px;">
                    🚀 开始练习
                </button>
            </div>
            
            <!-- 练习区域 -->
            <div class="practice-area" id="practiceArea">
                <div style="text-align: center; color: #6c757d; font-size: 1.2em;">
                    <p>🎯 选择您的练习设置，然后点击"开始练习"</p>
                    <p style="margin-top: 10px; font-size: 1em;">基于您的笔记库，智能生成个性化练习内容</p>
                </div>
            </div>
            
            <!-- 统计面板 -->
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-number" id="practiceCount">0</div>
                    <div class="stat-label">今日练习次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="accuracyRate">0%</div>
                    <div class="stat-label">准确率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="streakCount">0</div>
                    <div class="stat-label">连续正确</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalTime">0</div>
                    <div class="stat-label">练习时长(分钟)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentScenario = 'random';
        let currentDifficulty = '中级';
        let currentMode = 'template';
        let practiceData = {};
        let stats = {
            practiceCount: 0,
            correctCount: 0,
            streakCount: 0,
            totalTime: 0,
            startTime: null
        };

        // 真正基于您笔记的数据结构 - 从实际笔记文件提取
        const realTemplateData = {
            "According_to_PIPL_Article_51": {
                template: "According_to_[法律名称]_Article_[条款编号]_[主体]_shall_[义务]_to_[目的]",
                difficulty: "中级",
                frequency: "高频",
                tags: ["法律引用", "义务表述", "句式模板"],
                usage_scenario: "引用具体法律条款时的标准句式模板"
            },
            "Based_on_our_compliance_framework": {
                template: "Based_on_our_[框架名称]_[主体]_implemented_[措施]_[时间]_to_address_[风险类型]",
                difficulty: "中级",
                frequency: "高频",
                tags: ["框架实施", "时序表达", "句式模板"],
                usage_scenario: "描述合规框架实施情况时"
            },
            "Data_controllers_shall": {
                template: "[主体]_shall_[义务动词]_[措施]_pursuant_to_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["法律义务", "句式模板", "GDPR条款"],
                usage_scenario: "表述法律义务时的标准模板"
            },
            "Under_法律名称": {
                template: "Under_[法律名称]_[主体]_must/shall_[义务]",
                difficulty: "中级",
                frequency: "高频",
                tags: ["法律要求", "句式模板", "标准格式"],
                usage_scenario: "描述法律要求时的标准句式模板"
            },
            "We_have_deployed_DSAR": {
                template: "We_have_deployed_[系统]_through_[技术手段]_and_achieved_[时间指标]_for_[法规条款]",
                difficulty: "高级",
                frequency: "高频",
                tags: ["三并列句", "DSAR门户", "自动化流程"],
                usage_scenario: "全面展示合规成果的三维价值"
            }
        };

        // 变量数据库（基于您的变量笔记）
        const variableDatabase = {
            "法律名称": ["GDPR", "PIPL", "CCPA", "CPRA", "Data Security Law", "Cybersecurity Law"],
            "主体": ["Data controllers", "Data processors", "Enterprises", "Organizations", "Financial institutions"],
            "义务": ["implement", "establish", "maintain", "protect", "ensure", "monitor", "report", "notify"],
            "技术方案": ["AES-256 encryption", "zero-knowledge proofs", "federated learning", "differential privacy", "homomorphic encryption"],
            "时间": ["within 72 hours", "within one month", "annually", "immediately", "without undue delay"],
            "条件": ["explicit consent", "legitimate interests", "contractual necessity", "legal obligation", "vital interests"],
            "问题": ["compliance gaps", "data breach risks", "regulatory penalties", "cross-border restrictions", "technical limitations"]
        };

        // 可选关键词池（用户可以自选）
        const optionalKeywords = {
            "连接词": ["pursuant to", "in accordance with", "therefore", "furthermore", "moreover", "consequently", "nevertheless"],
            "时间表达": ["within", "by", "no later than", "immediately", "without undue delay", "annually", "quarterly"],
            "义务动词": ["shall", "must", "implement", "ensure", "establish", "maintain", "monitor"],
            "技术术语": ["encryption", "authentication", "authorization", "pseudonymization", "anonymization", "tokenization"],
            "法律术语": ["adequacy decisions", "SCCs", "BCRs", "legitimate interests", "explicit consent", "supervisory authority"],
            "合规术语": ["compliance", "regulatory", "mandatory", "assessment", "audit", "documentation", "remediation"]
        };

        // 用户选择的关键词
        let selectedOptionalKeywords = [];
        let selectedKeywordCategories = [];

        // 选择场景
        function selectScenario(scenario) {
            currentScenario = scenario;
            updateActiveButton('scenario', scenario);
        }

        // 选择难度
        function selectDifficulty(difficulty) {
            currentDifficulty = difficulty;
            updateActiveButton('difficulty', difficulty);
        }

        // 选择模式
        function selectMode(mode) {
            currentMode = mode;
            updateActiveButton('mode', mode);
        }

        // 切换关键词选择
        function toggleKeywordSelection() {
            const keywordSelection = document.getElementById('keywordSelection');
            const keywordToggleText = document.getElementById('keywordToggleText');
            
            if (keywordSelection.classList.contains('hidden')) {
                keywordSelection.classList.remove('hidden');
                keywordToggleText.textContent = '🔒 固定关键词';
            } else {
                keywordSelection.classList.add('hidden');
                keywordToggleText.textContent = '🎲 随机关键词';
            }
        }

        // 切换关键词分类
        function toggleKeywordCategory(category) {
            const index = selectedKeywordCategories.indexOf(category);
            if (index > -1) {
                selectedKeywordCategories.splice(index, 1);
            } else {
                selectedKeywordCategories.push(category);
            }
            
            // 更新按钮状态
            event.target.classList.toggle('active');
            
            // 更新显示
            updateSelectedKeywordsDisplay();
        }

        // 更新选中关键词显示
        function updateSelectedKeywordsDisplay() {
            const display = document.getElementById('selectedKeywordsDisplay');
            if (selectedKeywordCategories.length === 0) {
                display.innerHTML = '<small style="color: #6c757d;">已选择的关键词将显示在这里</small>';
                return;
            }
            
            let keywords = [];
            selectedKeywordCategories.forEach(category => {
                keywords = keywords.concat(optionalKeywords[category]);
            });
            
            selectedOptionalKeywords = keywords;
            
            display.innerHTML = `
                <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                    ${selectedOptionalKeywords.map(keyword => 
                        `<span class="keyword-tag" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">${keyword}</span>`
                    ).join('')}
                </div>
            `;
        }

        // 根据难度过滤模板
        function getTemplatesByDifficulty(difficulty) {
            return Object.values(realTemplateData).filter(template => {
                if (difficulty === '基础') {
                    return template.difficulty === '基础' || template.difficulty === '中级';
                } else if (difficulty === '中级') {
                    return template.difficulty === '中级' || template.difficulty === '高级';
                } else {
                    return template.difficulty === '高级';
                }
            });
        }

        // 更新按钮状态
        function updateActiveButton(type, value) {
            let selector = '';

            // 根据类型确定选择器
            switch(type) {
                case 'scenario':
                    selector = '.control-panel .control-group:nth-child(1) .btn';
                    break;
                case 'difficulty':
                    selector = '.control-panel .control-group:nth-child(2) .btn';
                    break;
                case 'mode':
                    selector = '.control-panel .control-group:nth-child(3) .btn';
                    break;
                default:
                    return;
            }

            // 更新按钮状态
            document.querySelectorAll(selector).forEach(btn => {
                btn.classList.remove('active');

                // 检查按钮是否匹配当前值
                const btnText = btn.textContent.trim();
                const onclick = btn.getAttribute('onclick') || '';

                if (btnText.includes(value) || onclick.includes(`'${value}'`)) {
                    btn.classList.add('active');
                }
            });
        }

        // 开始练习
        function startPractice() {
            stats.startTime = Date.now();
            const practiceArea = document.getElementById('practiceArea');
            
            // 生成练习内容
            const content = generatePracticeContent();
            practiceArea.innerHTML = content;
            
            // 更新统计
            stats.practiceCount++;
            updateStats();
        }

        // 生成练习内容（基于真实笔记数据）
        function generatePracticeContent() {
            // 根据难度获取合适的模板
            const availableTemplates = getTemplatesByDifficulty(currentDifficulty);
            if (availableTemplates.length === 0) {
                return '<p>没有找到适合当前难度的模板，请尝试其他难度等级。</p>';
            }
            
            // 随机选择一个模板
            const templateObj = availableTemplates[Math.floor(Math.random() * availableTemplates.length)];
            
            // 获取场景描述
            const scenarioDesc = getScenarioDescription(currentScenario);
            
            // 根据模式生成内容
            switch(currentMode) {
                case 'template':
                    return generateTemplateMode(templateObj.template, scenarioDesc.title, scenarioDesc.description);
                case 'keywords':
                    return generateKeywordsMode(scenarioDesc.title, scenarioDesc.description);
                case 'scenario':
                    return generateScenarioMode(scenarioDesc.title, scenarioDesc.description, getRandomKeywords(5));
                case 'correction':
                    return generateCorrectionMode(scenarioDesc.title, templateObj.template, scenarioDesc.description);
                default:
                    return '<p>未知的练习模式</p>';
            }
        }

        // 获取场景描述
        function getScenarioDescription(scenario) {
            const scenarios = {
                'meeting': {
                    title: '🤝 开会场景',
                    description: '在会议中讨论数据合规相关议题'
                },
                'compliance': {
                    title: '⚖️ 合规场景',
                    description: '处理数据保护和隐私合规事务'
                },
                'technical': {
                    title: '💻 技术场景',
                    description: '技术实施和安全措施讨论'
                },
                'legal': {
                    title: '📜 法律场景',
                    description: '法律条款解读和合规义务说明'
                },
                'random': {
                    title: '🎲 随机场景',
                    description: '随机生成的练习场景'
                }
            };
            
            return scenarios[scenario] || scenarios['random'];
        }

        // 获取随机关键词
        function getRandomKeywords(count) {
            // 如果用户选择了关键词类别，则使用选中的关键词
            if (selectedOptionalKeywords.length > 0) {
                // 从选中的关键词中随机选择
                const shuffled = [...selectedOptionalKeywords].sort(() => 0.5 - Math.random());
                return shuffled.slice(0, count);
            }
            
            // 否则从所有关键词中随机选择
            let allKeywords = [];
            Object.values(optionalKeywords).forEach(category => {
                allKeywords = allKeywords.concat(category);
            });
            
            const shuffled = [...allKeywords].sort(() => 0.5 - Math.random());
            return shuffled.slice(0, count);
        }

        // 模板填空模式
        function generateTemplateMode(template, title, scenario) {
            // 提取变量占位符
            const variables = template.match(/\[[^\]]+\]/g) || [];
            
            // 为每个占位符选择合适的值
            const filledTemplate = variables.reduce((temp, variable) => {
                const varName = variable.slice(1, -1); // 去掉方括号
                const options = variableDatabase[varName] || [varName];
                const selected = options[Math.floor(Math.random() * options.length)];
                return temp.replace(variable, `____${selected}____`);
            }, template);
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">📝 ${title} - 模板填空</div>
                    <p><strong>场景：</strong>${scenario}</p>
                </div>
                
                <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h4 style="color: #1976d2; margin-bottom: 15px;">📋 练习模板：</h4>
                    <p style="font-size: 1.2em; line-height: 1.8; font-weight: 500;">${filledTemplate}</p>
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✍️ 请填写完整的专业表达：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请填写完整的专业英语表达..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="evaluateResponse()" style="margin-right: 10px;">
                        📊 评估表达
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 关键词造句模式
        function generateKeywordsMode(title, scenario) {
            // 获取3-5个随机关键词
            const selectedKeywords = getRandomKeywords(Math.floor(Math.random() * 3) + 3);
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">🔤 ${title} - 关键词造句</div>
                    <p><strong>场景：</strong>${scenario}</p>
                </div>
                
                <div class="keywords-container">
                    <h4>🎯 必须使用的关键词：</h4>
                    ${selectedKeywords.map(k => `<span class="keyword-tag" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">${k}</span>`).join('')}
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✍️ 请使用上述关键词造一个完整的句子：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请确保使用所有给定的关键词..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="checkKeywordUsage()" style="margin-right: 10px;">
                        ✅ 检查关键词
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 情景对话模式
        function generateScenarioMode(title, scenario, keywords) {
            const situations = [
                "您需要向客户解释数据处理的法律依据",
                "监管机构询问您的合规措施",
                "技术团队需要了解安全要求",
                "董事会要求汇报合规状况",
                "与法律顾问讨论风险评估"
            ];
            const situation = situations[Math.floor(Math.random() * situations.length)];
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">🎬 ${title} - 情景对话</div>
                    <p><strong>场景：</strong>${scenario}</p>
                    <p><strong>情况：</strong>${situation}</p>
                </div>
                
                <div class="keywords-container">
                    <h4>💼 建议使用的专业词汇：</h4>
                    ${keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        🗣️ 请写出您的专业回应：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请用专业、准确的英语表达您的观点..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="evaluateResponse()" style="margin-right: 10px;">
                        📊 评估回应
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 错误纠正模式
        function generateCorrectionMode(title, template, scenario) {
            const incorrectSentences = [
                "Company must do encryption for protect data",
                "We delete user data when they ask us",
                "Data transfer to USA need permission from government",
                "User can access their data anytime they want",
                "We keep data as long as we need for business"
            ];
            const incorrect = incorrectSentences[Math.floor(Math.random() * incorrectSentences.length)];
            
            return `
                <div class="scenario-card">
                    <div class="scenario-title">✏️ ${title} - 错误纠正</div>
                    <p><strong>场景：</strong>${scenario}</p>
                </div>
                
                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">❌ 错误表达：</h4>
                    <p style="font-style: italic; color: #856404; font-size: 1.1em;">"${incorrect}"</p>
                </div>
                
                <div style="margin: 20px 0;">
                    <label for="practiceInput" style="font-weight: bold; margin-bottom: 10px; display: block;">
                        ✅ 请写出正确的专业表达：
                    </label>
                    <textarea id="practiceInput" class="practice-input" 
                              placeholder="请提供准确、专业的英语表达..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="checkCorrection()" style="margin-right: 10px;">
                        ✅ 检查纠正
                    </button>
                    <button class="btn btn-primary" onclick="startPractice()">
                        🔄 下一题
                    </button>
                </div>
            `;
        }

        // 检查关键词使用
        function checkKeywordUsage() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的句子！');
                return;
            }
            
            // 检查关键词使用情况
            const keywords = document.querySelectorAll('.keyword-tag');
            const usedKeywords = [];
            const missedKeywords = [];
            
            keywords.forEach(tag => {
                const keyword = tag.textContent.trim();
                if (input.toLowerCase().includes(keyword.toLowerCase())) {
                    usedKeywords.push(keyword);
                } else {
                    missedKeywords.push(keyword);
                }
            });
            
            showKeywordFeedback(usedKeywords, missedKeywords, input);
        }

        // 评估回应
        function evaluateResponse() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的回应！');
                return;
            }
            
            const score = evaluateAnswer(input);
            showDetailedFeedback(score, input);
        }

        // 检查纠正
        function checkCorrection() {
            const input = document.getElementById('practiceInput').value.trim();
            if (!input) {
                alert('请先输入您的纠正！');
                return;
            }
            
            const score = evaluateAnswer(input);
            showCorrectionFeedback(score, input);
        }

        // 评估答案
        function evaluateAnswer(input) {
            let score = 50; // 基础分
            
            // 长度检查
            if (input.length > 20) score += 10;
            if (input.length > 50) score += 10;
            
            // 专业词汇检查
            const professionalTerms = ['pursuant', 'accordance', 'implement', 'ensure', 'compliance', 'regulatory', 'appropriate', 'measures'];
            professionalTerms.forEach(term => {
                if (input.toLowerCase().includes(term)) score += 5;
            });
            
            // 语法结构检查
            if (input.includes('shall') || input.includes('must')) score += 10;
            if (input.includes('Article') || input.includes('GDPR') || input.includes('PIPL')) score += 10;
            
            return Math.min(100, score);
        }

        // 显示反馈
        function showFeedback(score, input) {
            let feedback = '';
            let color = '';
            
            if (score >= 90) {
                feedback = '🎉 优秀！您的表达非常专业和准确！';
                color = '#28a745';
            } else if (score >= 70) {
                feedback = '👍 很好！表达基本准确，可以继续改进。';
                color = '#ffc107';
            } else {
                feedback = '💪 继续努力！建议多使用专业术语和标准句式。';
                color = '#dc3545';
            }
            
            const feedbackDiv = document.createElement('div');
            feedbackDiv.style.cssText = `
                background: ${color}20;
                border: 2px solid ${color};
                color: ${color};
                padding: 15px;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: bold;
            `;
            feedbackDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>得分: ${score}/100</span>
                    <span>${feedback}</span>
                </div>
                <div style="margin-top: 10px; font-weight: normal; font-style: italic;">
                    " ${input} "
                </div>
            `;
            
            feedbackDiv.className = 'feedback';
            
            const practiceArea = document.getElementById('practiceArea');
            const existingFeedback = practiceArea.querySelector('.feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            practiceArea.appendChild(feedbackDiv);
        }

        // 显示关键词反馈
        function showKeywordFeedback(used, missed, input) {
            const feedbackDiv = document.createElement('div');
            let content = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: #495057;">🔤 关键词使用检查</h4>
                </div>
            `;
            
            if (used.length > 0) {
                content += `<p style="color: #28a745; margin: 10px 0;"><strong>✅ 已使用：</strong> ${used.join(', ')}</p>`;
            }
            
            if (missed.length > 0) {
                content += `<p style="color: #dc3545; margin: 10px 0;"><strong>❌ 未使用：</strong> ${missed.join(', ')}</p>`;
            }
            
            const score = Math.round((used.length / (used.length + missed.length)) * 100);
            content += `<p style="margin-top: 15px;"><strong>完成度：${score}%</strong></p>`;
            
            feedbackDiv.innerHTML = content;
            feedbackDiv.className = 'feedback';
            
            const practiceArea = document.getElementById('practiceArea');
            const existingFeedback = practiceArea.querySelector('.feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            practiceArea.appendChild(feedbackDiv);
        }

        // 显示详细反馈
        function showDetailedFeedback(score, input) {
            showFeedback(score, input);
        }

        // 显示纠正反馈
        function showCorrectionFeedback(score, input) {
            showFeedback(score, input);
        }

        // 显示提示
        function showHint() {
            const hints = [
                "💡 使用 'pursuant to' 或 'in accordance with' 来引用法律条款",
                "💡 使用 'shall' 而不是 'must' 来表达法律义务",
                "💡 包含具体的法律条款引用，如 'GDPR Article 32'",
                "💡 使用被动语态使表达更加正式",
                "💡 添加时间限制，如 'within 72 hours' 或 'without undue delay'"
            ];
            
            const hint = hints[Math.floor(Math.random() * hints.length)];
            alert(hint);
        }

        // 更新统计
        function updateStats() {
            document.getElementById('practiceCount').textContent = stats.practiceCount;
            
            const accuracy = stats.practiceCount > 0 ? 
                Math.round((stats.correctCount / stats.practiceCount) * 100) : 0;
            document.getElementById('accuracyRate').textContent = accuracy + '%';
            
            document.getElementById('streakCount').textContent = stats.streakCount;
            
            if (stats.startTime) {
                const minutes = Math.round((Date.now() - stats.startTime) / 60000);
                document.getElementById('totalTime').textContent = minutes;
            }
        }

        // 初始化函数
        function initializeApp() {
            // 设置默认按钮状态
            updateActiveButton('scenario', 'random');
            updateActiveButton('difficulty', '中级');
            updateActiveButton('mode', 'template');

            // 加载保存的统计数据
            const savedStats = localStorage.getItem('englishPracticeStats');
            if (savedStats) {
                stats = { ...stats, ...JSON.parse(savedStats) };
                updateStats();
            }

            // 定期保存统计数据
            setInterval(() => {
                localStorage.setItem('englishPracticeStats', JSON.stringify(stats));
            }, 30000);

            console.log('🎯 英语练习插件已初始化');
            console.log('当前设置:', { currentScenario, currentDifficulty, currentMode });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });
    </script>
</body>
</html>