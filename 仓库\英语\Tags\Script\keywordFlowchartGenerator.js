async function keywordFlowchartGenerator(token, keywords, outputFileName, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型
  
  if (!keywords || !token) {
    new Notice("请设置密钥和搜索关键词");
    return;
  }
  
  if (!outputFileName) {
    outputFileName = `${keywords}_流程图汇总`;
  }
  
  new Notice(`开始搜索关键词: ${keywords}`);
  
  // 搜索包含关键词的所有笔记
  const searchResults = [];
  const allFiles = app.vault.getMarkdownFiles();
  
  for (const file of allFiles) {
    try {
      const content = await app.vault.cachedRead(file);
      const title = file.basename;
      
      // 检查标题或内容是否包含关键词
      if (title.toLowerCase().includes(keywords.toLowerCase()) || 
          content.toLowerCase().includes(keywords.toLowerCase())) {
        
        searchResults.push({
          title: title,
          path: file.path,
          content: content,
          file: file
        });
      }
    } catch (error) {
      console.error(`读取文件失败 ${file.path}:`, error);
    }
  }
  
  if (searchResults.length === 0) {
    new Notice(`未找到包含关键词 "${keywords}" 的笔记`);
    return;
  }
  
  new Notice(`找到 ${searchResults.length} 个相关笔记，开始AI分析...`);
  
  // 准备AI分析的内容
  const notesContent = searchResults.map((note, index) => {
    return `## 笔记${index + 1}: ${note.title}
${note.content}

---`;
  }).join('\n\n');
  
  // AI提示语：分析笔记并生成流程图
  const analysisPrompt = `
作为一个专业的知识管理和流程分析专家，请分析以下关于"${keywords}"的多个笔记内容，并生成一个综合性的流程图。

搜索关键词: ${keywords}
找到的笔记数量: ${searchResults.length}

笔记内容:
${notesContent}

请按照以下要求进行分析和生成：

1. **内容分析**：
   - 识别所有笔记中与"${keywords}"相关的核心概念
   - 找出这些概念之间的逻辑关系和先后顺序
   - 提取关键的步骤、阶段或流程节点

2. **流程图生成**：
   - 使用Mermaid语法生成流程图
   - 流程图应该体现完整的逻辑链条
   - 包含决策点、并行流程、关键节点
   - 节点名称要简洁明确

3. **输出格式**：
请以JSON格式返回结果：
{
  "summary": "对所有笔记内容的综合总结",
  "key_concepts": ["概念1", "概念2", "概念3"],
  "mermaid_flowchart": "完整的Mermaid流程图代码",
  "process_steps": [
    {
      "step": "步骤名称",
      "description": "步骤描述",
      "related_notes": ["相关笔记标题"]
    }
  ],
  "insights": "从多个笔记中发现的新见解或模式"
}

只返回JSON格式的结果，不要包含其他说明文字。
`;

  // 调用AI API
  const analysisOptions = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: analysisPrompt,
        },
      ],
    }),
  };

  try {
    const response = await obsidian.requestUrl(analysisOptions);
    const result = response.json;
    
    if (result.choices.length === 0) {
      new Notice("AI分析失败，没有返回内容");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败，返回内容为空");
      return;
    }

    // 解析AI返回的JSON
    let analysisData;
    try {
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      analysisData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI返回格式错误，无法解析JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    // 生成汇总笔记内容
    const currentTime = new Date().toLocaleString('zh-CN');
    const summaryContent = `---
title: ${outputFileName}
keywords: ${keywords}
source_count: ${searchResults.length}
created: ${new Date().toISOString().split('T')[0]}
type: 流程图汇总
tags: #流程图 #汇总 #${keywords}
---

# ${outputFileName}

> 基于关键词"${keywords}"搜索到的${searchResults.length}个笔记的AI流程图汇总
> 生成时间: ${currentTime}

## 📊 搜索统计
- **搜索关键词**: ${keywords}
- **找到笔记数量**: ${searchResults.length}
- **生成时间**: ${currentTime}

## 📝 内容总结
${analysisData.summary || '暂无总结'}

## 🔑 核心概念
${analysisData.key_concepts ? analysisData.key_concepts.map(concept => `- ${concept}`).join('\n') : '- 暂无核心概念'}

## 🔄 流程图

\`\`\`mermaid
${analysisData.mermaid_flowchart || 'graph TD\n    A[开始] --> B[结束]'}
\`\`\`

## 📋 详细步骤

${analysisData.process_steps ? analysisData.process_steps.map((step, index) => `
### ${index + 1}. ${step.step}
${step.description}

**相关笔记**: ${step.related_notes ? step.related_notes.map(note => `[[${note}]]`).join(', ') : '无'}
`).join('\n') : '暂无详细步骤'}

## 💡 新发现的见解
${analysisData.insights || '暂无特殊见解'}

## 📚 源笔记列表

${searchResults.map((note, index) => `${index + 1}. [[${note.title}]]`).join('\n')}

---

## 🔗 相关链接
${searchResults.map(note => `- [[${note.title}]]`).join('\n')}

---
*本汇总由关键词流程图生成器自动创建*
*搜索关键词: ${keywords}*
*包含笔记: ${searchResults.length}个*
`;

    // 创建汇总笔记文件
    const fileName = `${outputFileName}.md`;
    let finalPath = fileName;
    let counter = 1;
    
    // 避免文件名冲突
    while (await app.vault.adapter.exists(finalPath)) {
      finalPath = `${outputFileName}_${counter}.md`;
      counter++;
    }
    
    await app.vault.create(finalPath, summaryContent);
    
    new Notice(`✅ 成功生成流程图汇总: ${finalPath}`);
    
    // 可选：打开生成的文件
    const createdFile = app.vault.getAbstractFileByPath(finalPath);
    if (createdFile) {
      await app.workspace.getLeaf().openFile(createdFile);
    }
    
  } catch (error) {
    new Notice("处理过程中发生错误，请查看控制台");
    console.error("关键词流程图生成错误:", error);
  }
}

exports.default = {
  entry: keywordFlowchartGenerator,
  name: "keywordFlowchartGenerator",
  description: `通过关键词搜索多个笔记，AI分析后生成综合流程图汇总

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥。==

  使用方法：

  \`keywordFlowchartGenerator('你的密钥', '搜索关键词', '输出文件名')\`

  示例：
  \`keywordFlowchartGenerator('your-api-key', '股权融资', '股权融资流程图')\`
  \`keywordFlowchartGenerator('your-api-key', '项目管理', '项目管理流程汇总')\`

  也可以指定其他付费模型：
  \`keywordFlowchartGenerator('你的密钥', '关键词', '文件名', 'glm-4-plus')\`

  功能特点：
  - 🔍 智能搜索：在所有笔记中搜索包含关键词的内容
  - 🤖 AI分析：深度分析多个笔记的内容和关联关系  
  - 📊 流程图生成：自动生成Mermaid格式的综合流程图
  - 📝 详细汇总：包含概念提取、步骤分解、见解发现
  - 🔗 链接建立：自动建立与源笔记的双向链接
  - 📋 结构化输出：生成完整的汇总笔记文档

  输出内容：
  - 搜索统计信息
  - 内容综合总结  
  - 核心概念提取
  - Mermaid流程图
  - 详细步骤分解
  - 新见解发现
  - 源笔记链接列表
  `,
};
