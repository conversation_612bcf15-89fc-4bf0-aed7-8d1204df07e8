{"components": [{"id": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-25T13:04:09.233Z", "updateAt": "2025-03-25T13:04:09.233Z", "components": [{"componentId": "1d80d70b-dce3-4ba5-a661-b743e067e3ec"}, {"componentId": "75dd5f5b-7da7-4120-8772-62d28ce041e0"}, {"componentId": "7091e6ee-07ab-43a5-9088-2720c5aeb0e4"}, {"componentId": "e55a5db2-cafa-4ff4-8da8-4a67c46426be"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "1d80d70b-dce3-4ba5-a661-b743e067e3ec", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "逻辑链", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "214.82034301757812", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "a9b1a3e0-80cd-474c-83f9-6edf390c5bde", "name": "Tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": false, "showAllTasks": false, "hideTaskFields": false, "timeRecordStyle": "tasks", "insertPosition": {"position": "TopOfNote", "headingLine": ""}, "prefix": "", "suffix": "", "totalValueType": "constant", "color": "components--color-none", "uiType": "progressRing", "total": 100}, "alias": "tasks"}, {"id": "1629486d-e1ea-4fa4-a326-b0452b162714", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "7d996d58-5159-4952-befb-60544507cee8", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "87"}, "alias": "auto-file"}, {"id": "f570c3ca-cccf-4b5e-883f-70eb588ede04", "name": "error_type", "isShow": false, "type": "text", "options": {}}, {"id": "2e85909c-4227-439f-a211-92179517fd87", "name": "前提条件", "isShow": false, "type": "multiSelect", "options": {"width": "624"}}, {"id": "81271658-7944-417a-b4fa-76dc734ae55b", "name": "要求内容", "isShow": false, "type": "multiSelect", "options": {}}, {"id": "7c69eb0e-2211-4078-8114-ac14adaa4b77", "name": "条件类型", "isShow": false, "type": "multiSelect", "options": {}}, {"id": "caff8cb6-8ee2-4b8f-90fc-b655f66f80fb", "name": "关系", "isShow": true, "type": "text", "options": {"width": "897"}}, {"id": "bac7fd11-50d0-45af-a8a1-16d9398a2819", "name": "类型", "isShow": true, "type": "text", "options": {"width": "143"}}, {"id": "fa18bc4e-60a7-41bb-abbe-22a7af5e9457", "name": "相关法律", "isShow": true, "type": "multiSelect", "options": {"width": "136"}}, {"id": "b30dce49-5e56-4277-bc9e-f9be4f77adcd", "name": "适用场景", "isShow": true, "type": "text", "options": {"width": "152"}}, {"id": "8ca744a5-82c9-4943-bdfa-a6345ffa1c52", "name": "关键文档", "isShow": true, "type": "text", "options": {}}, {"id": "479fc61f-451f-4e3f-a6b9-84f5e6fe03ba", "name": "人话", "isShow": true, "type": "text", "options": {}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 585}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "or", "conditions": [{"id": "94712956-c3e8-4d9d-a5d9-99c9181ee719", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "关系总结", "conditions": []}, {"id": "ae3e6179-467b-43f3-b397-9ef1a644d37e", "type": "filter", "operator": "has_value", "property": "关系", "value": null, "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}, {"id": "7091e6ee-07ab-43a5-9088-2720c5aeb0e4", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "CCPA", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "1107.7142944335938", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "a9b1a3e0-80cd-474c-83f9-6edf390c5bde", "name": "Tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": false, "showAllTasks": false, "hideTaskFields": false, "timeRecordStyle": "tasks", "insertPosition": {"position": "TopOfNote", "headingLine": ""}, "prefix": "", "suffix": "", "totalValueType": "constant", "color": "components--color-none", "uiType": "progressRing", "total": 100}, "alias": "tasks"}, {"id": "1629486d-e1ea-4fa4-a326-b0452b162714", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "7d996d58-5159-4952-befb-60544507cee8", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "87"}, "alias": "auto-file"}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "or", "conditions": [{"id": "1733a49a-f952-4c07-a9c1-1a98be81a0d9", "type": "filter", "operator": "contains", "property": "相关法律", "value": "CCPA", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352", "groupBy": "English type"}, {"id": "e55a5db2-cafa-4ff4-8da8-4a67c46426be", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "GDPR", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "493", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "5a18b4bb-2aef-4d2e-870e-d1d5385a271a", "name": "correct_expression", "isShow": false, "type": "text", "options": {}}, {"id": "325d525e-f78f-449c-ab76-bbb3a7e77c8f", "name": "usage_scenario", "isShow": false, "type": "text", "options": {}}, {"id": "c8e50ccc-bf2a-4dd0-9cf3-d6d5003f8861", "name": "common_errors", "isShow": true, "type": "text", "options": {}}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "82"}, "alias": "auto-file"}, {"id": "74716a8b-07ea-4613-bf37-81caab8988bd", "name": "frequency", "isShow": true, "type": "text", "options": {"width": "67"}}, {"id": "b6047932-1c50-4453-bc17-ac34596e4865", "name": "concept_type", "isShow": false, "type": "text", "options": {}}, {"id": "0f0ca5ad-48b9-400d-83aa-7d15a75c2579", "name": "difficulty", "isShow": false, "type": "text", "options": {"width": "81"}}, {"id": "75bf3a2c-7517-4fb6-bac8-f73fb6bdec85", "name": "error_type", "isShow": false, "type": "text", "options": {}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "or", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "contains", "property": "相关法律", "value": "GDPR", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe"}, {"id": "75dd5f5b-7da7-4120-8772-62d28ce041e0", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "决策树", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "460.71429443359375", "pinned": "left"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "a9b1a3e0-80cd-474c-83f9-6edf390c5bde", "name": "Tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": false, "showAllTasks": false, "hideTaskFields": false, "timeRecordStyle": "tasks", "insertPosition": {"position": "TopOfNote", "headingLine": ""}, "prefix": "", "suffix": "", "totalValueType": "constant", "color": "components--color-none", "uiType": "progressRing", "total": 100}, "alias": "tasks"}, {"id": "1629486d-e1ea-4fa4-a326-b0452b162714", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "7d996d58-5159-4952-befb-60544507cee8", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "87"}, "alias": "auto-file"}, {"id": "f570c3ca-cccf-4b5e-883f-70eb588ede04", "name": "error_type", "isShow": false, "type": "text", "options": {}}, {"id": "2e85909c-4227-439f-a211-92179517fd87", "name": "前提条件", "isShow": false, "type": "multiSelect", "options": {"width": "624"}}, {"id": "81271658-7944-417a-b4fa-76dc734ae55b", "name": "要求内容", "isShow": false, "type": "multiSelect", "options": {}}, {"id": "7c69eb0e-2211-4078-8114-ac14adaa4b77", "name": "条件类型", "isShow": false, "type": "multiSelect", "options": {}}, {"id": "caff8cb6-8ee2-4b8f-90fc-b655f66f80fb", "name": "关系", "isShow": true, "type": "text", "options": {"width": "897"}}, {"id": "bac7fd11-50d0-45af-a8a1-16d9398a2819", "name": "类型", "isShow": false, "type": "text", "options": {"width": "79"}}, {"id": "fa18bc4e-60a7-41bb-abbe-22a7af5e9457", "name": "相关法律", "isShow": false, "type": "multiSelect", "options": {"width": "136"}}, {"id": "b30dce49-5e56-4277-bc9e-f9be4f77adcd", "name": "适用场景", "isShow": false, "type": "text", "options": {"width": "152"}}, {"id": "8ca744a5-82c9-4943-bdfa-a6345ffa1c52", "name": "关键文档", "isShow": false, "type": "text", "options": {}}, {"id": "1e0adcf6-08e6-461c-8204-ba527fea9484", "name": "人话", "isShow": true, "type": "text", "options": {"width": "581"}}, {"id": "bfdae31c-b140-4ab2-8b2b-7ce7aad15b5c", "name": "决策树", "isShow": true, "type": "checkbox", "options": {}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 585}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "or", "conditions": [{"id": "ae3e6179-467b-43f3-b397-9ef1a644d37e", "type": "filter", "operator": "checked", "property": "决策树", "value": "", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}], "rootComponentId": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d"}