---
title: "数据合规英语中的逻辑简洁性"
source: "[[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]"
tags: ["数据合规", "逻辑简洁性", "专业表述"]
keywords: ["简洁性", "专业", "表述"]
created: 2025-07-31
type: 原子笔记
---

# 数据合规英语中的逻辑简洁性

避免口语化同义替换，例如将'get'替换为'process'，'collect'替换为'further process'，保持表述的专业性和准确性。

---

## 元信息
- **来源笔记**: [[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]
- **创建时间**: 2025/8/1 04:34:09
- **标签**: #数据合规 #逻辑简洁性 #专业表述
- **关键词**: 简洁性, 专业, 表述

## 相关链接
- 返回原笔记: [[数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」]]
