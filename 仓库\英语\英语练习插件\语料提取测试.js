// 测试智能语料提取器 - 基于您的080209.md笔记
async function testCorpusExtraction() {
  // 模拟您的笔记内容
  const sampleNoteContent = `---
练习日期: 2025-08-02
练习时间: 10:03
流利程度: 卡壳
练习感受: 一般
遇到的问题: ""
改进点: ""
场景来源: "[[合同续签合规对接]]"
练习类型: 口语练习
综合评分: 4
评价: 😕
评价时间: 2025/8/2 10:03:26
---
# 080209

### 修正后的正确表达：
**"Data breach is an important issue in today's world."**  
（数据泄露是当今世界的一个重要问题。）

### 更自然的表达建议：
如果想更地道，可以写成：  
- **"Data breaches are a critical issue in today's world."**  
  （用复数 "breaches" 表示普遍现象，"critical" 比 "important" 语气更强。）  
- **"Data security breaches pose a major challenge in the modern world."**  
  （更正式，强调 "挑战"。）

希望这对您有帮助！如果有其他问题，欢迎继续提问。`;

  console.log("🧪 开始测试智能语料提取...");
  
  // 分析笔记内容
  const analysis = await analyzeOralPracticeNote(sampleNoteContent, "080209");
  
  console.log("📊 分析结果:", analysis);
  
  // 显示提取的语料
  console.log("\n📚 提取的语料:");
  analysis.extractedCorpus.forEach((corpus, index) => {
    console.log(`${index + 1}. "${corpus.english}" [${corpus.type}] - ${corpus.difficulty}`);
  });
  
  // 显示识别的问题
  console.log("\n⚠️ 识别的问题:");
  analysis.identifiedProblems.forEach((problem, index) => {
    console.log(`${index + 1}. ${problem}`);
  });
  
  // 显示改进建议
  console.log("\n💡 改进建议:");
  analysis.suggestedImprovements.forEach((improvement, index) => {
    console.log(`${index + 1}. ${improvement}`);
  });
  
  // 生成示例语料笔记内容
  if (analysis.extractedCorpus.length > 0) {
    console.log("\n📝 示例语料笔记内容:");
    const sampleCorpus = analysis.extractedCorpus[0];
    const corpusContent = generateCorpusContent(sampleCorpus, analysis, "080209", "2025-08-02", "10:03");
    console.log(corpusContent);
  }
  
  return analysis;
}

// 分析口语练习笔记（简化版用于测试）
async function analyzeOralPracticeNote(content, fileName) {
  // 提取YAML前置数据
  const yamlMatch = content.match(/^---\n([\s\S]*?)\n---/);
  let yamlData = {};
  
  if (yamlMatch) {
    const yamlContent = yamlMatch[1];
    yamlContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split(':');
      if (key && valueParts.length > 0) {
        const value = valueParts.join(':').trim().replace(/^["']|["']$/g, '');
        yamlData[key.trim()] = value;
      }
    });
  }
  
  // 提取正文内容
  const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');
  
  const analysis = {
    fileName: fileName,
    yamlData: yamlData,
    extractedCorpus: [],
    identifiedProblems: [],
    suggestedImprovements: [],
    originalExpressions: [],
    correctedExpressions: []
  };
  
  // 1. 提取修正后的表达
  const correctedMatches = mainContent.match(/修正后的正确表达[：:]\s*\*\*"([^"]+)"\*\*/g);
  if (correctedMatches) {
    correctedMatches.forEach(match => {
      const expression = match.match(/"([^"]+)"/)[1];
      analysis.extractedCorpus.push({
        english: expression,
        type: "修正表达",
        difficulty: determineDifficulty(expression),
        context: "语法修正"
      });
    });
  }
  
  // 2. 提取更自然的表达建议
  const naturalExpressions = mainContent.match(/\*\*"([^"]+)"\*\*/g);
  if (naturalExpressions) {
    naturalExpressions.forEach(match => {
      const expression = match.match(/"([^"]+)"/)[1];
      if (!analysis.extractedCorpus.some(e => e.english === expression)) {
        analysis.extractedCorpus.push({
          english: expression,
          type: "建议表达",
          difficulty: determineDifficulty(expression),
          context: "自然表达"
        });
      }
    });
  }
  
  // 3. 分析问题
  analysis.identifiedProblems = analyzeProblems(mainContent, yamlData);
  
  // 4. 分析改进建议
  analysis.suggestedImprovements = analyzeSuggestedImprovements(mainContent, yamlData);
  
  return analysis;
}

// 分析问题
function analyzeProblems(content, yamlData) {
  const problems = [];
  
  // 从流利程度判断
  if (yamlData['流利程度'] === '卡壳') {
    problems.push("表达不够流利，存在卡壳现象");
  }
  
  // 从评分判断
  const score = parseInt(yamlData['综合评分']);
  if (score <= 4) {
    problems.push("整体表达质量需要提升");
  }
  
  // 从内容分析
  if (content.includes('修正后')) {
    problems.push("原始表达存在语法或用词错误");
  }
  
  if (content.includes('单数') || content.includes('复数')) {
    problems.push("单复数使用不当");
  }
  
  if (content.includes('important') && content.includes('critical')) {
    problems.push("词汇选择不够精准，需要使用更强烈的表达");
  }
  
  return problems;
}

// 分析改进建议
function analyzeSuggestedImprovements(content, yamlData) {
  const improvements = [];
  
  if (content.includes('更地道')) {
    improvements.push("学习更地道的英语表达方式");
  }
  
  if (content.includes('更正式')) {
    improvements.push("掌握正式场合的专业表达");
  }
  
  if (content.includes('critical') && content.includes('important')) {
    improvements.push("学会使用更强烈的形容词来增强表达力度");
  }
  
  if (content.includes('复数') && content.includes('普遍现象')) {
    improvements.push("注意使用复数形式表达普遍性概念");
  }
  
  // 根据场景来源提供建议
  if (yamlData['场景来源']) {
    improvements.push(`针对${yamlData['场景来源']}场景进行专项练习`);
  }
  
  // 根据评分提供建议
  const score = parseInt(yamlData['综合评分']);
  if (score <= 4) {
    improvements.push("建议增加基础语法和词汇练习");
    improvements.push("多进行口语流畅度训练");
  }
  
  return improvements;
}

// 判断难度等级
function determineDifficulty(expression) {
  const wordCount = expression.split(' ').length;
  const hasComplexWords = /\b(critical|challenge|security|compliance|breach)\b/i.test(expression);
  const hasComplexStructure = /\b(pose|in today's world|modern world)\b/i.test(expression);
  
  if (wordCount <= 4 && !hasComplexWords) return 'basic';
  if (wordCount <= 8 && (hasComplexWords || hasComplexStructure)) return 'intermediate';
  return 'advanced';
}

// 生成语料笔记内容
function generateCorpusContent(corpus, analysis, practiceFileName, fullDate, time) {
  const translation = generateTranslation(corpus.english);
  const meaning = generateMeaning(corpus, analysis);
  const errorPoint = generateErrorPoint(corpus, analysis);
  
  return `---
记录日期: "${fullDate}"
记录时间: "${time}"
翻译: "${translation}"
具体意义: "${meaning}"
出错点: "${errorPoint}"
语料类型: "${corpus.type}"
难度等级: "${corpus.difficulty}"
来源练习: "[[${practiceFileName}]]"
---

# ${corpus.english}

## 📝 语料详情

**原文**: ${corpus.english}
**翻译**: ${translation}
**类型**: ${corpus.type}
**难度**: ${corpus.difficulty}

## 💡 具体意义

${meaning}

## ⚠️ 学习要点

${errorPoint}

## 🎯 使用场景

${corpus.context || '专业商务沟通'}

## 📚 相关表达

${generateRelatedExpressions(corpus.english)}

---
来源练习: [[${practiceFileName}]]
`;
}

// 生成翻译
function generateTranslation(english) {
  const translations = {
    'data breach is an important issue in today\'s world': '数据泄露是当今世界的一个重要问题',
    'data breaches are a critical issue in today\'s world': '数据泄露是当今世界的一个关键问题',
    'data security breaches pose a major challenge in the modern world': '数据安全漏洞在现代世界中构成重大挑战',
    'data breach': '数据泄露',
    'critical issue': '关键问题',
    'major challenge': '重大挑战'
  };
  
  return translations[english.toLowerCase()] || '待翻译';
}

// 生成具体意义
function generateMeaning(corpus, analysis) {
  if (corpus.english.includes('data breach')) {
    return '数据泄露是网络安全领域的核心概念，指个人或敏感信息被未授权访问、披露或获取的安全事件。在商务沟通中经常需要讨论此类安全问题。';
  }
  
  if (corpus.english.includes('critical issue')) {
    return '这是一个强调严重程度的表达，"critical"比"important"更有紧迫感，适用于需要立即关注和解决的重要问题。';
  }
  
  return `这是一个${corpus.difficulty}级别的${corpus.type}，在专业场合中使用频率较高。`;
}

// 生成错误点
function generateErrorPoint(corpus, analysis) {
  let errorPoint = '';
  
  if (corpus.english.includes('breach is') && analysis.extractedCorpus.some(c => c.english.includes('breaches are'))) {
    errorPoint += '注意单复数的使用：谈论普遍现象时应使用复数形式"breaches are"。';
  }
  
  if (corpus.type === '修正表达') {
    errorPoint += '这是对原始错误表达的修正，需要重点记忆正确形式。';
  }
  
  return errorPoint || '注意在正式场合使用，确保语法和用词准确。';
}

// 生成相关表达
function generateRelatedExpressions(english) {
  if (english.includes('data breach')) {
    return `• data leak (数据泄漏)
• security incident (安全事件)
• privacy violation (隐私违规)
• cyber attack (网络攻击)`;
  }
  
  if (english.includes('critical issue')) {
    return `• major concern (主要关切)
• serious problem (严重问题)
• urgent matter (紧急事项)
• pressing issue (紧迫问题)`;
  }
  
  return '• 相关表达待补充';
}

// 运行测试
console.log("🚀 启动语料提取测试...");
testCorpusExtraction().then(result => {
  console.log("✅ 测试完成！");
}).catch(error => {
  console.error("❌ 测试失败:", error);
});
