---
人话: "DPA：合同，规定“你只能帮我搬砖，不能偷我的砖”（责任划分）。\r\rDPIA：报告，证明“我设计的砖房不会塌”（技术方案验证）因为它要回答三个关键问题：\r✅ 能不能干？✅ 怎么安全地干？✅ 干砸了怎么办？"
关系: "没DPIA：直接上线 → 结果人脸数据被黑客盗取 → 罚款+信誉崩塌。\r\r有DPIA：\r\r提前发现风险：人脸是敏感数据，需加密存储；\r\r制定方案：改用本地存储（不传云端）+ 定期删除；\r\r结论：方案可行，但必须加这些保护措施。"
决策树: true
---
用最直白的话解释 **DPIA（数据保护影响评估）** 为什么是“可行性报告”：

```mermaid
flowchart TD
    A[启动数据处理项目] --> B{是否高风险？<br>（敏感数据/AI决策/跨境传输）}
    B -->|是| C[必须做DPIA]
    B -->|否| D[无需DPIA]
    C --> E[评估内容：<br>1. 数据有哪些？<br>2. 可能泄露吗？<br>3. 如何保护？]
    E --> F[提交监管机构或客户]
    F --> G[通过后实施]
```


---

### **1. 本质是“风险预演”**

想象你要在河边盖房子，DPIA就是提前回答：

- **洪水来了会淹吗？**（数据会泄露吗？）
    
- **地基够稳吗？**（加密措施够强吗？）
    
- **逃生通道在哪？**（泄露后怎么补救？）
    

**DPIA就是让你证明：我的数据处理方案不会“淹死”用户隐私。**

---

### **2. 为什么叫“可行性”报告？**

因为它要回答三个关键问题：  
✅ **能不能干？**  
（例如：用AI分析患者健康数据风险太高，可能直接否决项目。）  
✅ **怎么安全地干？**  
（例如：必须匿名化数据 + 服务器放在本地。）  
✅ **干砸了怎么办？**  
（例如：泄露后2小时内通知用户，并准备赔偿方案。）

**相当于向监管机构/客户证明：这活儿我盘算过了，能接！**

---

### **3. 实际案例对比**

#### **场景：开发一个人脸识别考勤系统**

- **没DPIA**：直接上线 → 结果人脸数据被黑客盗取 → 罚款+信誉崩塌。
    
- **有DPIA**：
    
    - 提前发现风险：人脸是敏感数据，需加密存储；
        
    - 制定方案：改用本地存储（不传云端）+ 定期删除；
        
    - 结论：**方案可行，但必须加这些保护措施**。
        

**DPIA就是逼你“先穿护具再打架”。**

---

### **4. 和DPA的区别**

- **DPA**：合同，规定“你只能帮我搬砖，不能偷我的砖”（责任划分）。
    
- **DPIA**：报告，证明“我设计的砖房不会塌”（技术方案验证）。
    

**简单说**：

- DPA管“谁能干”，DPIA管“怎么干才安全”。
    

如果需要看具体DPIA报告模板（比如怎么写“风险点”），告诉我！