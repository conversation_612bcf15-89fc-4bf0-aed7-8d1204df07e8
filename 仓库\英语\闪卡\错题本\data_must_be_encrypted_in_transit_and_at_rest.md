---
title: "Data Encrypted in Transit and at Rest"
correct_expression: "data must be encrypted in transit and at rest"
usage_scenario: "安全措施场景 - 加密要求"
common_errors: "data must be encrypted by transit and by rest"
error_type: "介词搭配错误"
tags: ["介词搭配", "数据加密", "传输加密", "静态加密"]
difficulty: "中级"
frequency: "高频"
---

# data_must_be_encrypted_in_transit_and_at_rest

## 正确专业表达
**"data must be encrypted in transit and at rest"**

### 详细说明
- **错误原因**: 混淆了状态介词，"in/at"表示状态，"by"表示方式
- **正确用法**: "in transit"表示传输状态，"at rest"表示静止状态
- **注意事项**: 这是数据保护的两种基本状态

### 专业例句
"Data must be encrypted **in** transit and **at** rest."

### 相关例句
- "All personal data is protected in transit through TLS encryption."
- "Database files are encrypted at rest using AES-256."
- "Security policies require encryption both in transit and at rest."

### 记忆要点
- "in transit"表示传输中的状态
- "at rest"表示静止存储的状态
- 这是数据保护的标准术语

### 相关搭配
- in motion
- in processing
- at storage
- at backup
