async function atomicNoteSplitter(token, outputFolderName, modelType) {
  const model = modelType || "GLM-4-Flash"; // 智谱清言模型，GLM-4-Flash 是一个免费模型
  
  if (!outputFolderName || !token) {
    new Notice("请设置密钥或输出文件夹名");
    return;
  }
  
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 创建输出文件夹
  const outputFolder = outputFolderName || "原子笔记";
  try {
    await app.vault.createFolder(outputFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }
  
  // AI提示语：让AI帮助识别和切分原子笔记
  const analysisPrompt = `
作为一个专业的知识管理专家，请分析以下笔记内容，将其切分成独立的原子笔记。

笔记标题：${title}

笔记内容：
${fileContent || ""}

请按照以下要求进行切分：
1. 每个原子笔记应该包含一个独立、完整的知识点
2. 原子笔记应该具有独立性，可以单独理解和使用
3. 每个原子笔记的内容长度应该适中（100-500字）
4. 保留原文的核心信息，不要遗漏重要内容
5. 为每个原子笔记生成一个简洁明确的标题

请以JSON格式返回结果，格式如下：
{
  "atomic_notes": [
    {
      "title": "原子笔记标题",
      "content": "原子笔记内容（保持markdown格式）",
      "tags": ["标签1", "标签2"],
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}

只返回JSON格式的结果，不要包含其他说明文字。
`;

  // 调用AI API进行内容分析
  const analysisOptions = {
    method: "POST",
    url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: analysisPrompt,
        },
      ],
    }),
  };

  new Notice("正在分析笔记内容...");
  
  try {
    const response = await obsidian.requestUrl(analysisOptions);
    const result = response.json;
    
    if (result.choices.length === 0) {
      new Notice("AI分析失败，没有返回内容");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败，返回内容为空");
      return;
    }

    // 解析AI返回的JSON
    let atomicNotesData;
    try {
      // 清理可能的markdown代码块标记
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      atomicNotesData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI返回格式错误，无法解析JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI返回数据格式不正确");
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`识别到 ${atomicNotes.length} 个原子笔记，开始创建文件...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      // 生成安全的文件名
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      // 生成原子笔记内容
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      // 处理标签和关键词的格式
      const tagsStr = tags.length > 0 ? `[${tags.map(tag => `"${tag}"`).join(', ')}]` : '[]';
      const keywordsStr = keywords.length > 0 ? `[${keywords.map(keyword => `"${keyword}"`).join(', ')}]` : '[]';

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: ${tagsStr}
keywords: ${keywordsStr}
created: ${currentTime}
type: 原子笔记
---

# ${note.title}

${note.content}

---

## 元信息
- **来源笔记**: [[${title}]]
- **创建时间**: ${new Date().toLocaleString('zh-CN')}
- **标签**: ${tags.length > 0 ? tags.map(tag => `#${tag}`).join(' ') : '无'}
- **关键词**: ${keywords.length > 0 ? keywords.join(', ') : '无'}

## 相关链接
- 返回原笔记: [[${title}]]
`;

      try {
        // 检查文件是否已存在，如果存在则添加序号
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    // 创建索引文件
    const indexContent = `# ${title} - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: ${new Date().toLocaleString('zh-CN')}
> 原始笔记: [[${title}]]

## 统计信息
- 原始笔记: [[${title}]]
- 切分出的原子笔记数量: ${createdCount}
- 生成时间: ${new Date().toLocaleString('zh-CN')}

## 原子笔记列表

${atomicNotes.map((note, index) => {
  if (!note.title || !note.content) return '';
  const safeTitle = note.title
    .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
    .replace(/[_\s]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 50);
  return `${index + 1}. [[${safeTitle}]] - ${note.title}`;
}).filter(item => item).join('\n')}

## 标签分类

${(() => {
  const tagGroups = {};
  atomicNotes.forEach(note => {
    if (note.tags) {
      note.tags.forEach(tag => {
        if (!tagGroups[tag]) tagGroups[tag] = [];
        const safeTitle = note.title
          .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
          .replace(/[_\s]+/g, '_')
          .replace(/^_+|_+$/g, '')
          .substring(0, 50);
        tagGroups[tag].push(`[[${safeTitle}]]`);
      });
    }
  });
  
  return Object.entries(tagGroups)
    .map(([tag, notes]) => `### #${tag}\n${notes.map(note => `- ${note}`).join('\n')}`)
    .join('\n\n');
})()}

---
*此索引文件由原子笔记切分工具生成*
`;

    try {
      const indexPath = `${outputFolder}/${title}_原子笔记索引.md`;
      await app.vault.create(indexPath, indexContent);
    } catch (indexError) {
      console.error("创建索引文件失败:", indexError);
    }

    new Notice(`✅ 成功创建 ${createdCount} 个原子笔记！`);
    
  } catch (error) {
    new Notice("处理过程中发生错误，请查看控制台");
    console.error("原子笔记切分错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitter,
  name: "atomicNoteSplitter",
  description: `通过智谱清言的 API 将当前笔记智能切分成原子笔记（默认使用免费模型 GLM-4-Flash）

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥。==

  使用方法：

  \`atomicNoteSplitter('你的密钥', '输出文件夹名')\`

  也可以指定其他付费模型：

  \`atomicNoteSplitter('你的密钥', '输出文件夹名', 'glm-4-plus')\`

  功能特点：
  - 🤖 AI智能识别知识点，自动切分原子笔记
  - 📝 保持原文核心信息，确保内容完整性
  - 🏷️ 自动生成标签和关键词
  - 📁 创建结构化的文件夹和索引
  - 🔗 建立原子笔记与原始笔记的链接关系
  - 📊 生成详细的索引和统计信息
  
  输出结构：
  - 原子笔记文件（包含frontmatter元数据）
  - 索引文件（按标签分类）
  - 自动建立双向链接关系
  `,
};
