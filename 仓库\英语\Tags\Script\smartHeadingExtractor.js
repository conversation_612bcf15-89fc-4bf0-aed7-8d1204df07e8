async function smartHeadingExtractor(heading) {
  const { currentFile } = this;
  const file = currentFile;
  const cache = app.metadataCache.getFileCache(file);
  const headings = cache?.headings || [];
  const expectedHeading = heading || "";

  // 查找匹配的标题
  let match = -1;
  for (let i = 0; i < headings.length; i++) {
    const h = headings[i];
    const sharps = "#".repeat(h.level);
    const head = `${sharps} ${h.heading}`;
    if (head === expectedHeading) {
      match = i;
      break;
    }
  }
  if (match === -1) return "";

  const fileContent = await app.vault.cachedRead(file);
  const fileContentLines = fileContent.split("\n");
  
  if (match >= 0) {
    const nextHeading = match + 1 < headings.length ? headings[match + 1] : null;
    const matchLine = headings[match].position.start.line;
    const endLine = nextHeading ? nextHeading.position.start.line : fileContentLines.length;
    
    let contentLines = [];

    for (let i = matchLine + 1; i < endLine; i++) {
      const originalLine = fileContentLines[i];
      const line = originalLine.trim();
      const indent = originalLine.search(/\S/); // 获取原始缩进
      
      if (!line) continue;
      
      const isList = /^[-*+] |^\d+\. /.test(line);
      
      if (isList && indent === 0) {
        // 只处理顶级列表（无缩进）
        const cleanLine = line.replace(/^[-*+] |^\d+\. /, "");
        
        // 根据标签确定符号（新增功能）
        let symbol = "◾"; // 默认符号
        if (line.includes("#科研")) {
          symbol = "⚙";
        } else if (line.includes("#日常")) {
          symbol = "🌷";
        } else if (line.includes("#个人")) {
          symbol = "💭";
 	} else if (line.includes("#工作")) {
          symbol = "🐎";

        }
        
        contentLines.push(`${symbol} ${cleanLine}`);
      } else if (indent === 0 && !isList) {
        // 非列表的顶级内容
        contentLines.push(line);
      }
      // 忽略所有缩进内容（包括子列表）
    }

    // 创建渲染容器
    const res = contentLines.join("\n");
    const el = document.createElement("div");
    
    // 添加无缩进样式
    el.style.cssText = `
      margin: 0;
      padding: 0;
      list-style: none;
    `;
    
    // 渲染Markdown为HTML
    await obsidian.MarkdownRenderer.render(app, res, el, "", null);
    
    // 强制移除所有缩进
    el.querySelectorAll("*").forEach(e => {
      e.style.margin = "0";
      e.style.padding = "0";
      e.style.marginLeft = "0";
      e.style.paddingLeft = "0";
    });
    
    const innerHTML = el.innerHTML;
    el.remove();
    return innerHTML;
  }
  return "";
}

exports.default = {
  name: "smartHeadingExtractor",
  description: `提取指定标题下的文本内容（不包含标题和子列表）

  使用方法:
  \`\`\`js
  smartHeadingExtractor('## 你的标题')
  \`\`\`
  
  返回内容将:
  - 不包含标题本身
  - 只处理顶级列表项（无缩进的内容）
  - 移除列表标记后根据标签添加不同符号:
    * 无tag: ◾
    * #科研: ⚙
    * #生活: 🌷
    * #个人: 💭
    * #工作: 🐎
  - 完全跳过所有子列表
  - 非列表内容原样保留
  - 保持原始HTML输出格式
      `,
  entry: smartHeadingExtractor,
};