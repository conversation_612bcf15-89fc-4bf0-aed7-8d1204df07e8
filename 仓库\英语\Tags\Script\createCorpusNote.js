// AI增强版语料笔记创建器
async function createCorpusNote(token, corpusTitle) {
  if (!token) {
    new Notice("❌ 请传入AI密钥参数");
    return;
  }

  const { currentFile } = this;
  const file = currentFile;
  const practiceFileName = file.basename;

  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !practiceFileName.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }

  // 如果没有提供语料标题，询问用户输入（AI增强版）
  let corpusData;
  if (!corpusTitle) {
    corpusData = await showAICorpusInputModal();
    if (!corpusData) {
      new Notice("已取消创建语料笔记");
      return;
    }
    corpusTitle = corpusData.title;
  } else {
    // 如果直接提供了标题，其他字段为空，让AI填写
    corpusData = {
      title: corpusTitle,
      errorPoint: ""
    };
  }

  try {
    // 显示AI处理提示
    new Notice("🤖 AI正在分析语料...", 2000);

    // 调用AI分析语料
    const aiAnalysis = await analyzeCorpusWithAI(token, corpusData.title, file);

    if (aiAnalysis) {
      corpusData.translation = aiAnalysis.translation;
      corpusData.meaning = aiAnalysis.meaning;
      corpusData.examples = aiAnalysis.examples;
      corpusData.exampleTranslations = aiAnalysis.exampleTranslations;
      // 保留用户输入的易错点，如果没有则使用AI的
      if (!corpusData.errorPoint) {
        corpusData.errorPoint = aiAnalysis.errorPoint;
      }
    }

  } catch (error) {
    new Notice(`❌ AI创建语料笔记失败: ${error.message}`);
    console.error("AI语料笔记创建错误:", error);
    return;
  }
  
  // 获取当前日期信息
  const today = new Date();
  const fullDateString = today.toISOString().split('T')[0]; // 2025-08-01格式
  const timeString = today.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit' 
  }); // HH:mm格式
  
  // 创建语料笔记文件名（使用用户输入的语料标题）
  const safeCorpusTitle = corpusTitle
    .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
    .replace(/[_\s]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 30); // 限制长度
  
  const corpusFileName = `${safeCorpusTitle}.md`;
  const corpusFolder = "英语/口语训练/语料卡";
  const corpusFilePath = `${corpusFolder}/${corpusFileName}`;
  
  // 检查文件是否已存在
  if (await app.vault.adapter.exists(corpusFilePath)) {
    const userChoice = await showFileExistsModal(corpusFileName);
    if (userChoice === 'cancel') {
      new Notice("已取消创建语料笔记");
      return;
    } else if (userChoice === 'new') {
      // 创建新版本，添加日期后缀
      const dateStamp = practiceFileName.substring(0, 6); // 取练习笔记的日期
      const newFileName = `${safeCorpusTitle}_${dateStamp}.md`;
      const newFilePath = `${corpusFolder}/${newFileName}`;
      await createCorpusFile(newFilePath, newFileName, corpusData, practiceFileName, fullDateString, timeString);
      return;
    }
    // 如果选择覆盖，继续执行
  }

  // 创建语料笔记文件
  await createCorpusFile(corpusFilePath, corpusFileName, corpusData, practiceFileName, fullDateString, timeString);
}

// 创建语料笔记文件
async function createCorpusFile(filePath, fileName, corpusData, practiceFileName, fullDate, time) {
  // 生成语料笔记内容（包含例句）
  const examples = corpusData.examples || [
    `This ${corpusData.title} is very important.`,
    `We need to consider this ${corpusData.title} carefully.`
  ];

  const exampleTranslations = corpusData.exampleTranslations || [
    `这个${corpusData.translation || '表达'}非常重要。`,
    `我们需要仔细考虑这个${corpusData.translation || '表达'}。`
  ];

  // 处理属性值，确保YAML格式正确
  const safeTranslation = (corpusData.translation || '待翻译').replace(/"/g, '\\"').replace(/\n/g, ' ');
  const safeMeaning = (corpusData.meaning || '待补充').replace(/"/g, '\\"').replace(/\n/g, ' ');
  const safeErrorPoint = (corpusData.errorPoint || '待补充').replace(/"/g, '\\"').replace(/\n/g, ' ');

  const corpusContent = `---
记录日期: "${fullDate}"
记录时间: "${time}"
翻译: "${safeTranslation}"
具体意义: "${safeMeaning}"
出错点: "${safeErrorPoint}"
语料类型: "用户创建"
难度等级: "intermediate"
提取方式: "AI增强创建"
来源练习: "[[${practiceFileName}]]"
---

# ${corpusData.title}

## 📚 例句

### 例句1
**英文**: ${examples[0]}
**中文**: ${exampleTranslations[0]}

### 例句2
**英文**: ${examples[1]}
**中文**: ${exampleTranslations[1]}

---
来源练习: [[${practiceFileName}]]
`;

  try {
    // 确保文件夹存在
    const corpusFolder = "英语/口语训练/语料卡";
    try {
      await app.vault.createFolder(corpusFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 创建语料笔记文件
    await app.vault.create(filePath, corpusContent);
    
    new Notice(`✅ 语料笔记已创建: ${fileName}`);
    
    // 打开新创建的文件
    const createdFile = app.vault.getAbstractFileByPath(filePath);
    if (createdFile) {
      await app.workspace.getLeaf().openFile(createdFile);
    }
    
    // 在原练习笔记中添加语料链接
    await addCorpusLinkToPractice(practiceFileName, fileName.replace('.md', ''));
    
  } catch (error) {
    new Notice(`❌ 创建语料笔记失败: ${error.message}`);
    console.error("创建语料笔记错误:", error);
  }
}

// 在练习笔记中添加语料链接
async function addCorpusLinkToPractice(practiceFileName, corpusFileName) {
  try {
    // 查找练习笔记文件
    const allFiles = app.vault.getMarkdownFiles();
    const practiceFile = allFiles.find(f => f.basename === practiceFileName);
    
    if (!practiceFile) {
      console.log("未找到练习笔记文件，跳过添加链接");
      return;
    }
    
    // 读取练习笔记内容
    const content = await app.vault.read(practiceFile);
    
    // 检查是否已有语料记录部分
    const corpusLink = `- [[${corpusFileName}]] - ${new Date().toLocaleString('zh-CN')}`;
    
    if (content.includes('## 📚 语料记录')) {
      // 如果已有语料记录部分，添加到该部分
      const updatedContent = content.replace(
        /## 📚 语料记录\n/,
        `## 📚 语料记录\n\n${corpusLink}\n`
      );
      await app.vault.modify(practiceFile, updatedContent);
    } else {
      // 如果没有语料记录部分，在相关链接前添加
      const linkSection = '## 🔗 相关链接';
      if (content.includes(linkSection)) {
        const updatedContent = content.replace(
          linkSection,
          `## 📚 语料记录\n\n${corpusLink}\n\n${linkSection}`
        );
        await app.vault.modify(practiceFile, updatedContent);
      } else {
        // 如果没有相关链接部分，在文件末尾添加
        const updatedContent = content + `\n\n## 📚 语料记录\n\n${corpusLink}\n`;
        await app.vault.modify(practiceFile, updatedContent);
      }
    }
    
    new Notice("✅ 已在练习笔记中添加语料链接");
    
  } catch (error) {
    console.error("添加语料链接失败:", error);
  }
}

// AI增强版语料输入模态框
async function showAICorpusInputModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("🤖 AI语料笔记创建器");

    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";

    // 说明文字
    const description = container.createEl("div");
    description.innerHTML = `
      <p style="color: #666; margin-bottom: 15px;">
        💡 <strong>AI增强模式</strong>：您只需输入语料内容和易错点，翻译和具体意义由AI自动填写！
      </p>
    `;

    // 语料内容（必填）
    container.createEl("label", { text: "语料内容（必填）：" });
    const titleInput = container.createEl("input", {
      type: "text",
      placeholder: "例如：critical issue, pose a challenge, data breach..."
    });
    titleInput.style.cssText = "width: 100%; padding: 8px; margin: 5px 0 15px 0; border: 1px solid #ccc; border-radius: 4px; font-size: 14px;";

    // 易错点（可选）
    container.createEl("label", { text: "易错点（可选，AI会补充）：" });
    const errorInput = container.createEl("textarea", {
      placeholder: "您在使用这个语料时遇到的困难或容易出错的地方..."
    });
    errorInput.style.cssText = "width: 100%; padding: 8px; margin: 5px 0 15px 0; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; height: 60px; resize: vertical;";

    // AI提示
    const aiHint = container.createEl("div");
    aiHint.innerHTML = `
      <p style="background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 13px;">
        🤖 <strong>AI将自动填写</strong>：<br>
        • 翻译：准确的中文翻译<br>
        • 具体意义：详细的用法说明和使用场景
      </p>
    `;

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "20px";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "🤖 AI创建" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer;";
    confirmBtn.onclick = () => {
      const title = titleInput.value.trim();
      if (title) {
        modal.close();
        resolve({
          title: title,
          errorPoint: errorInput.value.trim()
        });
      } else {
        new Notice("请输入语料内容");
      }
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve(null);
    };

    titleInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        confirmBtn.click();
      }
    });

    modal.open();
    setTimeout(() => titleInput.focus(), 100);
  });
}

// 显示文件已存在的模态框
async function showFileExistsModal(fileName) {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("📚 语料笔记已存在");

    const container = modal.contentEl.createDiv();
    
    container.createEl("p", { 
      text: `语料笔记 "${fileName}" 已经存在。` 
    });
    
    container.createEl("p", { 
      text: "请选择操作：" 
    });

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "20px";

    // 覆盖按钮
    const overwriteBtn = buttonContainer.createEl("button", { text: "🔄 覆盖现有文件" });
    overwriteBtn.style.cssText = "background: #FF9800; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer;";
    overwriteBtn.onclick = () => {
      modal.close();
      resolve('overwrite');
    };

    // 创建新版本按钮
    const newBtn = buttonContainer.createEl("button", { text: "📄 创建新版本" });
    newBtn.style.cssText = "background: #4CAF50; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer;";
    newBtn.onclick = () => {
      modal.close();
      resolve('new');
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      modal.close();
      resolve('cancel');
    };

    modal.open();
  });
}

// AI分析语料
async function analyzeCorpusWithAI(token, corpusTitle, file) {
  const prompt = `你是一个专业的英语学习助手，请分析这个英语语料短语，提供准确的翻译和详细的用法说明。

语料短语：${corpusTitle}

请以JSON格式返回：
{
  "translation": "准确的中文翻译",
  "meaning": "详细的用法说明，包括使用场景、语法要点、搭配等",
  "errorPoint": "学习这个语料时需要注意的要点或容易出错的地方",
  "examples": [
    "第一个实用例句",
    "第二个实用例句"
  ],
  "exampleTranslations": [
    "第一个例句的中文翻译",
    "第二个例句的中文翻译"
  ]
}

要求：
1. 翻译要准确、简洁
2. 用法说明要详细，包含使用场景
3. 错误点要针对中国学习者的常见问题
4. 返回标准JSON格式`;

  try {
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    });

    const result = response.json;
    const aiContent = result.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new Error("AI返回结果为空");
    }

    // 尝试解析JSON
    try {
      const jsonMatch = aiContent.match(/```json\n([\s\S]*?)\n```/) || aiContent.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : aiContent;

      const parsedResult = JSON.parse(jsonStr);
      return parsedResult;
    } catch (parseError) {
      console.error("JSON解析失败:", parseError);
      console.log("AI原始返回:", aiContent);

      // 如果JSON解析失败，返回默认值
      return {
        translation: "待翻译",
        meaning: aiContent.substring(0, 200) + "...",
        errorPoint: "请注意正确的使用场景和语法搭配"
      };
    }

  } catch (error) {
    console.error("AI调用失败:", error);
    return {
      translation: "AI分析失败",
      meaning: "请手动填写用法说明",
      errorPoint: "AI分析失败，请手动填写"
    };
  }
}

exports.default = {
  entry: createCorpusNote,
  name: "createCorpusNote",
  description: `🤖 AI增强版语料笔记创建器

  🎯 核心特点：
  - 🚀 用户只需输入语料内容和易错点
  - 🤖 AI自动填写翻译和具体意义
  - 📝 简化的输入流程
  - 🔑 内置AI密钥支持

  🎮 使用方法：
  - 直接运行：createCorpusNote("your_token")
  - 指定语料：createCorpusNote("your_token", "critical issue")

  💡 AI自动填写：
  - 翻译：准确的中文翻译
  - 具体意义：详细的用法说明和使用场景
  - 易错点：如果用户未填写，AI会补充

  🔧 功能优势：
  - 大幅减少手动输入
  - AI提供专业的翻译和解释
  - 保持与原版本的兼容性
  - 支持文件名冲突处理

  适用于口语练习笔记，让AI帮您快速创建高质量的语料卡片！

  ==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==
  `,
};
