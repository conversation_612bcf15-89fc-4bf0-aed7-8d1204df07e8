{"components": [{"id": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-25T13:04:09.233Z", "updateAt": "2025-03-25T13:04:09.233Z", "components": [{"componentId": "e55b4c7c-ad2a-44c6-843d-2b57830b7861"}, {"componentId": "a0fe0b91-5f27-437f-a2c9-6baa51a58196"}, {"componentId": "1e7f877e-9799-4c09-b691-92d2cd440167"}, {"componentId": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a"}, {"componentId": "bf2a223b-b0ea-4896-8516-de708e57cb5b"}, {"componentId": "a7e69d8d-b371-4d49-851d-2cdd1dbdde81"}, {"componentId": "d96c4ca4-d366-401d-bd24-80930645f971"}, {"componentId": "1d80d70b-dce3-4ba5-a661-b743e067e3ec"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "1d80d70b-dce3-4ba5-a661-b743e067e3ec", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "场景", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYMMDD}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "160.71429443359375", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "3b1dd217-bf0b-4554-8651-f315220f3b23", "name": "涉及法规", "isShow": true, "type": "text", "options": {}}, {"id": "7f328102-f04c-4650-9a39-7f550ce5cd8f", "name": "场景", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "generateSpecificScenario(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}, "width": "92"}}, {"id": "597851f3-d63a-4634-8686-558343fb4c9b", "name": "${file.backlinks}", "isShow": true, "type": "text", "options": {}, "alias": "具体场景"}, {"id": "95d36d3e-62b0-4d9e-aad6-e5622f1c051b", "name": "场景类型", "isShow": true, "type": "text", "options": {"width": "104"}}, {"id": "700f76e1-0b6f-40a3-8516-fab79d2f0add", "name": "关键词", "isShow": true, "type": "multiSelect", "options": {"width": "544"}}, {"id": "60f11bf9-7ef3-4c0c-8720-02fd013a5470", "name": "适用角色", "isShow": true, "type": "multiSelect", "options": {"width": "295"}}, {"id": "5e393e9a-dd76-4e95-9959-31a4c374231a", "name": "难度等级", "isShow": true, "type": "text", "options": {"width": "120"}}], "templates": [{"id": "e92b79aa-44b6-4653-ab0d-ea95c548680a", "path": "英语/口语训练/杂七杂八/口语模板.md", "name": "口语模板.md", "type": "normal"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "英语/口语训练/口语练习"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "f824e92c-ee73-43e9-9b06-ad4528d79a44", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "口语训练/练习场景", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "🍎重要", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "455", "pinned": "left", "statisticType": "hasValue"}, "alias": "File Name"}, {"id": "20a2162a-297b-4a44-b852-dd135779c5a0", "name": "parent", "isShow": false, "type": "select", "options": {"items": [{"id": "85b72cda-e45a-4ee0-bd66-8ead94985e19", "value": "Project", "color": "components--color-red"}, {"id": "2b85000d-f319-438b-b58f-70ced55f8eda", "value": "Area", "color": "components--color-green"}, {"id": "c500ec07-a7d3-4750-bde1-3d54874358a9", "value": "Resource", "color": "components--color-purple"}, {"id": "1a5fdcdb-7748-41a6-abf3-6aa321ceb5d1", "value": "Diary", "color": "components--color-yellow"}], "pinned": null, "width": "73"}, "alias": "Parent"}, {"id": "cf02fde7-9938-44f7-80a3-c2e4b86b6be5", "name": "branch", "isShow": false, "type": "select", "options": {"items": [{"id": "601c21b3-bfe8-4df9-afc8-77993e69ead9", "value": "学习", "color": "components--color-blue"}, {"id": "1a59031f-cda4-4fd4-b166-d2b30e45fb5f", "value": "生活", "color": "components--color-pink"}, {"id": "fe431b21-d1f4-462e-a328-8276f65a90b7", "value": "项目", "color": "components--color-orange"}], "width": "75", "pinned": null}, "alias": "branch", "wrap": false}, {"id": "b12cee3c-2cdd-40bc-b061-69070f69c56c", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}, "wrap": true}, {"id": "51a34193-5a44-442f-accb-b2353644d26e", "name": "Archived", "isShow": false, "type": "checkbox", "options": {}, "alias": "archived"}, {"id": "d69f01f1-444b-471a-bc64-81b35b153981", "name": "${file.mtime}", "isShow": false, "type": "datetime", "options": {}, "alias": "modified time"}, {"id": "d3d655c4-f29e-4a30-aaa0-36097e7fbc91", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}, "alias": "created time"}, {"id": "d627fc1b-9017-4334-bcf6-27f00e0de211", "name": "🗂️", "isShow": false, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "migrateFile()"}, "width": "88"}, "alias": "auto-file"}, {"id": "82c7e60c-f383-4412-89ee-a929cc60d437", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}, "width": "92"}}, {"id": "80f735b0-4715-427a-8f1e-9f99bedb0657", "name": "总结", "isShow": true, "type": "text", "options": {"width": "407"}}], "templates": [{"id": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe", "path": "TestFolder/Templates/New Area.md", "name": "New Area.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "02-Areas"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "checked", "property": "🍎重要", "value": "", "conditions": []}, {"id": "57299b7d-992d-4670-8458-75d68e4a96f6", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "口语训练/语料卡", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "ec7e462a-d5dd-4462-8ede-fcc87b74d5fe"}, {"id": "bf2a223b-b0ea-4896-8516-de708e57cb5b", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "🍐语料", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "249.71429443359375", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "a339b133-aa30-4698-b327-8a643cb65286", "name": "翻译", "isShow": true, "type": "text", "options": {}}, {"id": "a83398a2-dac3-4c02-af6d-1960eef68b4c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "62"}}, {"id": "f58c4b71-b4d7-4dd2-af9a-69ebc7549e3c", "name": "重要", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e14774d2-c813-4367-b366-954a205e9ba6", "name": "🍎重要", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "65"}}, {"id": "93563940-d227-48e4-9ee4-5fa80039ae71", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {"width": "88"}}, {"id": "0e5bdcda-d393-4fbe-923c-e7595777d293", "name": "来源练习", "isShow": true, "type": "text", "options": {}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "f1e2002b-a9b9-4f2a-a375-f92487eab078", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "口语训练/语料卡", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "已学", "direction": "desc", "disabled": false}, {"id": "4d690858-07f2-4b44-9fcd-1b9ad9ef43cf", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}, {"id": "1e7f877e-9799-4c09-b691-92d2cd440167", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "最近", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "844.7142944335938", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "e5b33e67-4c6f-47fd-b3e8-210862006808", "name": "学了", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e11d1cc9-ca51-4add-a10a-f1e161f27aad", "name": "已学", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "66"}}, {"id": "4a9df847-03be-4613-a8d4-a7eb4b8b308c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "44"}}, {"id": "edadd273-0d17-4654-8fe6-a64bcf5f437c", "name": " 🪚", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "atomicNoteSplitter(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"inbox\")"}, "width": "65"}}, {"id": "dbf593ac-3e63-46f5-8888-2c3404c6f24d", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": []}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}, {"id": "a0fe0b91-5f27-437f-a2c9-6baa51a58196", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "📒练习评价", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYYYMMDDHHmm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "171.71429443359375", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "9c20d9f3-dec6-4fdb-bf94-97efed5fc4aa", "name": "场景来源", "isShow": true, "type": "text", "options": {}}, {"id": "d919f2e9-985f-49ff-ade1-6e8bd281ae43", "name": "遇到的问题", "isShow": true, "type": "text", "options": {}}, {"id": "d332b625-a4a7-4104-b699-dada69f8df52", "name": "改进点", "isShow": true, "type": "text", "options": {"width": "105"}}, {"id": "1996948a-9276-4ad4-bed9-1cdb9d109d3b", "name": "AI", "isShow": true, "type": "button", "options": {"width": "81", "action": {"type": "runScript", "properties": [], "expression": "extractCorpus(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}}, "alias": "AI提取"}, {"id": "f74eaad0-ca72-477a-8905-cf37343f98fe", "name": "🍐", "isShow": true, "type": "button", "options": {"width": "62", "action": {"type": "runScript", "properties": [], "expression": "createCorpusNote(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}}, "alias": "语料"}, {"id": "82bb96c9-371a-4753-bfd6-8460e88d48b5", "name": "批量", "isShow": true, "type": "button", "options": {"width": "76", "action": {"type": "runScript", "properties": [], "expression": "batchCreateCorpus(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}}, "alias": "🍐"}, {"id": "d5e4d113-d5c9-4856-be21-334d6a917497", "name": "评估", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "practiceEvaluation()"}, "width": "82"}}, {"id": "1c414711-643a-44dd-ac31-05ec742faeba", "name": "评价", "isShow": true, "type": "text", "options": {"width": "107"}}, {"id": "4db2f370-744c-4749-a48b-18b1166ff312", "name": "原本想说", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "originalIntentSimple(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}, "width": "200"}}], "templates": [{"id": "e92b79aa-44b6-4653-ab0d-ea95c548680a", "path": "英语/口语训练/杂七杂八/口语模板.md", "name": "口语模板.md", "type": "normal"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "英语/口语训练/口语练习"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "6413ad63-49f0-45e1-8ab6-aa3a8d77b85c", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "口语训练/口语练习", "conditions": []}, {"id": "8e8123bb-77da-4651-9280-b25b7d6a09cf", "type": "filter", "operator": "not_contains", "property": "${file.extension}", "value": "m4a", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "e55b4c7c-ad2a-44c6-843d-2b57830b7861", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "🍀练习", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYYYMMDDHHmm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "173.7756805419922", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "2c33738f-d5af-4bdd-a664-bde804648ce0", "name": "对话角色", "isShow": true, "type": "text", "options": {"width": "97"}}, {"id": "53495d61-701c-4f6d-90a0-f17c51e96e7f", "name": "场景类型", "isShow": true, "type": "text", "options": {"width": "102"}}, {"id": "035dfdb6-99b9-4c92-ad6a-9ecd80babfab", "name": "事件背景", "isShow": true, "type": "text", "options": {"width": "98"}}, {"id": "076b40b9-1a1f-4936-b489-2d7c9ead9463", "name": "🗣️", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "createPracticeNote()"}, "width": "39"}, "alias": "练习"}, {"id": "b84f7fb3-73eb-4ebc-8f26-df169e41ebb9", "name": "${file.backlinks}", "isShow": true, "type": "text", "options": {"width": "197"}}], "templates": [{"id": "e92b79aa-44b6-4653-ab0d-ea95c548680a", "path": "英语/口语训练/杂七杂八/口语模板.md", "name": "口语模板.md", "type": "normal"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "英语/口语训练/口语练习"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "6413ad63-49f0-45e1-8ab6-aa3a8d77b85c", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "口语训练/具体练习", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "a7e69d8d-b371-4d49-851d-2cdd1dbdde81", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "🌓原意", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYMMDD}}", "properties": [{"id": "713ff7c6-9369-4f1f-a9d2-22b60af98705", "name": "原本想说", "isShow": true, "type": "text", "options": {"width": "398"}, "wrap": true}, {"id": "2284eaf8-bc8e-45ec-b237-20bd636e72b7", "name": "AI翻译", "isShow": true, "type": "text", "options": {"width": "696"}, "wrap": true}, {"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": false, "wrap": true, "options": {"width": "109.71429443359375", "pinned": null, "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "680b2417-504f-40ce-b008-3b945a423981", "name": "🍐", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "createCorpusNote(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\")"}}, "alias": "语料"}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "inbox"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "f1e2002b-a9b9-4f2a-a375-f92487eab078", "type": "filter", "operator": "has_value", "property": "原本想说", "value": null, "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "已学", "direction": "desc", "disabled": false}, {"id": "4d690858-07f2-4b44-9fcd-1b9ad9ef43cf", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "d96c4ca4-d366-401d-bd24-80930645f971", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "原意描述", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": false, "wrap": false, "options": {}}, {"id": "410149ca-def2-4a3b-a542-fa5e6c56f105", "name": "原本想说", "isShow": true, "type": "text", "options": {"width": "525"}}, {"id": "e5b33e67-4c6f-47fd-b3e8-210862006808", "name": "学了", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e11d1cc9-ca51-4add-a10a-f1e161f27aad", "name": "已学", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "66"}}, {"id": "0c6072ad-9aad-46d6-880d-0e1e9abb341d", "name": "AI翻译", "isShow": true, "type": "text", "options": {"width": "684"}}, {"id": "4a9df847-03be-4613-a8d4-a7eb4b8b308c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "44"}}, {"id": "dbf593ac-3e63-46f5-8888-2c3404c6f24d", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {"width": "85"}}, {"id": "2990b96e-b122-4b2e-8c42-61f5c620fb7b", "name": "问题类型", "isShow": true, "type": "text", "options": {}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "126c7410-c764-401b-b8ae-eca5076367a3", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "英语/口语训练/原意表达", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}], "rootComponentId": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d"}