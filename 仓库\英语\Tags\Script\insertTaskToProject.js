module.exports = async function insertTaskToProject(params) {
    const { quickAddApi } = params;
    
    // 1. 获取所有在frontmatter的tags中包含"project"的文件
    const filesWithProjectTag = [];
    const files = app.vault.getMarkdownFiles();
    
    for (const file of files) {
        const cache = app.metadataCache.getFileCache(file);
        if (cache?.frontmatter?.tags?.includes("project")) {
            filesWithProjectTag.push(file.path);
        }
    }
    
    if (filesWithProjectTag.length === 0) {
        new Notice("⚠️ 没有找到在tags属性中包含project的文件");
        return;
    }
    
    // 2. 让用户选择文件
    const selectedFile = await quickAddApi.suggester(
        (filePath) => filePath.split("/").pop(),  // 只显示文件名（不显示路径）
        filesWithProjectTag
    );
    
    if (!selectedFile) return;
    
    // 3. 调用 Tasks API 创建任务
    const tasksPlugin = app.plugins.getPlugin('obsidian-tasks-plugin');
    if (!tasksPlugin) {
        new Notice("⚠️ 未找到 Tasks 插件");
        return;
    }
    
    const taskLine = await tasksPlugin.apiV1.createTaskLineModal();
    
    if (taskLine) {
        // 4. 在选定的文件中追加任务
        const file = app.vault.getAbstractFileByPath(selectedFile);
        const content = await app.vault.read(file);
        await app.vault.modify(file, content + "\n" + taskLine);
        new Notice(`✅ 任务已添加到 [[${selectedFile.split("/").pop()}]]`);
    }
};