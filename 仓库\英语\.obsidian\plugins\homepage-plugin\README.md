# Homepage Plugin

一个简单的 Obsidian 首页插件，可以设置固定的首页文件，并在启动时自动打开。

## 功能特性

- 🏠 **设置固定首页**: 指定一个文件作为 Obsidian 的首页
- 🚀 **启动时自动打开**: Obsidian 启动时自动打开设置的首页
- ⌨️ **手动打开命令**: 通过命令面板快速打开首页
- 🔄 **快速设置当前页**: 一键将当前打开的文件设置为首页
- ⚙️ **灵活配置选项**: 可选择是否替换当前标签页
- 🇨🇳 **中文界面**: 完全中文化的用户界面

## 使用方法

### 1. 配置首页

#### 方法一：手动输入路径
1. 打开 Obsidian 设置
2. 在左侧找到「首页插件」
3. 在「首页文件路径」中输入你想要作为首页的文件路径
   - 例如：`README.md`
   - 例如：`folder/homepage.md`

#### 方法二：快速设置当前页
1. 打开你想要设置为首页的文件
2. 打开 Obsidian 设置
3. 在左侧找到「首页插件」
4. 点击「设置当前页为首页」按钮

#### 方法三：使用命令面板
1. 打开你想要设置为首页的文件
2. 按 `Ctrl+P`（或 `Cmd+P`）打开命令面板
3. 搜索并执行「设置当前页为首页」命令

### 2. 配置选项

- **启动时打开首页**：勾选后，每次启动 Obsidian 都会自动打开首页
- **替换当前标签页**：勾选后，在当前标签页中打开首页；取消勾选则新建标签页

### 3. 快速访问

- **打开首页**: 按 `Ctrl+P`（或 `Cmd+P`），搜索「打开首页」
- **设置当前页为首页**: 按 `Ctrl+P`（或 `Cmd+P`），搜索「设置当前页为首页」
- **快捷键**: 可在 Obsidian 设置中为这些命令设置自定义快捷键

## 配置示例

```
首页文件路径: README.md
启动时打开首页: ✓
替换当前标签页: ✓
```

这样配置后，每次启动 Obsidian 都会在当前标签页中自动打开 `README.md` 文件。

## 注意事项

- 确保首页文件路径正确，文件必须存在
- 路径区分大小写
- 支持文件夹嵌套，使用 `/` 分隔路径
- 如果文件不存在，会显示错误提示

## 版本历史

### v1.0.0
- 初始版本
- 基本的首页设置功能
- 启动时自动打开
- 手动打开命令