# 首页插件 - 固定位置功能说明

## 功能概述

首页插件现在支持将首页固定在特定的标签页位置，确保首页不会因为其他操作而被移动。

## 新增功能

### 1. 固定首页位置
- **功能描述**：启用后，首页将始终保持在指定的标签页位置
- **使用场景**：适合希望首页始终在固定位置（如第一个标签页）的用户
- **配置位置**：设置 → 插件选项 → Homepage Plugin → 固定首页位置

### 2. 位置设置
- **功能描述**：设置首页的固定位置（0-10）
- **位置说明**：
  - 0 = 第一个标签页位置
  - 1 = 第二个标签页位置
  - 2 = 第三个标签页位置
  - 以此类推...
- **配置位置**：仅在启用"固定首页位置"后显示

## 使用步骤

1. **打开插件设置**
   - 进入 Obsidian 设置
   - 选择"插件选项"
   - 找到"Homepage Plugin"

2. **启用固定位置功能**
   - 开启"固定首页位置"开关
   - 设置页面会自动刷新并显示位置设置选项

3. **设置固定位置**
   - 使用滑块选择首页的固定位置（0-10）
   - 0表示第一个位置，1表示第二个位置，以此类推

4. **测试功能**
   - 使用命令"打开首页"或快捷键
   - 首页将在指定位置打开
   - 尝试移动其他标签页，首页应保持在固定位置

## 功能特点

### 自动维护位置
- 插件会自动监听工作区变化
- 当检测到首页不在指定位置时，会自动移动回去
- 确保首页始终保持在用户设定的位置

### 智能处理
- 如果指定位置超出当前标签页数量，会在末尾创建
- 兼容原有的"替换当前标签页"功能
- 固定位置功能优先级高于"替换当前标签页"设置

### 启动时支持
- 如果启用了"启动时打开首页"，首页会在指定位置打开
- 支持与其他插件功能的协同工作

## 注意事项

1. **性能考虑**
   - 插件会监听工作区变化事件
   - 使用了防抖机制避免频繁操作

2. **兼容性**
   - 与现有功能完全兼容
   - 不会影响其他插件的正常工作

3. **错误处理**
   - 包含完善的错误处理机制
   - 异常情况下会在控制台输出调试信息

## 故障排除

### 首页没有固定在指定位置
1. 检查是否启用了"固定首页位置"功能
2. 确认首页文件路径设置正确
3. 查看浏览器控制台是否有错误信息

### 位置设置不生效
1. 确保保存了设置（设置会自动保存）
2. 尝试重新打开首页
3. 重启 Obsidian 应用

### 性能问题
1. 如果遇到性能问题，可以暂时关闭固定位置功能
2. 检查是否有其他插件冲突

## 更新日志

### v1.1.0
- 新增固定首页位置功能
- 新增位置设置选项
- 优化用户界面和体验
- 增强错误处理和调试功能

---

如有问题或建议，请通过 GitHub 或插件社区反馈。