/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD AND TERSER
if you want to view the source, please visit the github repository of this plugin
*/

"use strict";

var __defProp = Object.defineProperty, __getOwnPropDesc = Object.getOwnPropertyDescriptor, __getOwnPropNames = Object.getOwnPropertyNames, __hasOwnProp = Object.prototype.hasOwnProperty, __typeError = msg => {
  throw TypeError(msg);
}, __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {
  enumerable: true,
  configurable: true,
  writable: true,
  value
}) : obj[key] = value, __export = (target, all) => {
  for (var name in all) __defProp(target, name, {
    get: all[name],
    enumerable: true
  });
}, __copyProps = (to, from, except, desc) => {
  if (from && "object" == typeof from || "function" == typeof from) for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
    get: () => from[key],
    enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
  });
  return to;
}, __toCommonJS = mod => __copyProps(__defProp({}, "__esModule", {
  value: true
}), mod), __publicField = (obj, key, value) => __defNormalProp(obj, "symbol" != typeof key ? key + "" : key, value), __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg), __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), 
getter ? getter.call(obj) : member.get(obj)), __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value), __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, "write to private field"), 
setter ? setter.call(obj, value) : member.set(obj, value), value), main_exports = {};

__export(main_exports, {
  default: () => TagFolderPlugin5
});

module.exports = __toCommonJS(main_exports);

var import_obsidian8 = require("obsidian"), enumShowListIn = {
  "": "Sidebar",
  CURRENT_PANE: "Current pane",
  SPLIT_PANE: "New pane"
}, DEFAULT_SETTINGS = {
  displayMethod: "NAME",
  alwaysOpen: false,
  ignoreDocTags: "",
  ignoreTags: "",
  hideOnRootTags: "",
  sortType: "DISPNAME_ASC",
  sortExactFirst: false,
  sortTypeTag: "NAME_ASC",
  expandLimit: 0,
  disableNestedTags: false,
  hideItems: "NONE",
  ignoreFolders: "",
  targetFolders: "",
  scanDelay: 250,
  useTitle: true,
  reduceNestedParent: true,
  frontmatterKey: "title",
  useTagInfo: false,
  tagInfo: "pininfo.md",
  mergeRedundantCombination: false,
  useVirtualTag: false,
  useFrontmatterTagsForNewNotes: false,
  doNotSimplifyTags: false,
  overrideTagClicking: false,
  useMultiPaneList: false,
  archiveTags: "",
  disableNarrowingDown: false,
  expandUntaggedToRoot: false,
  disableDragging: false,
  linkConfig: {
    incoming: {
      enabled: true,
      key: ""
    },
    outgoing: {
      enabled: true,
      key: ""
    }
  },
  linkShowOnlyFDR: true,
  linkCombineOtherTree: true,
  showListIn: "",
  displayFolderAsTag: false
}, VIEW_TYPE_SCROLL = "tagfolder-view-scroll", EPOCH_MINUTE = 60, EPOCH_HOUR = 60 * EPOCH_MINUTE, EPOCH_DAY = 24 * EPOCH_HOUR, FRESHNESS_1 = "FRESHNESS_01", FRESHNESS_2 = "FRESHNESS_02", FRESHNESS_3 = "FRESHNESS_03", FRESHNESS_4 = "FRESHNESS_04", FRESHNESS_5 = "FRESHNESS_05", tagDispDict = {
  FRESHNESS_01: "🕐",
  FRESHNESS_02: "📖",
  FRESHNESS_03: "📗",
  FRESHNESS_04: "📚",
  FRESHNESS_05: "🗄",
  _VIRTUAL_TAG_FRESHNESS: "⌛",
  _VIRTUAL_TAG_CANVAS: "📋 Canvas",
  _VIRTUAL_TAG_FOLDER: "📁"
}, VIEW_TYPE_TAGFOLDER = "tagfolder-view", VIEW_TYPE_TAGFOLDER_LINK = "tagfolder-link-view", VIEW_TYPE_TAGFOLDER_LIST = "tagfolder-view-list", OrderKeyTag = {
  NAME: "Tag name",
  ITEMS: "Count of items"
}, OrderDirection = {
  ASC: "Ascending",
  DESC: "Descending"
}, OrderKeyItem = {
  DISPNAME: "Displaying name",
  NAME: "File name",
  MTIME: "Modified time",
  CTIME: "Created time",
  FULLPATH: "Fullpath of the file"
}, is_array = Array.isArray, array_from = Array.from, object_keys = Object.keys, define_property = Object.defineProperty, get_descriptor = Object.getOwnPropertyDescriptor, get_descriptors = Object.getOwnPropertyDescriptors, object_prototype = Object.prototype, array_prototype = Array.prototype, get_prototype_of = Object.getPrototypeOf, noop = () => {};

function run_all(arr) {
  for (var i = 0; i < arr.length; i++) arr[i]();
}

var DEV = false, DERIVED = 2, EFFECT = 4, RENDER_EFFECT = 8, BLOCK_EFFECT = 16, BRANCH_EFFECT = 32, ROOT_EFFECT = 64, UNOWNED = 128, DISCONNECTED = 256, CLEAN = 512, DIRTY = 1024, MAYBE_DIRTY = 2048, INERT = 4096, DESTROYED = 8192, EFFECT_RAN = 16384, EFFECT_TRANSPARENT = 32768, LEGACY_DERIVED_PROP = 65536, INSPECT_EFFECT = 1 << 17, HEAD_EFFECT = 1 << 18, EFFECT_HAS_DERIVED = 1 << 19, STATE_SYMBOL = Symbol("$state"), STATE_SYMBOL_METADATA = Symbol("$state metadata"), LOADING_ATTR_SYMBOL = Symbol(""), request_idle_callback = "undefined" == typeof requestIdleCallback ? cb => setTimeout(cb, 1) : requestIdleCallback, is_micro_task_queued = false, is_idle_task_queued = false, current_queued_micro_tasks = [], current_queued_idle_tasks = [];

function process_micro_tasks() {
  is_micro_task_queued = false;
  const tasks = current_queued_micro_tasks.slice();
  current_queued_micro_tasks = [];
  run_all(tasks);
}

function process_idle_tasks() {
  is_idle_task_queued = false;
  const tasks = current_queued_idle_tasks.slice();
  current_queued_idle_tasks = [];
  run_all(tasks);
}

function queue_micro_task(fn) {
  if (!is_micro_task_queued) {
    is_micro_task_queued = true;
    queueMicrotask(process_micro_tasks);
  }
  current_queued_micro_tasks.push(fn);
}

function queue_idle_task(fn) {
  if (!is_idle_task_queued) {
    is_idle_task_queued = true;
    request_idle_callback(process_idle_tasks);
  }
  current_queued_idle_tasks.push(fn);
}

function flush_tasks() {
  if (is_micro_task_queued) process_micro_tasks();
  if (is_idle_task_queued) process_idle_tasks();
}

var bold = "font-weight: bold", normal = "font-weight: normal";

function hydration_attribute_changed(attribute, html2, value) {
  if (DEV) console.warn(`%c[svelte] hydration_attribute_changed\n%cThe \`${attribute}\` attribute on \`${html2}\` changed its value between server and client renders. The client value, \`${value}\`, will be ignored in favour of the server value`, bold, normal); else console.warn("hydration_attribute_changed");
}

function hydration_html_changed(location) {
  if (DEV) console.warn("%c[svelte] hydration_html_changed\n%c" + (location ? `The value of an \`{@html ...}\` block ${location} changed between server and client renders. The client value will be ignored in favour of the server value` : "The value of an `{@html ...}` block changed between server and client renders. The client value will be ignored in favour of the server value"), bold, normal); else console.warn("hydration_html_changed");
}

function hydration_mismatch(location) {
  if (DEV) console.warn("%c[svelte] hydration_mismatch\n%c" + (location ? `Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near ${location}` : "Hydration failed because the initial UI does not match what was rendered on the server"), bold, normal); else console.warn("hydration_mismatch");
}

function lifecycle_double_unmount() {
  if (DEV) console.warn("%c[svelte] lifecycle_double_unmount\n%cTried to unmount a component that was not mounted", bold, normal); else console.warn("lifecycle_double_unmount");
}

function ownership_invalid_binding(parent, child2, owner) {
  if (DEV) console.warn(`%c[svelte] ownership_invalid_binding\n%c${parent} passed a value to ${child2} with \`bind:\`, but the value is owned by ${owner}. Consider creating a binding between ${owner} and ${parent}`, bold, normal); else console.warn("ownership_invalid_binding");
}

function ownership_invalid_mutation(component2, owner) {
  if (DEV) console.warn("%c[svelte] ownership_invalid_mutation\n%c" + (component2 ? `${component2} mutated a value owned by ${owner}. This is strongly discouraged. Consider passing values to child components with \`bind:\`, or use a callback instead` : "Mutating a value outside the component that created it is strongly discouraged. Consider passing values to child components with `bind:`, or use a callback instead"), bold, normal); else console.warn("ownership_invalid_mutation");
}

function state_proxy_equality_mismatch(operator) {
  if (DEV) console.warn(`%c[svelte] state_proxy_equality_mismatch\n%cReactive \`$state(...)\` proxies and the values they proxy have different identities. Because of this, comparisons with \`${operator}\` will produce unexpected results`, bold, normal); else console.warn("state_proxy_equality_mismatch");
}

var EACH_ITEM_REACTIVE = 1, EACH_INDEX_REACTIVE = 2, EACH_IS_CONTROLLED = 4, EACH_IS_ANIMATED = 8, EACH_ITEM_IMMUTABLE = 16, PROPS_IS_IMMUTABLE = 1, PROPS_IS_RUNES = 2, PROPS_IS_UPDATED = 4, PROPS_IS_BINDABLE = 8, PROPS_IS_LAZY_INITIAL = 16, TRANSITION_OUT = 2, TRANSITION_GLOBAL = 4, TEMPLATE_FRAGMENT = 1, TEMPLATE_USE_IMPORT_NODE = 2, HYDRATION_START = "[", HYDRATION_START_ELSE = "[!", HYDRATION_END = "]", HYDRATION_ERROR = {}, ELEMENT_PRESERVE_ATTRIBUTE_CASE = 2, UNINITIALIZED = Symbol(), FILENAME = Symbol("filename"), HMR = Symbol("hmr"), boundaries = {}, chrome_pattern = /at (?:.+ \()?(.+):(\d+):(\d+)\)?$/, firefox_pattern = /@(.+):(\d+):(\d+)$/;

function get_stack() {
  var _a;
  const stack2 = (new Error).stack;
  if (!stack2) return null;
  const entries = [];
  for (const line of stack2.split("\n")) {
    let match = null != (_a = chrome_pattern.exec(line)) ? _a : firefox_pattern.exec(line);
    if (match) entries.push({
      file: match[1],
      line: +match[2],
      column: +match[3]
    });
  }
  return entries;
}

function get_component() {
  var _a;
  const stack2 = null == (_a = get_stack()) ? void 0 : _a.slice(4);
  if (!stack2) return null;
  for (let i = 0; i < stack2.length; i++) {
    const entry = stack2[i], modules = boundaries[entry.file];
    if (modules) {
      for (const module2 of modules) if (module2.start.line < entry.line && module2.end.line > entry.line) return module2.component;
    } else if (0 === i) return null;
  }
  return null;
}

var ADD_OWNER = Symbol("ADD_OWNER");

function add_owner(object, owner, global = false, skip_warning = false) {
  if (object && !global) {
    const component2 = dev_current_component_function, metadata = object[STATE_SYMBOL_METADATA];
    if (metadata && !has_owner(metadata, component2)) {
      let original = get_owner(metadata);
      if (owner[FILENAME] !== component2[FILENAME] && !skip_warning) ownership_invalid_binding(component2[FILENAME], owner[FILENAME], original[FILENAME]);
    }
  }
  add_owner_to_object(object, owner, new Set);
}

function widen_ownership(from, to) {
  if (null !== to.owners) for (;from; ) {
    if (null === from.owners) {
      to.owners = null;
      break;
    }
    for (const owner of from.owners) to.owners.add(owner);
    from = from.parent;
  }
}

function add_owner_to_object(object, owner, seen) {
  const metadata = null == object ? void 0 : object[STATE_SYMBOL_METADATA];
  if (metadata) {
    if ("owners" in metadata && null != metadata.owners) metadata.owners.add(owner);
  } else if (object && "object" == typeof object) {
    if (seen.has(object)) return;
    seen.add(object);
    if (ADD_OWNER in object && object[ADD_OWNER]) render_effect((() => {
      object[ADD_OWNER](owner);
    })); else {
      var proto = get_prototype_of(object);
      if (proto === Object.prototype) for (const key in object) add_owner_to_object(object[key], owner, seen); else if (proto === Array.prototype) for (let i = 0; i < object.length; i += 1) add_owner_to_object(object[i], owner, seen);
    }
  }
}

function has_owner(metadata, component2) {
  if (null === metadata.owners) return true; else return metadata.owners.has(component2) || null !== metadata.parent && has_owner(metadata.parent, component2);
}

function get_owner(metadata) {
  var _a, _b;
  return null != (_b = null == (_a = null == metadata ? void 0 : metadata.owners) ? void 0 : _a.values().next().value) ? _b : get_owner(metadata.parent);
}

var skip = false;

function check_ownership(metadata) {
  if (skip) return;
  const component2 = get_component();
  if (component2 && !has_owner(metadata, component2)) {
    let original = get_owner(metadata);
    if (original[FILENAME] !== component2[FILENAME]) ownership_invalid_mutation(component2[FILENAME], original[FILENAME]); else ownership_invalid_mutation();
  }
}

function equals(value) {
  return value === this.v;
}

function safe_not_equal(a, b) {
  return a != a ? b == b : a !== b || null !== a && "object" == typeof a || "function" == typeof a;
}

function safe_equals(value) {
  return !safe_not_equal(value, this.v);
}

function bind_invalid_checkbox_value() {
  if (DEV) {
    const error = new Error("bind_invalid_checkbox_value\nUsing `bind:value` together with a checkbox input is not allowed. Use `bind:checked` instead");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("bind_invalid_checkbox_value");
}

function derived_references_self() {
  if (DEV) {
    const error = new Error("derived_references_self\nA derived value cannot reference itself recursively");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("derived_references_self");
}

function effect_in_teardown(rune) {
  if (DEV) {
    const error = new Error(`effect_in_teardown\n\`${rune}\` cannot be used inside an effect cleanup function`);
    error.name = "Svelte error";
    throw error;
  } else throw new Error("effect_in_teardown");
}

function effect_in_unowned_derived() {
  if (DEV) {
    const error = new Error("effect_in_unowned_derived\nEffect cannot be created inside a `$derived` value that was not itself created inside an effect");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("effect_in_unowned_derived");
}

function effect_orphan(rune) {
  if (DEV) {
    const error = new Error(`effect_orphan\n\`${rune}\` can only be used inside an effect (e.g. during component initialisation)`);
    error.name = "Svelte error";
    throw error;
  } else throw new Error("effect_orphan");
}

function effect_update_depth_exceeded() {
  if (DEV) {
    const error = new Error("effect_update_depth_exceeded\nMaximum update depth exceeded. This can happen when a reactive block or effect repeatedly sets a new value. Svelte limits the number of nested updates to prevent infinite loops");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("effect_update_depth_exceeded");
}

function hydration_failed() {
  if (DEV) {
    const error = new Error("hydration_failed\nFailed to hydrate the application");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("hydration_failed");
}

function invalid_snippet() {
  if (DEV) {
    const error = new Error("invalid_snippet\nCould not `{@render}` snippet due to the expression being `null` or `undefined`. Consider using optional chaining `{@render snippet?.()}`");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("invalid_snippet");
}

function props_invalid_value(key) {
  if (DEV) {
    const error = new Error(`props_invalid_value\nCannot do \`bind:${key}={undefined}\` when \`${key}\` has a fallback value`);
    error.name = "Svelte error";
    throw error;
  } else throw new Error("props_invalid_value");
}

function rune_outside_svelte(rune) {
  if (DEV) {
    const error = new Error(`rune_outside_svelte\nThe \`${rune}\` rune is only available inside \`.svelte\` and \`.svelte.js/ts\` files`);
    error.name = "Svelte error";
    throw error;
  } else throw new Error("rune_outside_svelte");
}

function state_descriptors_fixed() {
  if (DEV) {
    const error = new Error("state_descriptors_fixed\nProperty descriptors defined on `$state` objects must contain `value` and always be `enumerable`, `configurable` and `writable`.");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("state_descriptors_fixed");
}

function state_prototype_fixed() {
  if (DEV) {
    const error = new Error("state_prototype_fixed\nCannot set prototype of `$state` object");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("state_prototype_fixed");
}

function state_unsafe_local_read() {
  if (DEV) {
    const error = new Error("state_unsafe_local_read\nReading state that was created inside the same derived is forbidden. Consider using `untrack` to read locally created state");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("state_unsafe_local_read");
}

function state_unsafe_mutation() {
  if (DEV) {
    const error = new Error("state_unsafe_mutation\nUpdating state inside a derived or a template expression is forbidden. If the value should not be reactive, declare it without `$state`");
    error.name = "Svelte error";
    throw error;
  } else throw new Error("state_unsafe_mutation");
}

var inspect_effects = new Set;

function set_inspect_effects(v) {
  inspect_effects = v;
}

function source(v) {
  return {
    f: 0,
    v,
    reactions: null,
    equals,
    version: 0
  };
}

function state(v) {
  return push_derived_source(source(v));
}

function mutable_source(initial_value, immutable = false) {
  var _a, _b;
  const s = source(initial_value);
  if (!immutable) s.equals = safe_equals;
  if (null !== component_context && null !== component_context.l) (null != (_b = (_a = component_context.l).s) ? _b : _a.s = []).push(s);
  return s;
}

function push_derived_source(source2) {
  if (null !== active_reaction && !!(active_reaction.f & DERIVED)) if (null === derived_sources) set_derived_sources([ source2 ]); else derived_sources.push(source2);
  return source2;
}

function set(source2, value) {
  if (null !== active_reaction && is_runes() && !!(active_reaction.f & (DERIVED | BLOCK_EFFECT)) && (null === derived_sources || !derived_sources.includes(source2))) state_unsafe_mutation();
  return internal_set(source2, value);
}

function internal_set(source2, value) {
  if (!source2.equals(value)) {
    source2.v = value;
    source2.version = increment_version();
    mark_reactions(source2, DIRTY);
    if (is_runes() && null !== active_effect && !!(active_effect.f & CLEAN) && !(active_effect.f & BRANCH_EFFECT)) if (null !== new_deps && new_deps.includes(source2)) {
      set_signal_status(active_effect, DIRTY);
      schedule_effect(active_effect);
    } else if (null === untracked_writes) set_untracked_writes([ source2 ]); else untracked_writes.push(source2);
    if (DEV && inspect_effects.size > 0) {
      const inspects = Array.from(inspect_effects);
      var previously_flushing_effect = is_flushing_effect;
      set_is_flushing_effect(true);
      try {
        for (const effect2 of inspects) {
          if (!!(effect2.f & CLEAN)) set_signal_status(effect2, MAYBE_DIRTY);
          if (check_dirtiness(effect2)) update_effect(effect2);
        }
      } finally {
        set_is_flushing_effect(previously_flushing_effect);
      }
      inspect_effects.clear();
    }
  }
  return value;
}

function mark_reactions(signal, status) {
  var reactions = signal.reactions;
  if (null !== reactions) for (var runes = is_runes(), length = reactions.length, i = 0; i < length; i++) {
    var reaction = reactions[i], flags = reaction.f;
    if (!(flags & DIRTY)) if (runes || reaction !== active_effect) if (!(DEV && flags & INSPECT_EFFECT)) {
      set_signal_status(reaction, status);
      if (!!(flags & (CLEAN | UNOWNED))) if (!!(flags & DERIVED)) mark_reactions(reaction, MAYBE_DIRTY); else schedule_effect(reaction);
    } else inspect_effects.add(reaction);
  }
}

function derived(fn) {
  var _a, flags = DERIVED | DIRTY;
  if (null === active_effect) flags |= UNOWNED; else active_effect.f |= EFFECT_HAS_DERIVED;
  const signal = {
    children: null,
    ctx: component_context,
    deps: null,
    equals,
    f: flags,
    fn,
    reactions: null,
    v: null,
    version: 0,
    parent: active_effect
  };
  if (null !== active_reaction && !!(active_reaction.f & DERIVED)) {
    var derived3 = active_reaction;
    (null != (_a = derived3.children) ? _a : derived3.children = []).push(signal);
  }
  return signal;
}

function derived_safe_equal(fn) {
  const signal = derived(fn);
  signal.equals = safe_equals;
  return signal;
}

function destroy_derived_children(derived3) {
  var children = derived3.children;
  if (null !== children) {
    derived3.children = null;
    for (var i = 0; i < children.length; i += 1) {
      var child2 = children[i];
      if (!!(child2.f & DERIVED)) destroy_derived(child2); else destroy_effect(child2);
    }
  }
}

var stack = [];

function execute_derived(derived3) {
  var value, prev_active_effect = active_effect;
  set_active_effect(derived3.parent);
  if (DEV) {
    let prev_inspect_effects = inspect_effects;
    set_inspect_effects(new Set);
    try {
      if (stack.includes(derived3)) derived_references_self();
      stack.push(derived3);
      destroy_derived_children(derived3);
      value = update_reaction(derived3);
    } finally {
      set_active_effect(prev_active_effect);
      set_inspect_effects(prev_inspect_effects);
      stack.pop();
    }
  } else try {
    destroy_derived_children(derived3);
    value = update_reaction(derived3);
  } finally {
    set_active_effect(prev_active_effect);
  }
  return value;
}

function update_derived(derived3) {
  var value = execute_derived(derived3);
  set_signal_status(derived3, (skip_reaction || !!(derived3.f & UNOWNED)) && null !== derived3.deps ? MAYBE_DIRTY : CLEAN);
  if (!derived3.equals(value)) {
    derived3.v = value;
    derived3.version = increment_version();
  }
}

function destroy_derived(signal) {
  destroy_derived_children(signal);
  remove_reactions(signal, 0);
  set_signal_status(signal, DESTROYED);
  signal.v = signal.children = signal.deps = signal.ctx = signal.reactions = null;
}

function lifecycle_outside_component(name) {
  if (DEV) {
    const error = new Error(`lifecycle_outside_component\n\`${name}(...)\` can only be used during component initialisation`);
    error.name = "Svelte error";
    throw error;
  } else throw new Error("lifecycle_outside_component");
}

var FLUSH_MICROTASK = 0, FLUSH_SYNC = 1, handled_errors = new WeakSet, scheduler_mode = FLUSH_MICROTASK, is_micro_task_queued2 = false, is_flushing_effect = false, is_destroying_effect = false;

function set_is_flushing_effect(value) {
  is_flushing_effect = value;
}

function set_is_destroying_effect(value) {
  is_destroying_effect = value;
}

var queued_root_effects = [], flush_count = 0, dev_effect_stack = [], active_reaction = null;

function set_active_reaction(reaction) {
  active_reaction = reaction;
}

var active_effect = null;

function set_active_effect(effect2) {
  active_effect = effect2;
}

var derived_sources = null;

function set_derived_sources(sources) {
  derived_sources = sources;
}

var new_deps = null, skipped_deps = 0, untracked_writes = null;

function set_untracked_writes(value) {
  untracked_writes = value;
}

var current_version = 0, skip_reaction = false, is_signals_recorded = false, captured_signals = new Set, component_context = null, dev_current_component_function = null;

function increment_version() {
  return ++current_version;
}

function is_runes() {
  return null !== component_context && null === component_context.l;
}

function check_dirtiness(reaction) {
  var _a, _b, _c, _d, flags = reaction.f;
  if (!!(flags & DIRTY)) return true;
  if (!!(flags & MAYBE_DIRTY)) {
    var dependencies = reaction.deps, is_unowned = !!(flags & UNOWNED);
    if (null !== dependencies) {
      var i;
      if (!!(flags & DISCONNECTED)) {
        for (i = 0; i < dependencies.length; i++) (null != (_b = (_a = dependencies[i]).reactions) ? _b : _a.reactions = []).push(reaction);
        reaction.f ^= DISCONNECTED;
      }
      for (i = 0; i < dependencies.length; i++) {
        var dependency = dependencies[i];
        if (check_dirtiness(dependency)) update_derived(dependency);
        if (is_unowned && null !== active_effect && !skip_reaction && !(null == (_c = null == dependency ? void 0 : dependency.reactions) ? void 0 : _c.includes(reaction))) (null != (_d = dependency.reactions) ? _d : dependency.reactions = []).push(reaction);
        if (dependency.version > reaction.version) return true;
      }
    }
    if (!is_unowned) set_signal_status(reaction, CLEAN);
  }
  return false;
}

function handle_error(error, effect2, component_context2) {
  var _a, _b;
  if (!DEV || handled_errors.has(error) || null === component_context2) throw error;
  const component_stack = [], effect_name = null == (_a = effect2.fn) ? void 0 : _a.name;
  if (effect_name) component_stack.push(effect_name);
  let current_context = component_context2;
  for (;null !== current_context; ) {
    if (DEV) {
      var filename = null == (_b = current_context.function) ? void 0 : _b[FILENAME];
      if (filename) {
        const file = filename.split("/").pop();
        component_stack.push(file);
      }
    }
    current_context = current_context.p;
  }
  const indent = /Firefox/.test(navigator.userAgent) ? "  " : "\t";
  define_property(error, "message", {
    value: error.message + `\n${component_stack.map((name => `\n${indent}in ${name}`)).join("")}\n`
  });
  const stack2 = error.stack;
  if (stack2) {
    const lines = stack2.split("\n"), new_lines = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (!line.includes("svelte/src/internal")) new_lines.push(line);
    }
    define_property(error, "stack", {
      value: error.stack + new_lines.join("\n")
    });
  }
  handled_errors.add(error);
  throw error;
}

function update_reaction(reaction) {
  var _a, _b, previous_deps = new_deps, previous_skipped_deps = skipped_deps, previous_untracked_writes = untracked_writes, previous_reaction = active_reaction, previous_skip_reaction = skip_reaction, prev_derived_sources = derived_sources, previous_component_context = component_context, flags = reaction.f;
  new_deps = null;
  skipped_deps = 0;
  untracked_writes = null;
  active_reaction = !(flags & (BRANCH_EFFECT | ROOT_EFFECT)) ? reaction : null;
  skip_reaction = !is_flushing_effect && !!(flags & UNOWNED);
  derived_sources = null;
  component_context = reaction.ctx;
  try {
    var result = (0, reaction.fn)(), deps = reaction.deps;
    if (null !== new_deps) {
      var i;
      remove_reactions(reaction, skipped_deps);
      if (null !== deps && skipped_deps > 0) {
        deps.length = skipped_deps + new_deps.length;
        for (i = 0; i < new_deps.length; i++) deps[skipped_deps + i] = new_deps[i];
      } else reaction.deps = deps = new_deps;
      if (!skip_reaction) for (i = skipped_deps; i < deps.length; i++) (null != (_b = (_a = deps[i]).reactions) ? _b : _a.reactions = []).push(reaction);
    } else if (null !== deps && skipped_deps < deps.length) {
      remove_reactions(reaction, skipped_deps);
      deps.length = skipped_deps;
    }
    return result;
  } finally {
    new_deps = previous_deps;
    skipped_deps = previous_skipped_deps;
    untracked_writes = previous_untracked_writes;
    active_reaction = previous_reaction;
    skip_reaction = previous_skip_reaction;
    derived_sources = prev_derived_sources;
    component_context = previous_component_context;
  }
}

function remove_reaction(signal, dependency) {
  let reactions = dependency.reactions;
  if (null !== reactions) {
    var index2 = reactions.indexOf(signal);
    if (-1 !== index2) {
      var new_length = reactions.length - 1;
      if (0 === new_length) reactions = dependency.reactions = null; else {
        reactions[index2] = reactions[new_length];
        reactions.pop();
      }
    }
  }
  if (!(null !== reactions || !(dependency.f & DERIVED) || null !== new_deps && new_deps.includes(dependency))) {
    set_signal_status(dependency, MAYBE_DIRTY);
    if (!(dependency.f & (UNOWNED | DISCONNECTED))) dependency.f ^= DISCONNECTED;
    remove_reactions(dependency, 0);
  }
}

function remove_reactions(signal, start_index) {
  var dependencies = signal.deps;
  if (null !== dependencies) for (var i = start_index; i < dependencies.length; i++) remove_reaction(signal, dependencies[i]);
}

function update_effect(effect2) {
  var flags = effect2.f;
  if (!(flags & DESTROYED)) {
    set_signal_status(effect2, CLEAN);
    var previous_effect = active_effect, previous_component_context = component_context;
    active_effect = effect2;
    if (DEV) {
      var previous_component_fn = dev_current_component_function;
      dev_current_component_function = effect2.component_function;
    }
    try {
      destroy_effect_deriveds(effect2);
      if (!!(flags & BLOCK_EFFECT)) destroy_block_effect_children(effect2); else destroy_effect_children(effect2);
      execute_effect_teardown(effect2);
      var teardown2 = update_reaction(effect2);
      effect2.teardown = "function" == typeof teardown2 ? teardown2 : null;
      effect2.version = current_version;
      if (DEV) dev_effect_stack.push(effect2);
    } catch (error) {
      handle_error(error, effect2, previous_component_context);
    } finally {
      active_effect = previous_effect;
      if (DEV) dev_current_component_function = previous_component_fn;
    }
  }
}

function infinite_loop_guard() {
  if (flush_count > 1e3) {
    flush_count = 0;
    if (DEV) try {
      effect_update_depth_exceeded();
    } catch (error) {
      define_property(error, "stack", {
        value: ""
      });
      console.error("Last ten effects were: ", dev_effect_stack.slice(-10).map((d => d.fn)));
      dev_effect_stack = [];
      throw error;
    } else effect_update_depth_exceeded();
  }
  flush_count++;
}

function flush_queued_root_effects(root_effects) {
  var length = root_effects.length;
  if (0 !== length) {
    infinite_loop_guard();
    var previously_flushing_effect = is_flushing_effect;
    is_flushing_effect = true;
    try {
      for (var i = 0; i < length; i++) {
        var effect2 = root_effects[i];
        if (!(effect2.f & CLEAN)) effect2.f ^= CLEAN;
        var collected_effects = [];
        process_effects(effect2, collected_effects);
        flush_queued_effects(collected_effects);
      }
    } finally {
      is_flushing_effect = previously_flushing_effect;
    }
  }
}

function flush_queued_effects(effects) {
  var length = effects.length;
  if (0 !== length) for (var i = 0; i < length; i++) {
    var effect2 = effects[i];
    if (!(effect2.f & (DESTROYED | INERT)) && check_dirtiness(effect2)) {
      update_effect(effect2);
      if (null === effect2.deps && null === effect2.first && null === effect2.nodes_start) if (null === effect2.teardown) unlink_effect(effect2); else effect2.fn = null;
    }
  }
}

function process_deferred() {
  is_micro_task_queued2 = false;
  if (flush_count > 1001) return;
  const previous_queued_root_effects = queued_root_effects;
  queued_root_effects = [];
  flush_queued_root_effects(previous_queued_root_effects);
  if (!is_micro_task_queued2) {
    flush_count = 0;
    if (DEV) dev_effect_stack = [];
  }
}

function schedule_effect(signal) {
  if (scheduler_mode === FLUSH_MICROTASK) if (!is_micro_task_queued2) {
    is_micro_task_queued2 = true;
    queueMicrotask(process_deferred);
  }
  for (var effect2 = signal; null !== effect2.parent; ) {
    var flags = (effect2 = effect2.parent).f;
    if (!!(flags & (ROOT_EFFECT | BRANCH_EFFECT))) {
      if (!(flags & CLEAN)) return;
      effect2.f ^= CLEAN;
    }
  }
  queued_root_effects.push(effect2);
}

function process_effects(effect2, collected_effects) {
  var current_effect = effect2.first, effects = [];
  main_loop: for (;null !== current_effect; ) {
    var flags = current_effect.f, is_branch = !!(flags & BRANCH_EFFECT);
    if (!(is_branch && !!(flags & CLEAN) || flags & INERT)) if (!!(flags & RENDER_EFFECT)) {
      if (is_branch) current_effect.f ^= CLEAN; else if (check_dirtiness(current_effect)) update_effect(current_effect);
      var child2 = current_effect.first;
      if (null !== child2) {
        current_effect = child2;
        continue;
      }
    } else if (!!(flags & EFFECT)) effects.push(current_effect);
    var sibling2 = current_effect.next;
    if (null === sibling2) {
      let parent = current_effect.parent;
      for (;null !== parent; ) {
        if (effect2 === parent) break main_loop;
        var parent_sibling = parent.next;
        if (null !== parent_sibling) {
          current_effect = parent_sibling;
          continue main_loop;
        }
        parent = parent.parent;
      }
    }
    current_effect = sibling2;
  }
  for (var i = 0; i < effects.length; i++) {
    child2 = effects[i];
    collected_effects.push(child2);
    process_effects(child2, collected_effects);
  }
}

function flush_sync(fn) {
  var previous_scheduler_mode = scheduler_mode, previous_queued_root_effects = queued_root_effects;
  try {
    infinite_loop_guard();
    const root_effects = [];
    scheduler_mode = FLUSH_SYNC;
    queued_root_effects = root_effects;
    is_micro_task_queued2 = false;
    flush_queued_root_effects(previous_queued_root_effects);
    var result = null == fn ? void 0 : fn();
    flush_tasks();
    if (queued_root_effects.length > 0 || root_effects.length > 0) flush_sync();
    flush_count = 0;
    if (DEV) dev_effect_stack = [];
    return result;
  } finally {
    scheduler_mode = previous_scheduler_mode;
    queued_root_effects = previous_queued_root_effects;
  }
}

function get(signal) {
  var _a, _b, flags = signal.f, is_derived = !!(flags & DERIVED);
  if (is_derived && !!(flags & DESTROYED)) {
    var value = execute_derived(signal);
    destroy_derived(signal);
    return value;
  }
  if (is_signals_recorded) captured_signals.add(signal);
  if (null !== active_reaction) {
    if (null !== derived_sources && derived_sources.includes(signal)) state_unsafe_local_read();
    var deps = active_reaction.deps;
    if (null === new_deps && null !== deps && deps[skipped_deps] === signal) skipped_deps++; else if (null === new_deps) new_deps = [ signal ]; else new_deps.push(signal);
    if (null !== untracked_writes && null !== active_effect && !!(active_effect.f & CLEAN) && !(active_effect.f & BRANCH_EFFECT) && untracked_writes.includes(signal)) {
      set_signal_status(active_effect, DIRTY);
      schedule_effect(active_effect);
    }
  } else if (is_derived && null === signal.deps) {
    var derived3 = signal, parent = derived3.parent;
    if (null !== parent && !(null == (_a = parent.deriveds) ? void 0 : _a.includes(derived3))) (null != (_b = parent.deriveds) ? _b : parent.deriveds = []).push(derived3);
  }
  if (is_derived) if (check_dirtiness(derived3 = signal)) update_derived(derived3);
  return signal.v;
}

function untrack(fn) {
  const previous_reaction = active_reaction;
  try {
    active_reaction = null;
    return fn();
  } finally {
    active_reaction = previous_reaction;
  }
}

var STATUS_MASK = ~(DIRTY | MAYBE_DIRTY | CLEAN);

function set_signal_status(signal, status) {
  signal.f = signal.f & STATUS_MASK | status;
}

function getContext(key) {
  const result = get_or_init_context_map("getContext").get(key);
  if (DEV) {
    const fn = component_context.function;
    if (fn) add_owner(result, fn, true);
  }
  return result;
}

function setContext(key, context) {
  get_or_init_context_map("setContext").set(key, context);
  return context;
}

function get_or_init_context_map(name) {
  var _a;
  if (null === component_context) lifecycle_outside_component(name);
  return null != (_a = component_context.c) ? _a : component_context.c = new Map(get_parent_context(component_context) || void 0);
}

function get_parent_context(component_context2) {
  let parent = component_context2.p;
  for (;null !== parent; ) {
    const context_map = parent.c;
    if (null !== context_map) return context_map;
    parent = parent.p;
  }
  return null;
}

function push(props, runes = false, fn) {
  component_context = {
    p: component_context,
    c: null,
    e: null,
    m: false,
    s: props,
    x: null,
    l: null
  };
  if (!runes) component_context.l = {
    s: null,
    u: null,
    r1: [],
    r2: source(false)
  };
  if (DEV) {
    component_context.function = fn;
    dev_current_component_function = fn;
  }
}

function pop(component2) {
  var _a, _b;
  const context_stack_item = component_context;
  if (null !== context_stack_item) {
    if (void 0 !== component2) context_stack_item.x = component2;
    const component_effects = context_stack_item.e;
    if (null !== component_effects) {
      var previous_effect = active_effect, previous_reaction = active_reaction;
      context_stack_item.e = null;
      try {
        for (var i = 0; i < component_effects.length; i++) {
          var component_effect = component_effects[i];
          set_active_effect(component_effect.effect);
          set_active_reaction(component_effect.reaction);
          effect(component_effect.fn);
        }
      } finally {
        set_active_effect(previous_effect);
        set_active_reaction(previous_reaction);
      }
    }
    component_context = context_stack_item.p;
    if (DEV) dev_current_component_function = null != (_b = null == (_a = context_stack_item.p) ? void 0 : _a.function) ? _b : null;
    context_stack_item.m = true;
  }
  return component2 || {};
}

if (DEV) {
  let throw_rune_error = function(rune) {
    if (!(rune in globalThis)) {
      let value;
      Object.defineProperty(globalThis, rune, {
        configurable: true,
        get: () => {
          if (void 0 !== value) return value;
          rune_outside_svelte(rune);
        },
        set: v => {
          value = v;
        }
      });
    }
  };
  throw_rune_error("$state");
  throw_rune_error("$effect");
  throw_rune_error("$derived");
  throw_rune_error("$inspect");
  throw_rune_error("$props");
  throw_rune_error("$bindable");
}

var hydrate_node, $window, $document, first_child_getter, next_sibling_getter, hydrating = false;

function set_hydrating(value) {
  hydrating = value;
}

function set_hydrate_node(node) {
  if (null === node) {
    hydration_mismatch();
    throw HYDRATION_ERROR;
  }
  return hydrate_node = node;
}

function hydrate_next() {
  return set_hydrate_node(get_next_sibling(hydrate_node));
}

function reset(node) {
  if (hydrating) {
    if (null !== get_next_sibling(hydrate_node)) {
      hydration_mismatch();
      throw HYDRATION_ERROR;
    }
    hydrate_node = node;
  }
}

function next(count = 1) {
  if (hydrating) {
    for (var i = count, node = hydrate_node; i--; ) node = get_next_sibling(node);
    hydrate_node = node;
  }
}

function remove_nodes() {
  for (var depth = 0, node = hydrate_node; ;) {
    if (8 === node.nodeType) {
      var data = node.data;
      if (data === HYDRATION_END) {
        if (0 === depth) return node;
        depth -= 1;
      } else if (data === HYDRATION_START || data === HYDRATION_START_ELSE) depth += 1;
    }
    var next2 = get_next_sibling(node);
    node.remove();
    node = next2;
  }
}

function proxy(value, parent = null, prev) {
  var _a, _b;
  if ("object" != typeof value || null === value || STATE_SYMBOL in value) return value;
  const prototype = get_prototype_of(value);
  if (prototype !== object_prototype && prototype !== array_prototype) return value;
  var metadata, sources = new Map, is_proxied_array = is_array(value), version = source(0);
  if (is_proxied_array) sources.set("length", source(value.length));
  if (DEV) {
    metadata = {
      parent,
      owners: null
    };
    if (prev) {
      const prev_owners = null == (_b = null == (_a = prev.v) ? void 0 : _a[STATE_SYMBOL_METADATA]) ? void 0 : _b.owners;
      metadata.owners = prev_owners ? new Set(prev_owners) : null;
    } else metadata.owners = null === parent ? null !== component_context ? new Set([ component_context.function ]) : null : new Set;
  }
  return new Proxy(value, {
    defineProperty(_, prop2, descriptor) {
      if (!("value" in descriptor) || false === descriptor.configurable || false === descriptor.enumerable || false === descriptor.writable) state_descriptors_fixed();
      var s = sources.get(prop2);
      if (void 0 === s) {
        s = source(descriptor.value);
        sources.set(prop2, s);
      } else set(s, proxy(descriptor.value, metadata));
      return true;
    },
    deleteProperty(target, prop2) {
      var s = sources.get(prop2);
      if (void 0 === s) {
        if (prop2 in target) sources.set(prop2, source(UNINITIALIZED));
      } else {
        if (is_proxied_array && "string" == typeof prop2) {
          var ls = sources.get("length"), n = Number(prop2);
          if (Number.isInteger(n) && n < ls.v) set(ls, n);
        }
        set(s, UNINITIALIZED);
        update_version(version);
      }
      return true;
    },
    get(target, prop2, receiver) {
      var _a2;
      if (DEV && prop2 === STATE_SYMBOL_METADATA) return metadata;
      if (prop2 === STATE_SYMBOL) return value;
      var s = sources.get(prop2), exists = prop2 in target;
      if (void 0 === s && (!exists || (null == (_a2 = get_descriptor(target, prop2)) ? void 0 : _a2.writable))) {
        s = source(proxy(exists ? target[prop2] : UNINITIALIZED, metadata));
        sources.set(prop2, s);
      }
      if (void 0 !== s) {
        var v = get(s);
        if (DEV) {
          var prop_metadata = null == v ? void 0 : v[STATE_SYMBOL_METADATA];
          if (prop_metadata && (null == prop_metadata ? void 0 : prop_metadata.parent) !== metadata) widen_ownership(metadata, prop_metadata);
        }
        return v === UNINITIALIZED ? void 0 : v;
      }
      return Reflect.get(target, prop2, receiver);
    },
    getOwnPropertyDescriptor(target, prop2) {
      var descriptor = Reflect.getOwnPropertyDescriptor(target, prop2);
      if (descriptor && "value" in descriptor) {
        var s = sources.get(prop2);
        if (s) descriptor.value = get(s);
      } else if (void 0 === descriptor) {
        var source2 = sources.get(prop2), value2 = null == source2 ? void 0 : source2.v;
        if (void 0 !== source2 && value2 !== UNINITIALIZED) return {
          enumerable: true,
          configurable: true,
          value: value2,
          writable: true
        };
      }
      return descriptor;
    },
    has(target, prop2) {
      var _a2;
      if (DEV && prop2 === STATE_SYMBOL_METADATA) return true;
      if (prop2 === STATE_SYMBOL) return true;
      var s = sources.get(prop2), has = void 0 !== s && s.v !== UNINITIALIZED || Reflect.has(target, prop2);
      if (void 0 !== s || null !== active_effect && (!has || (null == (_a2 = get_descriptor(target, prop2)) ? void 0 : _a2.writable))) {
        if (void 0 === s) {
          s = source(has ? proxy(target[prop2], metadata) : UNINITIALIZED);
          sources.set(prop2, s);
        }
        if (get(s) === UNINITIALIZED) return false;
      }
      return has;
    },
    set(target, prop2, value2, receiver) {
      var _a2, s = sources.get(prop2), has = prop2 in target;
      if (is_proxied_array && "length" === prop2) for (var i = value2; i < s.v; i += 1) {
        var other_s = sources.get(i + "");
        if (void 0 !== other_s) set(other_s, UNINITIALIZED); else if (i in target) {
          other_s = source(UNINITIALIZED);
          sources.set(i + "", other_s);
        }
      }
      if (void 0 === s) {
        if (!has || (null == (_a2 = get_descriptor(target, prop2)) ? void 0 : _a2.writable)) {
          set(s = source(void 0), proxy(value2, metadata));
          sources.set(prop2, s);
        }
      } else {
        has = s.v !== UNINITIALIZED;
        set(s, proxy(value2, metadata));
      }
      if (DEV) {
        var prop_metadata = null == value2 ? void 0 : value2[STATE_SYMBOL_METADATA];
        if (prop_metadata && (null == prop_metadata ? void 0 : prop_metadata.parent) !== metadata) widen_ownership(metadata, prop_metadata);
        check_ownership(metadata);
      }
      var descriptor = Reflect.getOwnPropertyDescriptor(target, prop2);
      if (null == descriptor ? void 0 : descriptor.set) descriptor.set.call(receiver, value2);
      if (!has) {
        if (is_proxied_array && "string" == typeof prop2) {
          var ls = sources.get("length"), n = Number(prop2);
          if (Number.isInteger(n) && n >= ls.v) set(ls, n + 1);
        }
        update_version(version);
      }
      return true;
    },
    ownKeys(target) {
      get(version);
      var own_keys = Reflect.ownKeys(target).filter((key2 => {
        var source3 = sources.get(key2);
        return void 0 === source3 || source3.v !== UNINITIALIZED;
      }));
      for (var [key, source2] of sources) if (source2.v !== UNINITIALIZED && !(key in target)) own_keys.push(key);
      return own_keys;
    },
    setPrototypeOf() {
      state_prototype_fixed();
    }
  });
}

function update_version(signal, d = 1) {
  set(signal, signal.v + d);
}

function get_proxied_value(value) {
  if (null !== value && "object" == typeof value && STATE_SYMBOL in value) return value[STATE_SYMBOL]; else return value;
}

function init_array_prototype_warnings() {
  const array_prototype2 = Array.prototype, cleanup = Array.__svelte_cleanup;
  if (cleanup) cleanup();
  const {indexOf, lastIndexOf, includes} = array_prototype2;
  array_prototype2.indexOf = function(item, from_index) {
    const index2 = indexOf.call(this, item, from_index);
    if (-1 === index2) if (-1 !== indexOf.call(get_proxied_value(this), get_proxied_value(item), from_index)) state_proxy_equality_mismatch("array.indexOf(...)");
    return index2;
  };
  array_prototype2.lastIndexOf = function(item, from_index) {
    const index2 = lastIndexOf.call(this, item, null != from_index ? from_index : this.length - 1);
    if (-1 === index2) if (-1 !== lastIndexOf.call(get_proxied_value(this), get_proxied_value(item), null != from_index ? from_index : this.length - 1)) state_proxy_equality_mismatch("array.lastIndexOf(...)");
    return index2;
  };
  array_prototype2.includes = function(item, from_index) {
    const has = includes.call(this, item, from_index);
    if (!has) if (includes.call(get_proxied_value(this), get_proxied_value(item), from_index)) state_proxy_equality_mismatch("array.includes(...)");
    return has;
  };
  Array.__svelte_cleanup = () => {
    array_prototype2.indexOf = indexOf;
    array_prototype2.lastIndexOf = lastIndexOf;
    array_prototype2.includes = includes;
  };
}

function init_operations() {
  if (void 0 === $window) {
    $window = window;
    $document = document;
    var element_prototype = Element.prototype, node_prototype = Node.prototype;
    first_child_getter = get_descriptor(node_prototype, "firstChild").get;
    next_sibling_getter = get_descriptor(node_prototype, "nextSibling").get;
    element_prototype.__click = void 0;
    element_prototype.__className = "";
    element_prototype.__attributes = null;
    element_prototype.__styles = null;
    element_prototype.__e = void 0;
    Text.prototype.__t = void 0;
    if (DEV) {
      element_prototype.__svelte_meta = null;
      init_array_prototype_warnings();
    }
  }
}

function create_text(value = "") {
  return document.createTextNode(value);
}

function get_first_child(node) {
  return first_child_getter.call(node);
}

function get_next_sibling(node) {
  return next_sibling_getter.call(node);
}

function child(node, is_text) {
  if (!hydrating) return get_first_child(node);
  var child2 = get_first_child(hydrate_node);
  if (null === child2) child2 = hydrate_node.appendChild(create_text()); else if (is_text && 3 !== child2.nodeType) {
    var text2 = create_text();
    null == child2 || child2.before(text2);
    set_hydrate_node(text2);
    return text2;
  }
  set_hydrate_node(child2);
  return child2;
}

function first_child(fragment, is_text) {
  var _a, _b;
  if (!hydrating) {
    var first = get_first_child(fragment);
    if (first instanceof Comment && "" === first.data) return get_next_sibling(first); else return first;
  }
  if (is_text && 3 !== (null == (_a = hydrate_node) ? void 0 : _a.nodeType)) {
    var text2 = create_text();
    null == (_b = hydrate_node) || _b.before(text2);
    set_hydrate_node(text2);
    return text2;
  }
  return hydrate_node;
}

function sibling(node, count = 1, is_text = false) {
  let next_sibling = hydrating ? hydrate_node : node;
  for (;count--; ) next_sibling = get_next_sibling(next_sibling);
  if (!hydrating) return next_sibling;
  var type = next_sibling.nodeType;
  if (is_text && 3 !== type) {
    var text2 = create_text();
    null == next_sibling || next_sibling.before(text2);
    set_hydrate_node(text2);
    return text2;
  }
  set_hydrate_node(next_sibling);
  return next_sibling;
}

function clear_text_content(node) {
  node.textContent = "";
}

function validate_effect(rune) {
  if (null === active_effect && null === active_reaction) effect_orphan(rune);
  if (null !== active_reaction && !!(active_reaction.f & UNOWNED)) effect_in_unowned_derived();
  if (is_destroying_effect) effect_in_teardown(rune);
}

function push_effect(effect2, parent_effect) {
  var parent_last = parent_effect.last;
  if (null === parent_last) parent_effect.last = parent_effect.first = effect2; else {
    parent_last.next = effect2;
    effect2.prev = parent_last;
    parent_effect.last = effect2;
  }
}

function create_effect(type, fn, sync, push2 = true) {
  var _a, is_root = !!(type & ROOT_EFFECT), parent_effect = active_effect;
  if (DEV) for (;null !== parent_effect && !!(parent_effect.f & INSPECT_EFFECT); ) parent_effect = parent_effect.parent;
  var effect2 = {
    ctx: component_context,
    deps: null,
    deriveds: null,
    nodes_start: null,
    nodes_end: null,
    f: type | DIRTY,
    first: null,
    fn,
    last: null,
    next: null,
    parent: is_root ? null : parent_effect,
    prev: null,
    teardown: null,
    transitions: null,
    version: 0
  };
  if (DEV) effect2.component_function = dev_current_component_function;
  if (sync) {
    var previously_flushing_effect = is_flushing_effect;
    try {
      set_is_flushing_effect(true);
      update_effect(effect2);
      effect2.f |= EFFECT_RAN;
    } catch (e) {
      destroy_effect(effect2);
      throw e;
    } finally {
      set_is_flushing_effect(previously_flushing_effect);
    }
  } else if (null !== fn) schedule_effect(effect2);
  if (!(sync && null === effect2.deps && null === effect2.first && null === effect2.nodes_start && null === effect2.teardown && !(effect2.f & EFFECT_HAS_DERIVED)) && !is_root && push2) {
    if (null !== parent_effect) push_effect(effect2, parent_effect);
    if (null !== active_reaction && !!(active_reaction.f & DERIVED)) {
      var derived3 = active_reaction;
      (null != (_a = derived3.children) ? _a : derived3.children = []).push(effect2);
    }
  }
  return effect2;
}

function teardown(fn) {
  const effect2 = create_effect(RENDER_EFFECT, null, false);
  set_signal_status(effect2, CLEAN);
  effect2.teardown = fn;
  return effect2;
}

function user_effect(fn) {
  var _a;
  validate_effect("$effect");
  var defer = null !== active_effect && !!(active_effect.f & BRANCH_EFFECT) && null !== component_context && !component_context.m;
  if (DEV) define_property(fn, "name", {
    value: "$effect"
  });
  if (defer) {
    var context = component_context;
    (null != (_a = context.e) ? _a : context.e = []).push({
      fn,
      effect: active_effect,
      reaction: active_reaction
    });
  } else return effect(fn);
}

function effect_root(fn) {
  const effect2 = create_effect(ROOT_EFFECT, fn, true);
  return () => {
    destroy_effect(effect2);
  };
}

function effect(fn) {
  return create_effect(EFFECT, fn, false);
}

function render_effect(fn) {
  return create_effect(RENDER_EFFECT, fn, true);
}

function template_effect(fn) {
  if (DEV) define_property(fn, "name", {
    value: "{expression}"
  });
  return block(fn);
}

function block(fn, flags = 0) {
  return create_effect(RENDER_EFFECT | BLOCK_EFFECT | flags, fn, true);
}

function branch(fn, push2 = true) {
  return create_effect(RENDER_EFFECT | BRANCH_EFFECT, fn, true, push2);
}

function execute_effect_teardown(effect2) {
  var teardown2 = effect2.teardown;
  if (null !== teardown2) {
    const previously_destroying_effect = is_destroying_effect, previous_reaction = active_reaction;
    set_is_destroying_effect(true);
    set_active_reaction(null);
    try {
      teardown2.call(null);
    } finally {
      set_is_destroying_effect(previously_destroying_effect);
      set_active_reaction(previous_reaction);
    }
  }
}

function destroy_effect_deriveds(signal) {
  var deriveds = signal.deriveds;
  if (null !== deriveds) {
    signal.deriveds = null;
    for (var i = 0; i < deriveds.length; i += 1) destroy_derived(deriveds[i]);
  }
}

function destroy_effect_children(signal, remove_dom = false) {
  var effect2 = signal.first;
  signal.first = signal.last = null;
  for (;null !== effect2; ) {
    var next2 = effect2.next;
    destroy_effect(effect2, remove_dom);
    effect2 = next2;
  }
}

function destroy_block_effect_children(signal) {
  for (var effect2 = signal.first; null !== effect2; ) {
    var next2 = effect2.next;
    if (!(effect2.f & BRANCH_EFFECT)) destroy_effect(effect2);
    effect2 = next2;
  }
}

function destroy_effect(effect2, remove_dom = true) {
  var removed = false;
  if ((remove_dom || !!(effect2.f & HEAD_EFFECT)) && null !== effect2.nodes_start) {
    for (var node = effect2.nodes_start, end = effect2.nodes_end; null !== node; ) {
      var next2 = node === end ? null : get_next_sibling(node);
      node.remove();
      node = next2;
    }
    removed = true;
  }
  destroy_effect_deriveds(effect2);
  destroy_effect_children(effect2, remove_dom && !removed);
  remove_reactions(effect2, 0);
  set_signal_status(effect2, DESTROYED);
  var transitions = effect2.transitions;
  if (null !== transitions) for (const transition2 of transitions) transition2.stop();
  execute_effect_teardown(effect2);
  var parent = effect2.parent;
  if (null !== parent && null !== parent.first) unlink_effect(effect2);
  if (DEV) effect2.component_function = null;
  effect2.next = effect2.prev = effect2.teardown = effect2.ctx = effect2.deps = effect2.parent = effect2.fn = effect2.nodes_start = effect2.nodes_end = null;
}

function unlink_effect(effect2) {
  var parent = effect2.parent, prev = effect2.prev, next2 = effect2.next;
  if (null !== prev) prev.next = next2;
  if (null !== next2) next2.prev = prev;
  if (null !== parent) {
    if (parent.first === effect2) parent.first = next2;
    if (parent.last === effect2) parent.last = prev;
  }
}

function pause_effect(effect2, callback) {
  var transitions = [];
  pause_children(effect2, transitions, true);
  run_out_transitions(transitions, (() => {
    destroy_effect(effect2);
    if (callback) callback();
  }));
}

function run_out_transitions(transitions, fn) {
  var remaining = transitions.length;
  if (remaining > 0) {
    var check = () => --remaining || fn();
    for (var transition2 of transitions) transition2.out(check);
  } else fn();
}

function pause_children(effect2, transitions, local) {
  if (!(effect2.f & INERT)) {
    effect2.f ^= INERT;
    if (null !== effect2.transitions) for (const transition2 of effect2.transitions) if (transition2.is_global || local) transitions.push(transition2);
    for (var child2 = effect2.first; null !== child2; ) {
      var sibling2 = child2.next;
      pause_children(child2, transitions, !!(child2.f & EFFECT_TRANSPARENT) || !!(child2.f & BRANCH_EFFECT) ? local : false);
      child2 = sibling2;
    }
  }
}

function resume_effect(effect2) {
  resume_children(effect2, true);
}

function resume_children(effect2, local) {
  if (effect2.f & INERT) {
    effect2.f ^= INERT;
    if (check_dirtiness(effect2)) update_effect(effect2);
    for (var child2 = effect2.first; null !== child2; ) {
      var sibling2 = child2.next;
      resume_children(child2, !!(child2.f & EFFECT_TRANSPARENT) || !!(child2.f & BRANCH_EFFECT) ? local : false);
      child2 = sibling2;
    }
    if (null !== effect2.transitions) for (const transition2 of effect2.transitions) if (transition2.is_global || local) transition2.in();
  }
}

var all_styles = new Map;

function register_style(hash2, style) {
  var styles = all_styles.get(hash2);
  if (!styles) {
    styles = new Set;
    all_styles.set(hash2, styles);
  }
  styles.add(style);
}

var head_anchor, all_registered_events = new Set, root_event_handles = new Set;

function create_event(event_name, dom, handler, options) {
  function target_handler(event2) {
    if (!options.capture) handle_event_propagation.call(dom, event2);
    if (!event2.cancelBubble) {
      var previous_reaction = active_reaction, previous_effect = active_effect;
      set_active_reaction(null);
      set_active_effect(null);
      try {
        return handler.call(this, event2);
      } finally {
        set_active_reaction(previous_reaction);
        set_active_effect(previous_effect);
      }
    }
  }
  if (event_name.startsWith("pointer") || event_name.startsWith("touch") || "wheel" === event_name) queue_micro_task((() => {
    dom.addEventListener(event_name, target_handler, options);
  })); else dom.addEventListener(event_name, target_handler, options);
  return target_handler;
}

function event(event_name, dom, handler, capture, passive2) {
  var options = {
    capture,
    passive: passive2
  }, target_handler = create_event(event_name, dom, handler, options);
  if (dom === document.body || dom === window || dom === document) teardown((() => {
    dom.removeEventListener(event_name, target_handler, options);
  }));
}

function delegate(events) {
  for (var i = 0; i < events.length; i++) all_registered_events.add(events[i]);
  for (var fn of root_event_handles) fn(events);
}

function handle_event_propagation(event2) {
  var _a, owner_document = this.ownerDocument, event_name = event2.type, path = (null == (_a = event2.composedPath) ? void 0 : _a.call(event2)) || [], current_target = path[0] || event2.target, path_idx = 0, handled_at = event2.__root;
  if (handled_at) {
    var at_idx = path.indexOf(handled_at);
    if (-1 !== at_idx && (this === document || this === window)) {
      event2.__root = this;
      return;
    }
    var handler_idx = path.indexOf(this);
    if (-1 === handler_idx) return;
    if (at_idx <= handler_idx) path_idx = at_idx;
  }
  if ((current_target = path[path_idx] || event2.target) !== this) {
    define_property(event2, "currentTarget", {
      configurable: true,
      get: () => current_target || owner_document
    });
    var previous_reaction = active_reaction, previous_effect = active_effect;
    set_active_reaction(null);
    set_active_effect(null);
    try {
      for (var throw_error, other_errors = []; null !== current_target; ) {
        var parent_element = current_target.assignedSlot || current_target.parentNode || current_target.host || null;
        try {
          var delegated = current_target["__" + event_name];
          if (void 0 !== delegated && !current_target.disabled) if (is_array(delegated)) {
            var [fn, ...data] = delegated;
            fn.apply(current_target, [ event2, ...data ]);
          } else delegated.call(current_target, event2);
        } catch (error) {
          if (throw_error) other_errors.push(error); else throw_error = error;
        }
        if (event2.cancelBubble || parent_element === this || null === parent_element) break;
        current_target = parent_element;
      }
      if (throw_error) {
        for (let error of other_errors) queueMicrotask((() => {
          throw error;
        }));
        throw throw_error;
      }
    } finally {
      event2.__root = this;
      delete event2.currentTarget;
      set_active_reaction(previous_reaction);
      set_active_effect(previous_effect);
    }
  }
}

function reset_head_anchor() {
  head_anchor = void 0;
}

function create_fragment_from_html(html2) {
  var elem = document.createElement("template");
  elem.innerHTML = html2;
  return elem.content;
}

function assign_nodes(start, end) {
  var effect2 = active_effect;
  if (null === effect2.nodes_start) {
    effect2.nodes_start = start;
    effect2.nodes_end = end;
  }
}

function template(content, flags) {
  var node, is_fragment = !!(flags & TEMPLATE_FRAGMENT), use_import_node = !!(flags & TEMPLATE_USE_IMPORT_NODE), has_start = !content.startsWith("<!>");
  return () => {
    if (hydrating) {
      assign_nodes(hydrate_node, null);
      return hydrate_node;
    }
    if (void 0 === node) {
      node = create_fragment_from_html(has_start ? content : "<!>" + content);
      if (!is_fragment) node = get_first_child(node);
    }
    var clone = use_import_node ? document.importNode(node, true) : node.cloneNode(true);
    if (is_fragment) assign_nodes(get_first_child(clone), clone.lastChild); else assign_nodes(clone, clone);
    return clone;
  };
}

function comment() {
  if (hydrating) {
    assign_nodes(hydrate_node, null);
    return hydrate_node;
  }
  var frag = document.createDocumentFragment(), start = document.createComment(""), anchor = create_text();
  frag.append(start, anchor);
  assign_nodes(start, anchor);
  return frag;
}

function append(anchor, dom) {
  if (!hydrating) {
    if (null !== anchor) anchor.before(dom);
  } else {
    active_effect.nodes_end = hydrate_node;
    hydrate_next();
  }
}

var regex_return_characters = /\r/g;

function hash(str) {
  let hash2 = 5381, i = (str = str.replace(regex_return_characters, "")).length;
  for (;i--; ) hash2 = (hash2 << 5) - hash2 ^ str.charCodeAt(i);
  return (hash2 >>> 0).toString(36);
}

var DOM_BOOLEAN_ATTRIBUTES = [ "allowfullscreen", "async", "autofocus", "autoplay", "checked", "controls", "default", "disabled", "formnovalidate", "hidden", "indeterminate", "ismap", "loop", "multiple", "muted", "nomodule", "novalidate", "open", "playsinline", "readonly", "required", "reversed", "seamless", "selected", "webkitdirectory" ], DOM_PROPERTIES = [ ...DOM_BOOLEAN_ATTRIBUTES, "formNoValidate", "isMap", "noModule", "playsInline", "readOnly", "value", "inert", "volume" ], PASSIVE_EVENTS = [ "touchstart", "touchmove" ];

function is_passive_event(name) {
  return PASSIVE_EVENTS.includes(name);
}

var should_intro = true;

function set_text(text2, value) {
  var _a, str = null == value ? "" : "object" == typeof value ? value + "" : value;
  if (str !== (null != (_a = text2.__t) ? _a : text2.__t = text2.nodeValue)) {
    text2.__t = str;
    text2.nodeValue = null == str ? "" : str + "";
  }
}

function mount(component2, options) {
  return _mount(component2, options);
}

function hydrate(component2, options) {
  var _a;
  init_operations();
  options.intro = null != (_a = options.intro) ? _a : false;
  const target = options.target, was_hydrating = hydrating, previous_hydrate_node = hydrate_node;
  try {
    for (var anchor = get_first_child(target); anchor && (8 !== anchor.nodeType || anchor.data !== HYDRATION_START); ) anchor = get_next_sibling(anchor);
    if (!anchor) throw HYDRATION_ERROR;
    set_hydrating(true);
    set_hydrate_node(anchor);
    hydrate_next();
    const instance = _mount(component2, {
      ...options,
      anchor
    });
    if (null === hydrate_node || 8 !== hydrate_node.nodeType || hydrate_node.data !== HYDRATION_END) {
      hydration_mismatch();
      throw HYDRATION_ERROR;
    }
    set_hydrating(false);
    return instance;
  } catch (error) {
    if (error === HYDRATION_ERROR) {
      if (false === options.recover) hydration_failed();
      init_operations();
      clear_text_content(target);
      set_hydrating(false);
      return mount(component2, options);
    }
    throw error;
  } finally {
    set_hydrating(was_hydrating);
    set_hydrate_node(previous_hydrate_node);
    reset_head_anchor();
  }
}

var document_listeners = new Map;

function _mount(Component, {target, anchor, props = {}, events, context, intro = true}) {
  init_operations();
  var registered_events = new Set, event_handle = events2 => {
    for (var i = 0; i < events2.length; i++) {
      var event_name = events2[i];
      if (!registered_events.has(event_name)) {
        registered_events.add(event_name);
        var passive2 = is_passive_event(event_name);
        target.addEventListener(event_name, handle_event_propagation, {
          passive: passive2
        });
        var n = document_listeners.get(event_name);
        if (void 0 === n) {
          document.addEventListener(event_name, handle_event_propagation, {
            passive: passive2
          });
          document_listeners.set(event_name, 1);
        } else document_listeners.set(event_name, n + 1);
      }
    }
  };
  event_handle(array_from(all_registered_events));
  root_event_handles.add(event_handle);
  var component2 = void 0, unmount2 = effect_root((() => {
    var anchor_node = null != anchor ? anchor : target.appendChild(create_text());
    branch((() => {
      if (context) {
        push({});
        component_context.c = context;
      }
      if (events) props.$$events = events;
      if (hydrating) assign_nodes(anchor_node, null);
      should_intro = intro;
      component2 = Component(anchor_node, props) || {};
      should_intro = true;
      if (hydrating) active_effect.nodes_end = hydrate_node;
      if (context) pop();
    }));
    return () => {
      var _a;
      for (var event_name of registered_events) {
        target.removeEventListener(event_name, handle_event_propagation);
        var n = document_listeners.get(event_name);
        if (0 == --n) {
          document.removeEventListener(event_name, handle_event_propagation);
          document_listeners.delete(event_name);
        } else document_listeners.set(event_name, n);
      }
      root_event_handles.delete(event_handle);
      mounted_components.delete(component2);
      if (anchor_node !== anchor) null == (_a = anchor_node.parentNode) || _a.removeChild(anchor_node);
    };
  }));
  mounted_components.set(component2, unmount2);
  return component2;
}

var mounted_components = new WeakMap;

function unmount(component2) {
  const fn = mounted_components.get(component2);
  if (fn) fn(); else if (DEV) lifecycle_double_unmount();
}

function if_block(node, get_condition, consequent_fn, alternate_fn = null, elseif = false) {
  if (hydrating) hydrate_next();
  var anchor = node, consequent_effect = null, alternate_effect = null, condition = null;
  block((() => {
    if (condition === (condition = !!get_condition())) return;
    let mismatch = false;
    if (hydrating) {
      const is_else = anchor.data === HYDRATION_START_ELSE;
      if (condition === is_else) {
        set_hydrate_node(anchor = remove_nodes());
        set_hydrating(false);
        mismatch = true;
      }
    }
    if (condition) {
      if (consequent_effect) resume_effect(consequent_effect); else consequent_effect = branch((() => consequent_fn(anchor)));
      if (alternate_effect) pause_effect(alternate_effect, (() => {
        alternate_effect = null;
      }));
    } else {
      if (alternate_effect) resume_effect(alternate_effect); else if (alternate_fn) alternate_effect = branch((() => alternate_fn(anchor)));
      if (consequent_effect) pause_effect(consequent_effect, (() => {
        consequent_effect = null;
      }));
    }
    if (mismatch) set_hydrating(true);
  }), elseif ? EFFECT_TRANSPARENT : 0);
  if (hydrating) anchor = hydrate_node;
}

var current_each_item = null;

function index(_, i) {
  return i;
}

function pause_effects(state2, items, controlled_anchor, items_map) {
  for (var transitions = [], length = items.length, i = 0; i < length; i++) pause_children(items[i].e, transitions, true);
  var is_controlled = length > 0 && 0 === transitions.length && null !== controlled_anchor;
  if (is_controlled) {
    var parent_node = controlled_anchor.parentNode;
    clear_text_content(parent_node);
    parent_node.append(controlled_anchor);
    items_map.clear();
    link(state2, items[0].prev, items[length - 1].next);
  }
  run_out_transitions(transitions, (() => {
    for (var i2 = 0; i2 < length; i2++) {
      var item = items[i2];
      if (!is_controlled) {
        items_map.delete(item.k);
        link(state2, item.prev, item.next);
      }
      destroy_effect(item.e, !is_controlled);
    }
  }));
}

function each(node, flags, get_collection, get_key, render_fn, fallback_fn = null) {
  var anchor = node, state2 = {
    flags,
    items: new Map,
    first: null
  };
  if (!!(flags & EACH_IS_CONTROLLED)) {
    var parent_node = node;
    anchor = hydrating ? set_hydrate_node(get_first_child(parent_node)) : parent_node.appendChild(create_text());
  }
  if (hydrating) hydrate_next();
  var fallback2 = null, was_empty = false;
  block((() => {
    var collection = get_collection(), array = is_array(collection) ? collection : null == collection ? [] : array_from(collection), length = array.length;
    if (was_empty && 0 === length) return;
    was_empty = 0 === length;
    let mismatch = false;
    if (hydrating) if (anchor.data === HYDRATION_START_ELSE != (0 === length)) {
      set_hydrate_node(anchor = remove_nodes());
      set_hydrating(false);
      mismatch = true;
    }
    if (hydrating) {
      for (var item, prev = null, i = 0; i < length; i++) {
        if (8 === hydrate_node.nodeType && hydrate_node.data === HYDRATION_END) {
          anchor = hydrate_node;
          mismatch = true;
          set_hydrating(false);
          break;
        }
        var value = array[i], key = get_key(value, i);
        item = create_item(hydrate_node, state2, prev, null, value, key, i, render_fn, flags);
        state2.items.set(key, item);
        prev = item;
      }
      if (length > 0) set_hydrate_node(remove_nodes());
    }
    if (!hydrating) reconcile(array, state2, anchor, render_fn, flags, get_key);
    if (null !== fallback_fn) if (0 === length) if (fallback2) resume_effect(fallback2); else fallback2 = branch((() => fallback_fn(anchor))); else if (null !== fallback2) pause_effect(fallback2, (() => {
      fallback2 = null;
    }));
    if (mismatch) set_hydrating(true);
    get_collection();
  }));
  if (hydrating) anchor = hydrate_node;
}

function reconcile(array, state2, anchor, render_fn, flags, get_key) {
  var _a, _b, _c, _d, seen, to_animate, value, key, item, i, is_animated = !!(flags & EACH_IS_ANIMATED), should_update = !!(flags & (EACH_ITEM_REACTIVE | EACH_INDEX_REACTIVE)), length = array.length, items = state2.items, current = state2.first, prev = null, matched = [], stashed = [];
  if (is_animated) for (i = 0; i < length; i += 1) {
    key = get_key(value = array[i], i);
    if (void 0 !== (item = items.get(key))) {
      null == (_a = item.a) || _a.measure();
      (null != to_animate ? to_animate : to_animate = new Set).add(item);
    }
  }
  for (i = 0; i < length; i += 1) {
    key = get_key(value = array[i], i);
    if (void 0 !== (item = items.get(key))) {
      if (should_update) update_item(item, value, i, flags);
      if (!!(item.e.f & INERT)) {
        resume_effect(item.e);
        if (is_animated) {
          null == (_b = item.a) || _b.unfix();
          (null != to_animate ? to_animate : to_animate = new Set).delete(item);
        }
      }
      if (item !== current) {
        if (void 0 !== seen && seen.has(item)) {
          if (matched.length < stashed.length) {
            var j, start = stashed[0];
            prev = start.prev;
            var a = matched[0], b = matched[matched.length - 1];
            for (j = 0; j < matched.length; j += 1) move(matched[j], start, anchor);
            for (j = 0; j < stashed.length; j += 1) seen.delete(stashed[j]);
            link(state2, a.prev, b.next);
            link(state2, prev, a);
            link(state2, b, start);
            current = start;
            prev = b;
            i -= 1;
            matched = [];
            stashed = [];
          } else {
            seen.delete(item);
            move(item, current, anchor);
            link(state2, item.prev, item.next);
            link(state2, item, null === prev ? state2.first : prev.next);
            link(state2, prev, item);
            prev = item;
          }
          continue;
        }
        matched = [];
        stashed = [];
        for (;null !== current && current.k !== key; ) {
          if (!(current.e.f & INERT)) (null != seen ? seen : seen = new Set).add(current);
          stashed.push(current);
          current = current.next;
        }
        if (null === current) continue;
        item = current;
      }
      matched.push(item);
      prev = item;
      current = item.next;
    } else {
      prev = create_item(current ? current.e.nodes_start : anchor, state2, prev, null === prev ? state2.first : prev.next, value, key, i, render_fn, flags);
      items.set(key, prev);
      matched = [];
      stashed = [];
      current = prev.next;
    }
  }
  if (null !== current || void 0 !== seen) {
    for (var to_destroy = void 0 === seen ? [] : array_from(seen); null !== current; ) {
      if (!(current.e.f & INERT)) to_destroy.push(current);
      current = current.next;
    }
    var destroy_length = to_destroy.length;
    if (destroy_length > 0) {
      var controlled_anchor = !!(flags & EACH_IS_CONTROLLED) && 0 === length ? anchor : null;
      if (is_animated) {
        for (i = 0; i < destroy_length; i += 1) null == (_c = to_destroy[i].a) || _c.measure();
        for (i = 0; i < destroy_length; i += 1) null == (_d = to_destroy[i].a) || _d.fix();
      }
      pause_effects(state2, to_destroy, controlled_anchor, items);
    }
  }
  if (is_animated) queue_micro_task((() => {
    var _a2;
    if (void 0 !== to_animate) for (item of to_animate) null == (_a2 = item.a) || _a2.apply();
  }));
  active_effect.first = state2.first && state2.first.e;
  active_effect.last = prev && prev.e;
}

function update_item(item, value, index2, type) {
  if (!!(type & EACH_ITEM_REACTIVE)) internal_set(item.v, value);
  if (!!(type & EACH_INDEX_REACTIVE)) internal_set(item.i, index2); else item.i = index2;
}

function create_item(anchor, state2, prev, next2, value, key, index2, render_fn, flags) {
  var previous_each_item = current_each_item;
  try {
    var v = !!(flags & EACH_ITEM_REACTIVE) ? !(flags & EACH_ITEM_IMMUTABLE) ? mutable_source(value) : source(value) : value, i = !(flags & EACH_INDEX_REACTIVE) ? index2 : source(index2), item = {
      i,
      v,
      k: key,
      a: null,
      e: null,
      prev,
      next: next2
    };
    current_each_item = item;
    item.e = branch((() => render_fn(anchor, v, i)), hydrating);
    item.e.prev = prev && prev.e;
    item.e.next = next2 && next2.e;
    if (null === prev) state2.first = item; else {
      prev.next = item;
      prev.e.next = item.e;
    }
    if (null !== next2) {
      next2.prev = item;
      next2.e.prev = item.e;
    }
    return item;
  } finally {
    current_each_item = previous_each_item;
  }
}

function move(item, next2, anchor) {
  for (var end = item.next ? item.next.e.nodes_start : anchor, dest = next2 ? next2.e.nodes_start : anchor, node = item.e.nodes_start; node !== end; ) {
    var next_node = get_next_sibling(node);
    dest.before(node);
    node = next_node;
  }
}

function link(state2, prev, next2) {
  if (null === prev) state2.first = next2; else {
    prev.next = next2;
    prev.e.next = next2 && next2.e;
  }
  if (null !== next2) {
    next2.prev = prev;
    next2.e.prev = prev && prev.e;
  }
}

function check_hash(element2, server_hash, value) {
  var _a, _b;
  if (!server_hash || server_hash === hash(String(null != value ? value : ""))) return;
  let location;
  const loc = null == (_a = element2.__svelte_meta) ? void 0 : _a.loc;
  if (loc) location = `near ${loc.file}:${loc.line}:${loc.column}`; else if (null == (_b = dev_current_component_function) ? void 0 : _b[FILENAME]) location = `in ${dev_current_component_function[FILENAME]}`;
  hydration_html_changed(null == location ? void 0 : location.replace(/\//g, "/​"));
}

function html(node, get_value, svg, mathml, skip_warning) {
  var effect2, anchor = node, value = "";
  block((() => {
    var _a;
    if (value !== (value = null != (_a = get_value()) ? _a : "")) {
      if (void 0 !== effect2) {
        destroy_effect(effect2);
        effect2 = void 0;
      }
      if ("" !== value) effect2 = branch((() => {
        if (!hydrating) {
          var html2 = value + "";
          if (svg) html2 = `<svg>${html2}</svg>`; else if (mathml) html2 = `<math>${html2}</math>`;
          var node2 = create_fragment_from_html(html2);
          if (svg || mathml) node2 = get_first_child(node2);
          assign_nodes(get_first_child(node2), node2.lastChild);
          if (svg || mathml) for (;get_first_child(node2); ) anchor.before(get_first_child(node2)); else anchor.before(node2);
        } else {
          for (var hash2 = hydrate_node.data, next2 = hydrate_next(), last = next2; null !== next2 && (8 !== next2.nodeType || "" !== next2.data); ) {
            last = next2;
            next2 = get_next_sibling(next2);
          }
          if (null === next2) {
            hydration_mismatch();
            throw HYDRATION_ERROR;
          }
          if (DEV && !skip_warning) check_hash(next2.parentNode, hash2, value);
          assign_nodes(hydrate_node, last);
          anchor = set_hydrate_node(next2);
        }
      }));
    } else if (hydrating) hydrate_next();
  }));
}

function snippet(node, get_snippet, ...args) {
  var snippet_effect, anchor = node, snippet2 = noop;
  block((() => {
    if (snippet2 !== (snippet2 = get_snippet())) {
      if (snippet_effect) {
        destroy_effect(snippet_effect);
        snippet_effect = null;
      }
      if (DEV && null == snippet2) invalid_snippet();
      snippet_effect = branch((() => snippet2(anchor, ...args)));
    }
  }), EFFECT_TRANSPARENT);
  if (hydrating) anchor = hydrate_node;
}

function append_styles(anchor, css) {
  queue_micro_task((() => {
    var _a, root6 = anchor.getRootNode(), target = root6.host ? root6 : null != (_a = root6.head) ? _a : root6.ownerDocument.head;
    if (!target.querySelector("#" + css.hash)) {
      const style = document.createElement("style");
      style.id = css.hash;
      style.textContent = css.code;
      target.appendChild(style);
      if (DEV) register_style(css.hash, style);
    }
  }));
}

var listening_to_form_reset = false;

function add_form_reset_listener() {
  if (!listening_to_form_reset) {
    listening_to_form_reset = true;
    document.addEventListener("reset", (evt => {
      Promise.resolve().then((() => {
        var _a;
        if (!evt.defaultPrevented) for (const e of evt.target.elements) null == (_a = e.__on_r) || _a.call(e);
      }));
    }), {
      capture: true
    });
  }
}

function remove_input_defaults(input) {
  if (hydrating) {
    var already_removed = false, remove_defaults = () => {
      if (!already_removed) {
        already_removed = true;
        if (input.hasAttribute("value")) {
          var value = input.value;
          set_attribute(input, "value", null);
          input.value = value;
        }
        if (input.hasAttribute("checked")) {
          var checked = input.checked;
          set_attribute(input, "checked", null);
          input.checked = checked;
        }
      }
    };
    input.__on_r = remove_defaults;
    queue_idle_task(remove_defaults);
    add_form_reset_listener();
  }
}

function set_attribute(element2, attribute, value, skip_warning) {
  var _a, attributes = null != (_a = element2.__attributes) ? _a : element2.__attributes = {};
  if (hydrating) {
    attributes[attribute] = element2.getAttribute(attribute);
    if ("src" === attribute || "srcset" === attribute || "href" === attribute && "LINK" === element2.nodeName) {
      if (!skip_warning) check_src_in_dev_hydration(element2, attribute, null != value ? value : "");
      return;
    }
  }
  if (attributes[attribute] !== (attributes[attribute] = value)) {
    if ("style" === attribute && "__styles" in element2) element2.__styles = {};
    if ("loading" === attribute) element2[LOADING_ATTR_SYMBOL] = value;
    if (null == value) element2.removeAttribute(attribute); else if ("string" != typeof value && get_setters(element2).includes(attribute)) element2[attribute] = value; else element2.setAttribute(attribute, value);
  }
}

var setters_cache = new Map;

function get_setters(element2) {
  var descriptors, setters = setters_cache.get(element2.nodeName);
  if (setters) return setters;
  setters_cache.set(element2.nodeName, setters = []);
  for (var proto = get_prototype_of(element2), element_proto = Element.prototype; element_proto !== proto; ) {
    descriptors = get_descriptors(proto);
    for (var key in descriptors) if (descriptors[key].set) setters.push(key);
    proto = get_prototype_of(proto);
  }
  return setters;
}

function check_src_in_dev_hydration(element2, attribute, value) {
  var _a;
  if (DEV) if ("srcset" !== attribute || !srcset_url_equal(element2, value)) if (!src_url_equal(null != (_a = element2.getAttribute(attribute)) ? _a : "", value)) hydration_attribute_changed(attribute, element2.outerHTML.replace(element2.innerHTML, element2.innerHTML && "..."), String(value));
}

function src_url_equal(element_src, url) {
  if (element_src === url) return true; else return new URL(element_src, document.baseURI).href === new URL(url, document.baseURI).href;
}

function split_srcset(srcset) {
  return srcset.split(",").map((src => src.trim().split(" ").filter(Boolean)));
}

function srcset_url_equal(element2, srcset) {
  var element_urls = split_srcset(element2.srcset), urls = split_srcset(srcset);
  return urls.length === element_urls.length && urls.every((([url, width], i) => width === element_urls[i][1] && (src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))));
}

function set_class(dom, value) {
  var prev_class_name = dom.__className, next_class_name = to_class(value);
  if (hydrating && dom.className === next_class_name) dom.__className = next_class_name; else if (prev_class_name !== next_class_name || hydrating && dom.className !== next_class_name) {
    if (null == value) dom.removeAttribute("class"); else dom.className = next_class_name;
    dom.__className = next_class_name;
  }
}

function to_class(value) {
  return null == value ? "" : value;
}

function toggle_class(dom, class_name, value) {
  if (value) {
    if (dom.classList.contains(class_name)) return;
    dom.classList.add(class_name);
  } else {
    if (!dom.classList.contains(class_name)) return;
    dom.classList.remove(class_name);
  }
}

function listen_to_event_and_reset_event(element2, event2, handler, on_reset = handler) {
  element2.addEventListener(event2, handler);
  const prev = element2.__on_r;
  if (prev) element2.__on_r = () => {
    prev();
    on_reset();
  }; else element2.__on_r = on_reset;
  add_form_reset_listener();
}

function bind_value(input, get3, set2 = get3) {
  var runes = is_runes();
  listen_to_event_and_reset_event(input, "input", (() => {
    if (DEV && "checkbox" === input.type) bind_invalid_checkbox_value();
    var value = is_numberlike_input(input) ? to_number(input.value) : input.value;
    set2(value);
    if (runes && value !== (value = get3())) input.value = null != value ? value : "";
  }));
  render_effect((() => {
    if (DEV && "checkbox" === input.type) bind_invalid_checkbox_value();
    var value = get3();
    if (!hydrating || input.defaultValue === input.value) {
      if (!is_numberlike_input(input) || value !== to_number(input.value)) if ("date" !== input.type || value || input.value) if (value !== input.value) input.value = null != value ? value : "";
    } else set2(input.value);
  }));
}

function is_numberlike_input(input) {
  var type = input.type;
  return "number" === type || "range" === type;
}

function to_number(value) {
  return "" === value ? null : +value;
}

function is_bound_this(bound_value, element_or_component) {
  return bound_value === element_or_component || (null == bound_value ? void 0 : bound_value[STATE_SYMBOL]) === element_or_component;
}

function bind_this(element_or_component = {}, update2, get_value, get_parts) {
  effect((() => {
    var old_parts, parts;
    render_effect((() => {
      old_parts = parts;
      parts = (null == get_parts ? void 0 : get_parts()) || [];
      untrack((() => {
        if (element_or_component !== get_value(...parts)) {
          update2(element_or_component, ...parts);
          if (old_parts && is_bound_this(get_value(...old_parts), element_or_component)) update2(null, ...old_parts);
        }
      }));
    }));
    return () => {
      queue_micro_task((() => {
        if (parts && is_bound_this(get_value(...parts), element_or_component)) update2(null, ...parts);
      }));
    };
  }));
  return element_or_component;
}

var _events, _instance, is_store_binding = false;

function store_get(store, store_name, stores) {
  var _a;
  const entry = null != (_a = stores[store_name]) ? _a : stores[store_name] = {
    store: null,
    source: mutable_source(void 0),
    unsubscribe: noop
  };
  if (entry.store !== store) {
    entry.unsubscribe();
    entry.store = null != store ? store : null;
    if (null == store) {
      entry.source.v = void 0;
      entry.unsubscribe = noop;
    } else {
      var is_synchronous_callback = true;
      entry.unsubscribe = subscribe_to_store(store, (v => {
        if (is_synchronous_callback) entry.source.v = v; else set(entry.source, v);
      }));
      is_synchronous_callback = false;
    }
  }
  return get(entry.source);
}

function store_set(store, value) {
  store.set(value);
  return value;
}

function setup_stores() {
  const stores = {};
  teardown((() => {
    for (var store_name in stores) stores[store_name].unsubscribe();
  }));
  return stores;
}

function capture_store_binding(fn) {
  var previous_is_store_binding = is_store_binding;
  try {
    is_store_binding = false;
    return [ fn(), is_store_binding ];
  } finally {
    is_store_binding = previous_is_store_binding;
  }
}

function with_parent_branch(fn) {
  for (var effect2 = active_effect, previous_effect = active_effect; null !== effect2 && !(effect2.f & (BRANCH_EFFECT | ROOT_EFFECT)); ) effect2 = effect2.parent;
  try {
    set_active_effect(effect2);
    return fn();
  } finally {
    set_active_effect(previous_effect);
  }
}

function prop(props, key, flags, fallback2) {
  var _a, prop_value, immutable = !!(flags & PROPS_IS_IMMUTABLE), runes = !!(flags & PROPS_IS_RUNES), bindable = !!(flags & PROPS_IS_BINDABLE), lazy = !!(flags & PROPS_IS_LAZY_INITIAL), is_store_sub = false;
  if (bindable) [prop_value, is_store_sub] = capture_store_binding((() => props[key])); else prop_value = props[key];
  var getter, setter = null == (_a = get_descriptor(props, key)) ? void 0 : _a.set, fallback_value = fallback2, fallback_dirty = true, fallback_used = false, get_fallback = () => {
    fallback_used = true;
    if (fallback_dirty) {
      fallback_dirty = false;
      if (lazy) fallback_value = untrack(fallback2); else fallback_value = fallback2;
    }
    return fallback_value;
  };
  if (void 0 === prop_value && void 0 !== fallback2) {
    if (setter && runes) props_invalid_value(key);
    prop_value = get_fallback();
    if (setter) setter(prop_value);
  }
  if (runes) getter = () => {
    var value = props[key];
    if (void 0 === value) return get_fallback();
    fallback_dirty = true;
    fallback_used = false;
    return value;
  }; else {
    var derived_getter = with_parent_branch((() => (immutable ? derived : derived_safe_equal)((() => props[key]))));
    derived_getter.f |= LEGACY_DERIVED_PROP;
    getter = () => {
      var value = get(derived_getter);
      if (void 0 !== value) fallback_value = void 0;
      return void 0 === value ? fallback_value : value;
    };
  }
  if (!(flags & PROPS_IS_UPDATED)) return getter;
  if (setter) {
    var legacy_parent = props.$$legacy;
    return function(value, mutation) {
      if (arguments.length > 0) {
        if (!runes || !mutation || legacy_parent || is_store_sub) setter(mutation ? getter() : value);
        return value;
      } else return getter();
    };
  }
  var from_child = false, was_from_child = false, inner_current_value = mutable_source(prop_value), current_value = with_parent_branch((() => derived((() => {
    var parent_value = getter(), child_value = get(inner_current_value);
    if (from_child || void 0 === parent_value && !!(active_reaction.f & DESTROYED)) {
      from_child = false;
      was_from_child = true;
      return child_value;
    }
    was_from_child = false;
    return inner_current_value.v = parent_value;
  }))));
  if (!immutable) current_value.equals = safe_equals;
  return function(value, mutation) {
    if (is_signals_recorded) {
      from_child = was_from_child;
      getter();
      get(inner_current_value);
    }
    if (arguments.length > 0) {
      const new_value = mutation ? get(current_value) : runes && bindable ? proxy(value) : value;
      if (!current_value.equals(new_value)) {
        from_child = true;
        set(inner_current_value, new_value);
        if (fallback_used && void 0 !== fallback_value) fallback_value = new_value;
        untrack((() => get(current_value)));
      }
      return value;
    }
    return get(current_value);
  };
}

function createClassComponent(options) {
  return new Svelte4Component(options);
}

var SvelteElement, Svelte4Component = class {
  constructor(options) {
    __privateAdd(this, _events);
    __privateAdd(this, _instance);
    var _a, _b, sources = new Map, add_source = (key, value) => {
      var s = mutable_source(value);
      sources.set(key, s);
      return s;
    };
    const props = new Proxy({
      ...options.props || {},
      $$events: {}
    }, {
      get(target, prop2) {
        var _a2;
        return get(null != (_a2 = sources.get(prop2)) ? _a2 : add_source(prop2, Reflect.get(target, prop2)));
      },
      has(target, prop2) {
        var _a2;
        get(null != (_a2 = sources.get(prop2)) ? _a2 : add_source(prop2, Reflect.get(target, prop2)));
        return Reflect.has(target, prop2);
      },
      set(target, prop2, value) {
        var _a2;
        set(null != (_a2 = sources.get(prop2)) ? _a2 : add_source(prop2, value), value);
        return Reflect.set(target, prop2, value);
      }
    });
    __privateSet(this, _instance, (options.hydrate ? hydrate : mount)(options.component, {
      target: options.target,
      props,
      context: options.context,
      intro: null != (_a = options.intro) ? _a : false,
      recover: options.recover
    }));
    if (!(null == (_b = null == options ? void 0 : options.props) ? void 0 : _b.$$host) || false === options.sync) flush_sync();
    __privateSet(this, _events, props.$$events);
    for (const key of Object.keys(__privateGet(this, _instance))) if ("$set" !== key && "$destroy" !== key && "$on" !== key) define_property(this, key, {
      get() {
        return __privateGet(this, _instance)[key];
      },
      set(value) {
        __privateGet(this, _instance)[key] = value;
      },
      enumerable: true
    });
    __privateGet(this, _instance).$set = next2 => {
      Object.assign(props, next2);
    };
    __privateGet(this, _instance).$destroy = () => {
      unmount(__privateGet(this, _instance));
    };
  }
  $set(props) {
    __privateGet(this, _instance).$set(props);
  }
  $on(event2, callback) {
    __privateGet(this, _events)[event2] = __privateGet(this, _events)[event2] || [];
    const cb = (...args) => callback.call(this, ...args);
    __privateGet(this, _events)[event2].push(cb);
    return () => {
      __privateGet(this, _events)[event2] = __privateGet(this, _events)[event2].filter((fn => fn !== cb));
    };
  }
  $destroy() {
    __privateGet(this, _instance).$destroy();
  }
};

_events = new WeakMap;

_instance = new WeakMap;

if ("function" == typeof HTMLElement) SvelteElement = class extends HTMLElement {
  constructor($$componentCtor, $$slots, use_shadow_dom) {
    super();
    __publicField(this, "$$ctor");
    __publicField(this, "$$s");
    __publicField(this, "$$c");
    __publicField(this, "$$cn", false);
    __publicField(this, "$$d", {});
    __publicField(this, "$$r", false);
    __publicField(this, "$$p_d", {});
    __publicField(this, "$$l", {});
    __publicField(this, "$$l_u", new Map);
    __publicField(this, "$$me");
    this.$$ctor = $$componentCtor;
    this.$$s = $$slots;
    if (use_shadow_dom) this.attachShadow({
      mode: "open"
    });
  }
  addEventListener(type, listener, options) {
    this.$$l[type] = this.$$l[type] || [];
    this.$$l[type].push(listener);
    if (this.$$c) {
      const unsub = this.$$c.$on(type, listener);
      this.$$l_u.set(listener, unsub);
    }
    super.addEventListener(type, listener, options);
  }
  removeEventListener(type, listener, options) {
    super.removeEventListener(type, listener, options);
    if (this.$$c) {
      const unsub = this.$$l_u.get(listener);
      if (unsub) {
        unsub();
        this.$$l_u.delete(listener);
      }
    }
  }
  async connectedCallback() {
    this.$$cn = true;
    if (!this.$$c) {
      let create_slot = function(name) {
        return anchor => {
          const slot2 = document.createElement("slot");
          if ("default" !== name) slot2.name = name;
          append(anchor, slot2);
        };
      };
      await Promise.resolve();
      if (!this.$$cn || this.$$c) return;
      const $$slots = {}, existing_slots = get_custom_elements_slots(this);
      for (const name of this.$$s) if (name in existing_slots) if ("default" === name && !this.$$d.children) {
        this.$$d.children = create_slot(name);
        $$slots.default = true;
      } else $$slots[name] = create_slot(name);
      for (const attribute of this.attributes) {
        const name = this.$$g_p(attribute.name);
        if (!(name in this.$$d)) this.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, "toProp");
      }
      for (const key in this.$$p_d) if (!(key in this.$$d) && void 0 !== this[key]) {
        this.$$d[key] = this[key];
        delete this[key];
      }
      this.$$c = createClassComponent({
        component: this.$$ctor,
        target: this.shadowRoot || this,
        props: {
          ...this.$$d,
          $$slots,
          $$host: this
        }
      });
      this.$$me = effect_root((() => {
        render_effect((() => {
          var _a;
          this.$$r = true;
          for (const key of object_keys(this.$$c)) {
            if (!(null == (_a = this.$$p_d[key]) ? void 0 : _a.reflect)) continue;
            this.$$d[key] = this.$$c[key];
            const attribute_value = get_custom_element_value(key, this.$$d[key], this.$$p_d, "toAttribute");
            if (null == attribute_value) this.removeAttribute(this.$$p_d[key].attribute || key); else this.setAttribute(this.$$p_d[key].attribute || key, attribute_value);
          }
          this.$$r = false;
        }));
      }));
      for (const type in this.$$l) for (const listener of this.$$l[type]) {
        const unsub = this.$$c.$on(type, listener);
        this.$$l_u.set(listener, unsub);
      }
      this.$$l = {};
    }
  }
  attributeChangedCallback(attr, _oldValue, newValue) {
    var _a;
    if (!this.$$r) {
      attr = this.$$g_p(attr);
      this.$$d[attr] = get_custom_element_value(attr, newValue, this.$$p_d, "toProp");
      null == (_a = this.$$c) || _a.$set({
        [attr]: this.$$d[attr]
      });
    }
  }
  disconnectedCallback() {
    this.$$cn = false;
    Promise.resolve().then((() => {
      if (!this.$$cn && this.$$c) {
        this.$$c.$destroy();
        this.$$me();
        this.$$c = void 0;
      }
    }));
  }
  $$g_p(attribute_name) {
    return object_keys(this.$$p_d).find((key => this.$$p_d[key].attribute === attribute_name || !this.$$p_d[key].attribute && key.toLowerCase() === attribute_name)) || attribute_name;
  }
};

function get_custom_element_value(prop2, value, props_definition, transform) {
  var _a;
  const type = null == (_a = props_definition[prop2]) ? void 0 : _a.type;
  value = "Boolean" === type && "boolean" != typeof value ? null != value : value;
  if (!transform || !props_definition[prop2]) return value; else if ("toAttribute" === transform) switch (type) {
   case "Object":
   case "Array":
    return null == value ? null : JSON.stringify(value);

   case "Boolean":
    return value ? "" : null;

   case "Number":
    return null == value ? null : value;

   default:
    return value;
  } else switch (type) {
   case "Object":
   case "Array":
    return value && JSON.parse(value);

   case "Boolean":
    return value;

   case "Number":
    return null != value ? +value : value;

   default:
    return value;
  }
}

function get_custom_elements_slots(element2) {
  const result = {};
  element2.childNodes.forEach((node => {
    result[node.slot || "default"] = true;
  }));
  return result;
}

function onMount(fn) {
  if (null === component_context) lifecycle_outside_component("onMount");
  if (null !== component_context.l) init_update_callbacks(component_context).m.push(fn); else user_effect((() => {
    const cleanup = untrack(fn);
    if ("function" == typeof cleanup) return cleanup;
  }));
}

function onDestroy(fn) {
  if (null === component_context) lifecycle_outside_component("onDestroy");
  onMount((() => () => untrack(fn)));
}

function init_update_callbacks(context) {
  var _a, l = context.l;
  return null != (_a = l.u) ? _a : l.u = {
    a: [],
    b: [],
    m: []
  };
}

function subscribe_to_store(store, run2, invalidate) {
  if (null == store) {
    run2(void 0);
    if (invalidate) invalidate(void 0);
    return noop;
  }
  const unsub = untrack((() => store.subscribe(run2, invalidate)));
  return unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;
}

var subscriber_queue = [];

function writable(value, start = noop) {
  let stop = null;
  const subscribers = new Set;
  function set2(new_value) {
    if (safe_not_equal(value, new_value)) {
      value = new_value;
      if (stop) {
        const run_queue = !subscriber_queue.length;
        for (const subscriber of subscribers) {
          subscriber[1]();
          subscriber_queue.push(subscriber, value);
        }
        if (run_queue) {
          for (let i = 0; i < subscriber_queue.length; i += 2) subscriber_queue[i][0](subscriber_queue[i + 1]);
          subscriber_queue.length = 0;
        }
      }
    }
  }
  function update2(fn) {
    set2(fn(value));
  }
  return {
    set: set2,
    update: update2,
    subscribe: function subscribe(run2, invalidate = noop) {
      const subscriber = [ run2, invalidate ];
      subscribers.add(subscriber);
      if (1 === subscribers.size) stop = start(set2, update2) || noop;
      run2(value);
      return () => {
        subscribers.delete(subscriber);
        if (0 === subscribers.size && stop) {
          stop();
          stop = null;
        }
      };
    }
  };
}

var currentFile = writable(""), maxDepth = writable(0), searchString = writable(""), tagInfo = writable({}), tagFolderSetting = writable(DEFAULT_SETTINGS), selectedTags = writable(), allViewItems = writable(), allViewItemsByLink = writable(), appliedFiles = writable(), v2expandedTags = writable(new Set), performHide = writable(0), pluginInstance = writable(void 0);

function unique(items) {
  return [ ...new Set([ ...items ]) ];
}

function trimSlash(src, keepStart = false, keepEnd = false) {
  const st = keepStart ? 0 : "/" == src[0] ? 1 : 0, end = keepEnd ? void 0 : src.endsWith("/") ? -1 : void 0;
  if (0 == st && null == end) return src; else return src.slice(st, end);
}

function trimPrefix(source2, prefix) {
  if (source2.startsWith(prefix)) return source2.substring(prefix.length); else return source2;
}

function ancestorToTags(ancestors) {
  return [ ...ancestors ].reduce(((p, i) => "/" != i[0] ? [ ...p, i ] : [ ...p, p.pop() + "/" + i.substring(1) ]), []);
}

function ancestorToLongestTag(ancestors) {
  return ancestors.reduceRight(((a, e) => {
    var _a;
    return !a ? [ e ] : (null == (_a = a[0]) ? void 0 : _a.startsWith(e)) ? a : [ e, ...a ];
  }), []);
}

function isSpecialTag(tagSrc) {
  const tag = trimSlash(tagSrc);
  return "_untagged" == tag || tag in tagDispDict;
}

var tagDispAlternativeDict = {};

tagInfo.subscribe((tagInfo2 => {
  tagDispAlternativeDict = {
    ...tagDispDict
  };
  if (null == tagInfo2) return;
  const items = Object.entries(tagInfo2);
  for (const [key, info] of items) if (null == info ? void 0 : info.alt) tagDispAlternativeDict[key] = info.alt;
}));

function renderSpecialTag(tagSrc) {
  const tag = trimSlash(tagSrc);
  return tag in tagDispAlternativeDict ? tagDispAlternativeDict[tag] : tagSrc;
}

function secondsToFreshness(totalAsMSec) {
  const totalAsSec = ~~(totalAsMSec / 1e3), totalSec = ~~(totalAsSec * (totalAsSec / Math.abs(totalAsSec)));
  if (totalSec < EPOCH_HOUR) return FRESHNESS_1;
  if (totalSec < 6 * EPOCH_HOUR) return FRESHNESS_2;
  if (totalSec < 3 * EPOCH_DAY) return FRESHNESS_3;
  if (totalSec < 7 * EPOCH_DAY) return FRESHNESS_4; else return FRESHNESS_5;
}

var queues = [];

function waitForRequestAnimationFrame() {
  return new Promise((res => requestAnimationFrame((() => res()))));
}

function delay(num) {
  return new Promise((res => setTimeout((() => res()), num || 5)));
}

function nextTick() {
  return new Promise((res => setTimeout((() => res()), 0)));
}

var waits = [ nextTick, delay, nextTick, delay, delay, nextTick ], waitIdx = 0, pumping = false, startContinuousProcessing = Date.now();

async function pump() {
  if (!pumping) try {
    pumping = true;
    for (;;) {
      const proc = queues.shift();
      if (proc) {
        proc();
        if (Date.now() - startContinuousProcessing > 120) {
          const w = waits[waitIdx];
          waitIdx = (waitIdx + 1) % waits.length;
          await w();
          startContinuousProcessing = Date.now();
        }
      } else break;
    }
  } finally {
    pumping = false;
  }
}

var doEvents = () => new Promise((res => {
  queues.push((() => {
    res();
  }));
  pump();
}));

function compare(x, y) {
  return `${x || ""}`.localeCompare(y, void 0, {
    numeric: true
  });
}

function getTagName(tagName, subtreePrefix, tagInfo2, invert) {
  if (null == tagInfo2) return tagName;
  const prefix = -1 == invert ? "￿" : "", unpinned = 1 == invert ? "￿" : "";
  if (tagName in tagInfo2 && tagInfo2[tagName]) if ("key" in tagInfo2[tagName]) return `${prefix}_${subtreePrefix}_-${tagInfo2[tagName].key}__${tagName}`;
  return `${prefix}_${subtreePrefix}_${unpinned}_${tagName}`;
}

function removeIntermediatePath(paths) {
  const passed = [];
  for (const v of paths) {
    const last = passed.pop();
    if (void 0 !== last) if (!(trimTrailingSlash(v.toLowerCase()) + "/").startsWith(trimTrailingSlash(last.toLowerCase()) + "/")) passed.push(last);
    passed.push(v);
  }
  return passed.reverse();
}

function getTagMark(tagInfo2) {
  if (!tagInfo2) return "";
  if ("key" in tagInfo2) if ("mark" in tagInfo2 && "" != tagInfo2.mark) return tagInfo2.mark; else return "📌"; else if ("mark" in tagInfo2 && "" != tagInfo2.mark) return tagInfo2.mark; else return "";
}

function escapeStringToHTML(str) {
  if (!str) return ""; else return str.replace(/[<>&"'`]/g, (match => ({
    "<": "&lt;",
    ">": "&gt;",
    "&": "&amp;",
    '"': "&quot;",
    "'": "&#39;",
    "`": "&#x60;"
  }[match])));
}

var V2FI_IDX_TAG = 0, V2FI_IDX_TAGNAME = 1, V2FI_IDX_TAGDISP = 2, V2FI_IDX_CHILDREN = 3;

function selectCompareMethodTags(settings, tagInfo2) {
  const _tagInfo = tagInfo2, invert = settings.sortTypeTag.contains("_DESC") ? -1 : 1, subTreeChar = {
    [-1]: "􏿿",
    [1]: "_"
  }, sortByName = (a, b) => {
    const isASubTree = "" == a[V2FI_IDX_TAGDISP][0], isBSubTree = "" == b[V2FI_IDX_TAGDISP][0], aName = a[V2FI_IDX_TAGNAME], bName = b[V2FI_IDX_TAGNAME], aPrefix = isASubTree ? subTreeChar[invert] : "", bPrefix = isBSubTree ? subTreeChar[invert] : "";
    return compare(getTagName(aName, aPrefix, settings.useTagInfo ? _tagInfo : void 0, invert), getTagName(bName, bPrefix, settings.useTagInfo ? _tagInfo : void 0, invert)) * invert;
  };
  switch (settings.sortTypeTag) {
   case "ITEMS_ASC":
   case "ITEMS_DESC":
    return (a, b) => {
      const aName = a[V2FI_IDX_TAGNAME], bName = b[V2FI_IDX_TAGNAME], aCount = a[V2FI_IDX_CHILDREN].length - (settings.useTagInfo && aName in _tagInfo && "key" in _tagInfo[aName] ? 1e5 * invert : 0), bCount = b[V2FI_IDX_CHILDREN].length - (settings.useTagInfo && bName in _tagInfo && "key" in _tagInfo[bName] ? 1e5 * invert : 0);
      if (aCount == bCount) return sortByName(a, b); else return (aCount - bCount) * invert;
    };

   case "NAME_ASC":
   case "NAME_DESC":
    return sortByName;

   default:
    console.warn("Compare method (tags) corrupted");
    return (a, b) => {
      const isASubTree = "" == a[V2FI_IDX_TAGDISP][0], isBSubTree = "" == b[V2FI_IDX_TAGDISP][0], aName = a[V2FI_IDX_TAGNAME], bName = b[V2FI_IDX_TAGNAME];
      return compare((isASubTree ? subTreeChar[invert] : "") + aName, (isBSubTree ? subTreeChar[invert] : "") + bName) * invert;
    };
  }
}

function uniqueCaseIntensive(pieces) {
  const delMap = new Set, ret = [];
  for (const piece of pieces) if (!delMap.has(piece.toLowerCase())) {
    ret.push(piece);
    delMap.add(piece.toLowerCase());
  }
  return ret;
}

function _sorterTagLength(a, b, invert) {
  const diff = a.split("/").length - b.split("/").length;
  if (0 != diff) return diff * (invert ? -1 : 1); else return (a.length - b.length) * (invert ? -1 : 1);
}

function getExtraTags(tags, trail, reduceNestedParent) {
  let tagsLeft = uniqueCaseIntensive(tags), removeTrailItems = [];
  if (reduceNestedParent) removeTrailItems = trail.sort(((a, b) => _sorterTagLength(a, b, true))); else removeTrailItems = removeIntermediatePath(trail);
  for (const t of removeTrailItems) {
    const trimLength = t.endsWith("/") ? t.length : t.length;
    if (reduceNestedParent) tagsLeft = tagsLeft.map((e => (e + "/").toLowerCase().startsWith(t.toLowerCase()) ? e.substring(trimLength) : e)); else {
      const f = tagsLeft.findIndex((e => (e + "/").toLowerCase().startsWith(t.toLowerCase())));
      if (-1 !== f) tagsLeft[f] = tagsLeft[f].substring(trimLength);
    }
  }
  return tagsLeft.filter((e => "" != e.trim()));
}

function trimTrailingSlash(src) {
  return trimSlash(src, true, false);
}

function joinPartialPath(path) {
  return path.reduce(((p, c) => c.endsWith("/") && p.length > 0 ? [ c + p[0], ...p.slice(1) ] : [ c, ...p ]), []);
}

function pathMatch(haystackLC, needleLC) {
  if (haystackLC == needleLC) return true;
  if ("/" == needleLC[needleLC.length - 1]) if (0 === (haystackLC + "/").indexOf(needleLC)) return true;
  return false;
}

function parseTagName(thisName, _tagInfo) {
  let tagNameDisp = [ "" ];
  const names = thisName.split("/").filter((e => "" != e.trim()));
  let inSubTree = false, tagName = "";
  if (names.length > 1) {
    tagName = `${names[names.length - 1]}`;
    inSubTree = true;
  } else tagName = thisName;
  if (tagName.endsWith("/")) tagName = tagName.substring(0, tagName.length - 1);
  const tagMark = getTagMark(tagName in _tagInfo ? _tagInfo[tagName] : void 0);
  tagNameDisp = [ `${tagMark}${renderSpecialTag(tagName)}` ];
  if (inSubTree) tagNameDisp = [ `${tagMark}`, `${renderSpecialTag(tagName)}` ];
  return [ tagName, tagNameDisp ];
}

function parseAllForwardReference(metaCache, filename, passed) {
  var _a;
  return unique(Object.keys(null != (_a = null == metaCache ? void 0 : metaCache[filename]) ? _a : {}).filter((e => !passed.contains(e))));
}

function parseAllReverseReference(metaCache, filename, passed) {
  return unique(Object.entries(metaCache).filter((([, links]) => filename in links)).map((([name]) => name)).filter((e => !passed.contains(e))));
}

function parseAllReference(metaCache, filename, conf) {
  var _a, _b;
  let linked = [ ...!(null == (_a = null == conf ? void 0 : conf.outgoing) ? void 0 : _a.enabled) ? [] : parseAllForwardReference(metaCache, filename, []), ...!(null == (_b = null == conf ? void 0 : conf.incoming) ? void 0 : _b.enabled) ? [] : parseAllReverseReference(metaCache, filename, []) ];
  if (0 != linked.length) linked = unique([ filename, ...linked ]);
  return linked;
}

function fileCacheToCompare(cache) {
  if (!cache) return ""; else return {
    l: cache.links,
    t: cache.tags
  };
}

var allViewItemsMap = new Map;

allViewItemsByLink.subscribe((e => {
  updateItemsLinkMap(e);
}));

function updateItemsLinkMap(e) {
  allViewItemsMap.clear();
  if (e) e.forEach((item => allViewItemsMap.set(item.path, item)));
}

function getViewItemFromPath(path) {
  return allViewItemsMap.get(path);
}

function getAllLinksRecursive(item, trail) {
  const leftLinks = item.links.filter((e => !trail.contains(e))), allChildLinks = leftLinks.flatMap((itemName => {
    const item2 = getViewItemFromPath(itemName);
    if (!item2) return []; else return getAllLinksRecursive(item2, [ ...trail, itemName ]);
  }));
  return unique([ ...leftLinks, ...allChildLinks ]);
}

var waitingProcess = new Map, runningProcess = new Set;

async function scheduleOnceIfDuplicated(key, proc) {
  if (!runningProcess.has(key)) try {
    runningProcess.add(key);
    await delay(3);
    if (waitingProcess.has(key)) {
      const nextProc = waitingProcess.get(key);
      waitingProcess.delete(key);
      runningProcess.delete(key);
      return scheduleOnceIfDuplicated(key, nextProc);
    } else await proc();
  } finally {
    runningProcess.delete(key);
  } else waitingProcess.set(key, proc);
}

function isSameAny(a, b) {
  if (typeof a != typeof b) return false;
  switch (typeof a) {
   case "string":
   case "number":
   case "bigint":
   case "boolean":
   case "symbol":
   case "function":
   case "undefined":
    return a == b;

   case "object":
    if (a === b) return true;
    if (a instanceof Map || a instanceof Set) {
      if (a.size != b.size) return false;
      const v = [ ...a ], w = [ ...b ];
      for (let i = 0; i < v.length; i++) if (v[i] != w[i]) return false;
      return true;
    }
    if (Array.isArray(a)) {
      for (let i = 0; i < a.length; i++) if (!isSameAny(a[i], b[i])) return false;
      return true;
    }
    {
      const x = Object.values(a), y = Object.values(b);
      if (x.length != y.length) return false;
      for (let i = 0; i < x.length; i++) if (!isSameAny(x[i], y[i])) return false;
      return true;
    }

   default:
    return false;
  }
}

var import_obsidian2 = require("obsidian"), PUBLIC_VERSION = "5";

if ("undefined" != typeof window) (window.__svelte || (window.__svelte = {
  v: new Set
})).v.add(PUBLIC_VERSION);

var import_obsidian = require("obsidian"), root = template('<div class="markdownBody svelte-1qfikme" style="min-height: 1em;"></div>'), $$css = {
  hash: "svelte-1qfikme",
  code: "\n\t.markdownBody.svelte-1qfikme {\n\t\tuser-select: text;\n\t\t-webkit-user-select: text;\n\t}\n"
};

function ScrollViewMarkdownComponent($$anchor, $$props) {
  push($$props, true);
  append_styles($$anchor, $$css);
  let file = prop($$props, "file", 19, (() => ({
    path: ""
  }))), el = state(void 0), renderedContent = state("");
  function onAppearing(_) {
    if (file().content && get(el) && get(renderedContent) != file().content) {
      import_obsidian.MarkdownRenderer.render($$props.plugin.app, file().content, get(el), file().path, $$props.plugin);
      set(renderedContent, proxy(file().content));
    }
  }
  onMount((() => {
    if (get(el) && $$props.observer) {
      $$props.observer.observe(get(el));
      get(el).addEventListener("appearing", onAppearing);
    }
  }));
  onDestroy((() => {
    if (get(el) && $$props.observer) {
      $$props.observer.unobserve(get(el));
      get(el).removeEventListener("appearing", onAppearing);
    }
  }));
  user_effect((() => {
    if (get(renderedContent) && file() && file().content && get(el) && get(renderedContent) != file().content) {
      get(el).style.minHeight = `${get(el).clientHeight}px`;
      get(el).innerHTML = "";
      import_obsidian.MarkdownRenderer.render($$props.plugin.app, file().content, get(el), file().path, $$props.plugin);
      set(renderedContent, proxy(file().content));
      get(el).style.minHeight = "20px";
    }
  }));
  var div = root();
  bind_this(div, ($$value => set(el, $$value)), (() => get(el)));
  append($$anchor, div);
  pop();
}

var on_click = (evt, handleOpenFile, file) => handleOpenFile(evt, get(file)), root_1 = template('<div class="file svelte-s1mg0b"><div class="header svelte-s1mg0b"><span> </span> <span class="path svelte-s1mg0b"> </span></div> <!> <hr class="svelte-s1mg0b"></div>'), root2 = template('<div class="x"><div class="header svelte-s1mg0b"> </div> <hr class="svelte-s1mg0b"> <!></div>'), $$css2 = {
  hash: "svelte-s1mg0b",
  code: "\n\t.header.svelte-s1mg0b {\n\t\tbackground-color: var(--background-secondary-alt);\n\t\tposition: sticky;\n\t\ttop: 0;\n\t\tcolor: var(--text-normal);\n\t\tmargin-bottom: 8px;\n\t}\n\t.file.svelte-s1mg0b {\n\t\tcursor: pointer;\n\t}\n\t.path.svelte-s1mg0b {\n\t\tfont-size: 75%;\n\t}\n\thr.svelte-s1mg0b {\n\t\tmargin: 8px auto;\n\t}\n"
};

function ScrollViewComponent($$anchor, $$props) {
  push($$props, true);
  append_styles($$anchor, $$css2);
  const $$stores = setup_stores();
  let store = prop($$props, "store", 19, (() => writable({
    files: [],
    title: "",
    tagPath: ""
  })));
  const _state = derived((() => store_get(store(), "$store", $$stores)));
  let files = derived((() => get(_state).files));
  const tagPath = derived((() => get(_state).tagPath.split(", ").map((e => "#" + trimTrailingSlash(e).split("/").map((e2 => renderSpecialTag(e2.trim()))).join("/"))).join(", ")));
  function handleOpenFile(e, file) {
    $$props.openfile(file.path, false);
    e.preventDefault();
  }
  let scrollEl = state(void 0), observer = state(void 0);
  const onAppearing = new CustomEvent("appearing", {
    detail: {}
  });
  user_effect((() => {
    const options = {
      root: get(scrollEl),
      rootMargin: "10px",
      threshold: 0
    };
    set(observer, proxy(new IntersectionObserver((entries => {
      for (const entry of entries) if (entry.isIntersecting) entry.target.dispatchEvent(onAppearing);
    }), options)));
  }));
  onDestroy((() => {
    null === get(observer) || void 0 === get(observer) || get(observer).disconnect();
  }));
  var div = root2(), div_1 = child(div), text2 = child(div_1);
  reset(div_1);
  each(sibling(div_1, 4), 17, (() => get(files)), index, (($$anchor2, file) => {
    var div_2 = root_1();
    bind_this(div_2, ($$value => set(scrollEl, $$value)), (() => get(scrollEl)));
    div_2.__click = [ on_click, handleOpenFile, file ];
    var div_3 = child(div_2), span = child(div_3), text_1 = child(span, true);
    reset(span);
    var span_1 = sibling(span, 2), text_2 = child(span_1);
    reset(span_1);
    reset(div_3);
    ScrollViewMarkdownComponent(sibling(div_3, 2), {
      get file() {
        return get(file);
      },
      get observer() {
        return get(observer);
      },
      get plugin() {
        return $$props.plugin;
      }
    });
    next(2);
    reset(div_2);
    template_effect((() => {
      var _a;
      set_text(text_1, get(file).title);
      set_text(text_2, `(${null != (_a = get(file).path) ? _a : ""})`);
    }));
    append($$anchor2, div_2);
  }));
  reset(div);
  template_effect((() => {
    var _a;
    return set_text(text2, `Files with ${null != (_a = get(tagPath)) ? _a : ""}`);
  }));
  append($$anchor, div);
  pop();
}

delegate([ "click" ]);

var ScrollView = class extends import_obsidian2.ItemView {
  constructor(leaf, plugin) {
    super(leaf);
    this.icon = "sheets-in-box";
    this.state = {
      files: [],
      title: "",
      tagPath: ""
    };
    this.title = "";
    this.navigation = true;
    this.plugin = plugin;
    this.store = writable({
      files: [],
      title: "",
      tagPath: ""
    });
  }
  getIcon() {
    return "sheets-in-box";
  }
  getViewType() {
    return VIEW_TYPE_SCROLL;
  }
  getDisplayText() {
    return this.state.tagPath || "Tags scroll";
  }
  async setFile(filenames) {
    this.state = {
      ...this.state,
      files: filenames
    };
    await this.updateView();
  }
  async setState(state2, result) {
    this.state = {
      ...state2
    };
    this.title = state2.title;
    await this.updateView();
  }
  getState() {
    return this.state;
  }
  isFileOpened(path) {
    return this.state.files.some((e => e.path == path));
  }
  getScrollViewState() {
    return this.state;
  }
  async updateView() {
    const items = [];
    for (const item of this.state.files) if (item.content) items.push(item); else {
      const f = this.app.vault.getAbstractFileByPath(item.path);
      if (null == f || !(f instanceof import_obsidian2.TFile)) {
        console.log(`File not found:${item.path}`);
        items.push(item);
        continue;
      }
      const title = this.plugin.getFileTitle(f), w = await this.app.vault.read(f);
      await doEvents();
      item.content = w;
      item.title = title;
      items.push(item);
    }
    this.state = {
      ...this.state,
      files: [ ...items ]
    };
    this.store.set(this.state);
  }
  async onOpen() {
    const app = mount(ScrollViewComponent, {
      target: this.contentEl,
      props: {
        store: this.store,
        openfile: this.plugin.focusFile,
        plugin: this.plugin
      }
    });
    this.component = app;
    return await Promise.resolve();
  }
  async onClose() {
    if (this.component) {
      unmount(this.component);
      this.component = void 0;
    }
    return await Promise.resolve();
  }
}, import_obsidian6 = require("obsidian");

function performSortExactFirst(_items, children, leftOverItems) {
  const childrenPathsArr = children.map((e => e[V2FI_IDX_CHILDREN].map((ee => ee.path)))).flat(), childrenPaths = new Set(childrenPathsArr), exactHerePaths = new Set(_items.map((e => e.path)));
  childrenPaths.forEach((path => exactHerePaths.delete(path)));
  return [ ...[ ...leftOverItems ].sort(((a, b) => (exactHerePaths.has(a.path) ? -1 : 0) + (exactHerePaths.has(b.path) ? 1 : 0))) ];
}

function delay2() {
  return new Promise((res => setTimeout((() => res()), 5)));
}

function nextTick2() {
  return new Promise((res => setTimeout((() => res()), 0)));
}

var delays = [ nextTick2, delay2, nextTick2, waitForRequestAnimationFrame ], delayIdx = 0;

async function collectChildren(previousTrail, tags, _tagInfo, _items) {
  const previousTrailLC = previousTrail.toLowerCase(), children = [], tagPerItem = new Map, lowercaseMap = new Map;
  for (const item of _items) item.tags.forEach((itemTag => {
    var _a;
    const tagLc = null != (_a = lowercaseMap.get(itemTag)) ? _a : lowercaseMap.set(itemTag, itemTag.toLowerCase()).get(itemTag);
    if (!tagPerItem.has(tagLc)) tagPerItem.set(tagLc, []);
    tagPerItem.get(tagLc).push(item);
  }));
  for (const tag of tags) {
    const tagLC = tag.toLowerCase(), tagNestedLC = trimPrefix(tagLC, previousTrailLC), items = [];
    for (const [itemTag, tempItems] of tagPerItem) if (pathMatch(itemTag, tagLC)) items.push(...tempItems); else if (pathMatch(itemTag, tagNestedLC)) items.push(...tempItems);
    children.push([ tag, ...parseTagName(tag, _tagInfo), [ ...new Set(items) ] ]);
    delayIdx++;
    delayIdx %= 4;
    await delays[delayIdx]();
  }
  return children;
}

async function collectTreeChildren({key, expandLimit, depth, tags, trailLower, _setting, isMainTree, isSuppressibleLevel, viewType, previousTrail, _tagInfo, _items, linkedItems, isRoot, sortFunc}) {
  let suppressLevels = [], children = [];
  if (expandLimit && depth >= expandLimit) {
    children = [];
    suppressLevels = getExtraTags(tags, trailLower, _setting.reduceNestedParent);
  } else if (!isMainTree) children = []; else if (isSuppressibleLevel) {
    children = [];
    suppressLevels = getExtraTags(tags, trailLower, _setting.reduceNestedParent);
  } else {
    let wChildren = [];
    if ("tags" == viewType) wChildren = await collectChildren(previousTrail, tags, _tagInfo, _items); else if ("links" == viewType) wChildren = tags.map((tag => {
      var _a;
      const selfInfo = getViewItemFromPath(tag), dispName = !selfInfo ? tag : selfInfo.displayName;
      return [ tag, dispName, [ dispName ], null != (_a = linkedItems.get(tag)) ? _a : [] ];
    }));
    if ("tags" == viewType) {
      if (_setting.mergeRedundantCombination) {
        const out = [], isShown = new Set;
        for (const [tag, tagName, tagsDisp, items] of wChildren) {
          const list = [];
          for (const v of items) if (!isShown.has(v.path)) {
            list.push(v);
            isShown.add(v.path);
          }
          if (0 != list.length) out.push([ tag, tagName, tagsDisp, list ]);
        }
        wChildren = out;
      }
      if (isMainTree && isRoot) {
        const archiveTags = _setting.archiveTags.toLowerCase().replace(/[\n ]/g, "").split(",");
        wChildren = wChildren.map((e => archiveTags.some((aTag => `${aTag}//`.startsWith(e[V2FI_IDX_TAG].toLowerCase() + "/"))) ? e : [ e[V2FI_IDX_TAG], e[V2FI_IDX_TAGNAME], e[V2FI_IDX_TAGDISP], e[V2FI_IDX_CHILDREN].filter((items => !items.tags.some((e2 => archiveTags.contains(e2.toLowerCase()))))) ])).filter((child2 => 0 != child2[V2FI_IDX_CHILDREN].length));
      }
    }
    wChildren = wChildren.sort(sortFunc);
    children = wChildren;
  }
  return {
    suppressLevels,
    children
  };
}

var root3 = template("<div><!></div>");

function OnDemandRender($$anchor, $$props) {
  push($$props, true);
  let cssClass = prop($$props, "cssClass", 3, ""), isVisible = prop($$props, "isVisible", 15, false), hidingScheduled = state(false);
  const {observe, unobserve} = getContext("observer");
  function setIsVisible(visibility) {
    if (isVisible() != visibility) if (visibility) isVisible(visibility);
    set(hidingScheduled, !visibility);
  }
  onMount((() => {
    performHide.subscribe((() => {
      if (get(hidingScheduled)) {
        isVisible(false);
        set(hidingScheduled, false);
      }
    }));
  }));
  onDestroy((() => {
    if (get(_el)) unobserve(get(_el));
  }));
  let _el = state(void 0), el = state(void 0);
  user_effect((() => {
    if (get(_el) != get(el)) {
      if (get(_el)) unobserve(get(_el));
      set(_el, proxy(get(el)));
      if (get(el)) observe(get(el), setIsVisible);
    }
  }));
  var div = root3();
  bind_this(div, ($$value => set(el, $$value)), (() => get(el)));
  snippet(child(div), (() => {
    var _a;
    return null != (_a = $$props.children) ? _a : noop;
  }), (() => ({
    isVisible: isVisible()
  })));
  reset(div);
  template_effect((() => set_class(div, cssClass())));
  append($$anchor, div);
  pop();
}

var on_click2 = (evt, $$props) => $$props.openFile($$props.item.path, evt.metaKey || evt.ctrlKey), on_mouseover = (e, handleMouseover, $$props) => {
  handleMouseover(e, $$props.item.path);
}, on_contextmenu = (evt, $$props) => $$props.showMenu(evt, $$props.trail, void 0, [ $$props.item ]), root_2 = template('<div class="tf-taglist"><!></div>'), root_12 = template('<div class="tree-item-self is-clickable nav-file-title"><div class="tree-item-inner nav-file-title-content lsl-f"> </div> <!></div>');

function V2TreeItemComponent($$anchor, $$props) {
  push($$props, true);
  const $$stores = setup_stores(), $pluginInstance = () => store_get(pluginInstance, "$pluginInstance", $$stores);
  function handleMouseover(e, path) {
    $$props.hoverPreview(e, path);
  }
  const _setting = derived((() => store_get(tagFolderSetting, "$tagFolderSetting", $$stores))), _currentActiveFilePath = derived((() => store_get(currentFile, "$currentFile", $$stores)));
  let isActive = derived((() => $$props.item.path == get(_currentActiveFilePath))), isItemVisible = state(false);
  const tagsLeft = derived((() => get(isItemVisible) ? uniqueCaseIntensive(getExtraTags($$props.item.tags, [ ...$$props.trail ], get(_setting).reduceNestedParent).map((e => trimSlash(e, false, true))).map((e => e.split("/").map((ee => renderSpecialTag(ee))).join("/"))).filter((e => "" != e))) : [])), extraTagsHtml = derived((() => `${get(tagsLeft).map((e => `<span class="tf-tag">${escapeStringToHTML(e)}</span>`)).join("")}`)), draggable = derived((() => !get(_setting).disableDragging)), app = derived((() => null === $pluginInstance() || void 0 === $pluginInstance() ? void 0 : $pluginInstance().app)), dm = derived((() => null === get(app) || void 0 === get(app) ? void 0 : get(app).dragManager));
  function dragStartFile(args) {
    if (!get(draggable)) return;
    const file = get(app).vault.getAbstractFileByPath($$props.item.path), param = get(dm).dragFile(args, file);
    if (param) return get(dm).onDragStart(args, param);
  }
  {
    const children = ($$anchor2, $$arg0) => {
      let isVisible = () => null == $$arg0 ? void 0 : $$arg0().isVisible;
      var div = root_12();
      div.__click = [ on_click2, $$props ];
      div.__mouseover = [ on_mouseover, handleMouseover, $$props ];
      div.__contextmenu = [ on_contextmenu, $$props ];
      var div_1 = child(div), text2 = child(div_1, true);
      reset(div_1);
      if_block(sibling(div_1, 2), isVisible, ($$anchor3 => {
        var div_2 = root_2();
        html(child(div_2), (() => get(extraTagsHtml)), false, false);
        reset(div_2);
        append($$anchor3, div_2);
      }));
      reset(div);
      template_effect((() => {
        set_attribute(div, "draggable", get(draggable));
        set_attribute(div, "data-path", $$props.item.path);
        toggle_class(div, "is-active", get(isActive));
        set_text(text2, isVisible() ? $$props.item.displayName : "");
      }));
      event("dragstart", div, dragStartFile);
      event("focus", div, (() => {}));
      append($$anchor2, div);
    };
    OnDemandRender($$anchor, {
      cssClass: "tree-item nav-file",
      get isVisible() {
        return get(isItemVisible);
      },
      set isVisible($$value) {
        set(isItemVisible, proxy($$value));
      },
      children,
      $$slots: {
        default: true
      }
    });
  }
  pop();
}

delegate([ "click", "mouseover", "contextmenu" ]);

function handleOpenItem(evt, viewType, $$props, filename) {
  if ("tags" != viewType()) {
    evt.preventDefault();
    evt.stopPropagation();
    $$props.openFile(get(filename), evt.metaKey || evt.ctrlKey);
  }
}

var on_contextmenu2 = (evt, shouldResponsibleFor, $$props, trail, suppressLevels, viewType, tagName, filename, _items) => {
  evt.stopPropagation();
  if (shouldResponsibleFor(evt)) $$props.showMenu(evt, [ ...trail(), ...get(suppressLevels) ], "tags" == viewType() ? tagName() : get(filename), get(_items));
}, root_22 = template('<div class="tree-item-self nav-folder-title"><div class="tree-item-inner nav-folder-title-content"> </div></div>'), root_6 = template('<svg class="svg-icon"></svg>'), root_7 = template('<div class="tagfolder-titletagname"><!></div>'), root_8 = template('<div class="tagfolder-titletagname">...</div>'), on_click3 = (e, handleOpenScroll, trail, _items) => handleOpenScroll(e, trail(), get(_items).map((e2 => e2.path))), root_4 = template('<div class="tree-item-icon collapse-icon nav-folder-collapse-indicator"><!></div> <div class="tree-item-inner nav-folder-title-content lsl-f"><!> <div class="tagfolder-quantity itemscount"><span class="itemscount"> </span></div></div>', 1), root_10 = template("<!> <!>", 1), root_15 = template('<div class="tree-item-children nav-folder-children"><!></div>'), root4 = template("<div><!> <!></div>");

function V2TreeFolderComponent_1($$anchor, $$props) {
  push($$props, true);
  const $$stores = setup_stores();
  var _a, _b, _c;
  let viewType = prop($$props, "viewType", 3, "tags"), thisName = prop($$props, "thisName", 3, ""), items = prop($$props, "items", 19, (() => [])), tagName = prop($$props, "tagName", 11, ""), tagNameDisp = prop($$props, "tagNameDisp", 27, (() => proxy([]))), trail = prop($$props, "trail", 19, (() => [])), depth = prop($$props, "depth", 3, 1), folderIcon = prop($$props, "folderIcon", 3, ""), headerTitle = prop($$props, "headerTitle", 3, ""), _setting = derived((() => store_get(tagFolderSetting, "$tagFolderSetting", $$stores)));
  const expandLimit = derived((() => !get(_setting).expandLimit ? 0 : "links" == viewType() ? get(_setting).expandLimit + 1 : get(_setting).expandLimit)), _tagInfo = derived((() => store_get(tagInfo, "$tagInfo", $$stores))), _currentActiveFilePath = derived((() => store_get(currentFile, "$currentFile", $$stores)));
  function handleOpenScroll(e, trails, filePaths) {
    if ("tags" == viewType()) $$props.openScrollView(void 0, "", joinPartialPath(removeIntermediatePath(trails)).join(", "), filePaths); else if ("links" == viewType()) $$props.openScrollView(void 0, "", `Linked to ${get(filename)}`, filePaths);
    e.preventDefault();
  }
  function shouldResponsibleFor(evt) {
    if (evt.target instanceof Element && evt.target.matchParent(".is-clickable.mod-collapsible.nav-folder-title")) return true; else return false;
  }
  function toggleFolder(evt) {
    evt.stopPropagation();
    if (shouldResponsibleFor(evt)) {
      evt.preventDefault();
      if (get(_setting).useMultiPaneList) selectedTags.set(trail());
      v2expandedTags.update((evt2 => {
        if (evt2.has(get(trailKey))) evt2.delete(get(trailKey)); else evt2.add(get(trailKey));
        return evt2;
      }));
    }
  }
  let _lastParam, suppressLevels = state(proxy([])), children = state(proxy([])), isUpdating = state(false);
  const viewContextID = `${null !== (_a = getContext("viewID")) && void 0 !== _a ? _a : ""}`;
  let isFolderVisible = state(false);
  function splitArrayToBatch(items2) {
    const ret = [];
    if (items2 && items2.length > 0) {
      const applyItems = [ ...items2 ];
      do {
        const batch = applyItems.splice(0, 80);
        if (0 == batch.length) break;
        ret.push(batch);
        if (batch.length < 80) break;
      } while (applyItems.length > 0);
    }
    return ret;
  }
  function dragStartFiles(args) {
    if (!get(draggable)) return;
    const files = get(_items).map((e => get(app).vault.getAbstractFileByPath(e.path))), param = get(dm).dragFiles(args, files);
    if (param) return get(dm).onDragStart(args, param);
  }
  function dragStartName(args) {
    if (!get(draggable)) return;
    if ("links" == viewType()) return function dragStartFile(args) {
      if (!get(draggable)) return;
      const file = get(app).vault.getAbstractFileByPath(get(filename)), param = get(dm).dragFile(args, file);
      if (param) return get(dm).onDragStart(args, param); else return;
    }(args);
    const expandedTags = [ ...ancestorToLongestTag(ancestorToTags(joinPartialPath(removeIntermediatePath([ ...trail(), ...get(suppressLevels) ])))) ].map((e => trimTrailingSlash(e))).map((e => e.split("/").filter((ee => !isSpecialTag(ee))).join("/"))).filter((e => "" != e)).map((e => "#" + e)).join(" ").trim();
    args.dataTransfer.setData("text/plain", expandedTags);
    args.dataTransfer.setData("Text", expandedTags);
    args.title = expandedTags;
    args.draggable = true;
    get(dm).onDragStart(args, args);
  }
  const filename = derived((() => "tags" == viewType() ? "" : thisName().substring(thisName().indexOf(":") + 1))), thisInfo = derived((() => "links" != viewType() ? void 0 : getViewItemFromPath(thisName()))), thisLinks = derived((() => "links" != viewType() ? [] : (null !== (_b = null === get(thisInfo) || void 0 === get(thisInfo) ? void 0 : get(thisInfo).links) && void 0 !== _b ? _b : []).map((e => `${e}`)))), thisNameLC = derived((() => thisName().toLowerCase())), tagNameLC = derived((() => tagName().toLowerCase())), trailKey = derived((() => trail().join("*"))), trailLower = derived((() => trail().map((e => e.toLowerCase())))), collapsed = derived((() => !$$props.isRoot && !store_get(v2expandedTags, "$v2expandedTags", $$stores).has(get(trailKey)))), inMiddleOfTagHierarchy = derived((() => trail().length >= 1 && trail()[trail().length - 1].endsWith("/"))), previousTrail = derived((() => get(inMiddleOfTagHierarchy) ? trail()[trail().length - 1] : "")), lastTrailTagLC = derived((() => trimTrailingSlash(get(previousTrail)).toLowerCase())), _items = derived(items), tagsAllCI = derived((() => uniqueCaseIntensive(get(_items).flatMap((e => e.tags))))), tagsAllLower = derived((() => get(tagsAllCI).map((e => e.toLowerCase())))), isInDedicatedTag = derived((() => get(inMiddleOfTagHierarchy) && !get(tagsAllLower).contains(get(lastTrailTagLC)))), isMixedDedicatedTag = derived((() => get(inMiddleOfTagHierarchy))), displayTagCandidates = derived((() => {
    let tagsAll = [];
    if ("links" == viewType()) {
      if (!$$props.isRoot) if (!get(_setting).linkShowOnlyFDR) tagsAll = get(thisInfo) ? getAllLinksRecursive(get(thisInfo), [ ...trail() ]) : [ ...get(thisLinks) ]; else tagsAll = [ ...get(thisLinks) ]; else tagsAll = unique(get(_items).flatMap((e => e.links)));
      if (!$$props.isRoot || get(_setting).expandUntaggedToRoot) tagsAll = tagsAll.filter((e => "_unlinked" != e));
      tagsAll = tagsAll.filter((e => !trail().contains(e)));
    } else {
      tagsAll = uniqueCaseIntensive(get(_items).flatMap((e => e.tags)));
      if (!$$props.isRoot || get(_setting).expandUntaggedToRoot) tagsAll = tagsAll.filter((e => "_untagged" != e));
    }
    return tagsAll;
  })), tagsExceptAlreadyShown = derived((() => "tags" != viewType() ? [] : get(displayTagCandidates).filter((tag => trail().every((trail2 => trimTrailingSlash(tag.toLowerCase()) !== trimTrailingSlash(trail2.toLowerCase()))))))), passedTagWithoutThis = derived((() => {
    const trimSlashedThisNameLC = "/" + trimSlash(thisName()).toLowerCase();
    return get(tagsExceptAlreadyShown).filter((tag => {
      const lc = tag.toLowerCase();
      return lc != get(thisNameLC) && lc != get(tagNameLC);
    })).filter((tag => !tag.toLowerCase().endsWith(trimSlashedThisNameLC)));
  })), escapedPreviousTrail = derived((() => !get(isMixedDedicatedTag) ? get(previousTrail) : get(previousTrail).split("/").join("*"))), sparseIntermediateTags = derived((() => {
    const t1 = !get(isInDedicatedTag) ? get(passedTagWithoutThis) : get(passedTagWithoutThis).filter((e => (e + "/").startsWith(get(previousTrail))));
    if (!get(isInDedicatedTag)) return t1; else return t1.map((e => (e + "/").startsWith(get(previousTrail)) ? get(escapedPreviousTrail) + e.substring(get(previousTrail).length) : e));
  })), tagsPhaseX1 = derived((() => get(sparseIntermediateTags))), $$d = derived((() => {
    let isSuppressibleLevel2 = false, existTags = get(tagsPhaseX1), existTagsFiltered1 = [];
    if (!get(_setting).doNotSimplifyTags && "links" != viewType()) if (1 == get(_items).length) {
      existTagsFiltered1 = existTags;
      isSuppressibleLevel2 = true;
    } else if (1 == uniqueCaseIntensive(get(_items).map((e => [ ...e.tags ].sort().join("**")))).length) {
      isSuppressibleLevel2 = true;
      existTagsFiltered1 = existTags;
    }
    if (!isSuppressibleLevel2) {
      const removeItems = [ get(thisNameLC) ];
      if (get(_setting).reduceNestedParent) removeItems.push(...get(trailLower));
      let tagsOnNextLevel = [];
      if ("tags" == viewType()) tagsOnNextLevel = uniqueCaseIntensive(existTags.map((e => {
        const idx = e.indexOf("/");
        if (idx < 1) return e;
        let piece = e.substring(0, idx + 1), idx2 = idx;
        for (;removeItems.some((e2 => e2.startsWith(piece.toLowerCase()))); ) {
          idx2 = e.indexOf("/", idx2 + 1);
          if (-1 === idx2) {
            piece = e;
            break;
          }
          piece = e.substring(0, idx2 + 1);
        }
        return piece;
      }))); else tagsOnNextLevel = unique(existTags);
      const trailShortest = removeIntermediatePath(trail());
      existTagsFiltered1 = tagsOnNextLevel.filter((tag => trailShortest.every((trail2 => trimTrailingSlash(tag.toLowerCase()) !== trimTrailingSlash(trail2.toLowerCase())))));
    }
    if (get(isMixedDedicatedTag) || get(isInDedicatedTag)) existTagsFiltered1 = existTagsFiltered1.map((e => e.replace(get(escapedPreviousTrail), get(previousTrail))));
    if (get(isMixedDedicatedTag) || get(isInDedicatedTag)) existTagsFiltered1 = existTagsFiltered1.map((e => e.replace(get(escapedPreviousTrail), get(previousTrail))));
    const existTagsFiltered1LC = existTagsFiltered1.map((e => e.toLowerCase()));
    return {
      filteredTags: uniqueCaseIntensive(existTagsFiltered1.map((e => existTagsFiltered1LC.contains(e.toLowerCase() + "/") ? e + "/" : e))),
      isSuppressibleLevel: isSuppressibleLevel2
    };
  })), filteredTags = derived((() => get($$d).filteredTags)), isSuppressibleLevel = derived((() => get($$d).isSuppressibleLevel)), $$d_1 = derived((() => {
    let tags2 = [];
    const leftOverItemsSrc2 = [];
    if (!get(_items)) return {
      tags: tags2,
      leftOverItemsSrc: leftOverItemsSrc2
    };
    if (!($$props.isMainTree && (!get(expandLimit) || get(expandLimit) && depth() < get(expandLimit)))) return {
      tags: tags2,
      leftOverItemsSrc: leftOverItemsSrc2
    };
    if ("links" == viewType()) {
      const ret = get(tagsOfLinkedItems);
      return {
        tags: ret.tags,
        leftOverItemsSrc: ret.leftOverItems
      };
    }
    if (get(previousTrail).endsWith("/")) {
      const existTagsFiltered4 = [];
      for (const tag of get(filteredTags)) if (!get(filteredTags).map((e => e.toLowerCase())).contains((get(previousTrail) + tag).toLowerCase())) existTagsFiltered4.push(tag);
      tags2 = uniqueCaseIntensive(removeIntermediatePath(existTagsFiltered4));
    } else tags2 = uniqueCaseIntensive(removeIntermediatePath(get(filteredTags)));
    return {
      tags: tags2,
      leftOverItemsSrc: leftOverItemsSrc2
    };
  })), tags = derived((() => get($$d_1).tags)), leftOverItemsSrc = derived((() => get($$d_1).leftOverItemsSrc)), linkedItems = derived((() => {
    const ret = new Map;
    if ("tags" == viewType()) return ret;
    for (const tag of get(displayTagCandidates)) if ("_unlinked" == tag) ret.set(tag, get(_items).filter((e => e.links.contains(tag)))); else {
      const wItems = get(_items).filter((e => e.path == tag));
      ret.set(tag, wItems);
    }
    return ret;
  })), tagsOfLinkedItems = derived((() => {
    let leftOverItems2 = [], tags2 = [];
    if ("tags" == viewType()) return {
      tags: tags2,
      leftOverItems: leftOverItems2
    };
    if ("_unlinked" == thisName()) leftOverItems2 = get(_items); else get(displayTagCandidates).forEach((tag => {
      if ("_unlinked" == tag) {
        tags2.push(tag);
        return;
      }
      const x = getViewItemFromPath(tag);
      if (null == x) return false;
      const existLinks = x.links.filter((e => !trail().contains(e) && e != thisName())), nextDepth = !get(expandLimit) || get(expandLimit) && depth() + 1 < get(expandLimit);
      if (existLinks.length >= 2 && nextDepth) tags2.push(tag); else leftOverItems2.push(x);
    }));
    return {
      tags: tags2,
      leftOverItems: leftOverItems2
    };
  })), leftOverItemsUnsorted = derived((() => {
    if (get(_setting).useMultiPaneList && $$props.isMainTree) return [];
    if ($$props.isRoot && $$props.isMainTree && !get(isSuppressibleLevel)) if (get(_setting).expandUntaggedToRoot) return get(_items).filter((e => e.tags.contains("_untagged") || e.tags.contains("_unlinked"))); else return [];
    if ($$props.isRoot && !$$props.isMainTree) return get(_items);
    if ("tags" == viewType()) if ("NONE" == get(_setting).hideItems) return get(_items); else if ("DEDICATED_INTERMIDIATES" == get(_setting).hideItems && get(isInDedicatedTag) || "ALL_EXCEPT_BOTTOM" == get(_setting).hideItems) return get(_items).filter((e => !get(children).map((e2 => e2[V2FI_IDX_CHILDREN])).flat().find((ee => e.path == ee.path)))); else return get(_items); else return get(leftOverItemsSrc);
  })), leftOverItems = derived((() => get(_setting).sortExactFirst ? performSortExactFirst(get(_items), get(children), get(leftOverItemsUnsorted)) : get(leftOverItemsUnsorted)));
  let isActive = derived((() => get(_items) && get(_items).some((e => e.path == get(_currentActiveFilePath))) || "links" == viewType() && (thisName() == get(_currentActiveFilePath) || get(tags).contains(get(_currentActiveFilePath)) || get(leftOverItems).some((e => e.path == get(_currentActiveFilePath))))));
  const tagsDisp = derived((() => get(isSuppressibleLevel) && get(isInDedicatedTag) ? [ [ ...tagNameDisp(), ...get(suppressLevels).flatMap((e => e.split("/").map((e2 => renderSpecialTag(e2))))) ] ] : get(isSuppressibleLevel) ? [ tagNameDisp(), ...get(suppressLevels).map((e => e.split("/").map((e2 => renderSpecialTag(e2))))) ] : [ tagNameDisp() ])), classKey = derived((() => "links" == viewType() ? " tf-link" : " tf-tag")), tagsDispHtml = derived((() => get(isFolderVisible) ? get(tagsDisp).map((e => `<span class="tagfolder-tag tag-tag${get(classKey)}">${e.map((ee => `<span class="tf-tag-each">${escapeStringToHTML(ee)}</span>`)).join("")}</span>`)).join("") : "")), itemCount = derived((() => "tags" == viewType() ? null !== (_c = null === get(_items) || void 0 === get(_items) ? void 0 : get(_items).length) && void 0 !== _c ? _c : 0 : get(tags).length + get(leftOverItems).length)), leftOverItemsDisp = derived((() => splitArrayToBatch(get(leftOverItems)))), childrenDisp = derived((() => splitArrayToBatch(get(children)))), draggable = derived((() => !get(_setting).disableDragging)), app = derived((() => store_get(pluginInstance, "$pluginInstance", $$stores).app)), dm = derived((() => null === get(app) || void 0 === get(app) ? void 0 : get(app).dragManager));
  user_effect((() => {
    const key = get(trailKey) + ($$props.isRoot ? "-r" : "-x") + viewContextID, sortFunc = selectCompareMethodTags(get(_setting), "links" == viewType() ? {} : get(_tagInfo));
    (function updateX(param) {
      if (!isSameAny(param, _lastParam)) {
        _lastParam = {
          ...param
        };
        if (param.isFolderVisible || $$props.isRoot) scheduleOnceIfDuplicated("update-children-" + param.key, (async () => {
          set(isUpdating, true);
          const ret = await collectTreeChildren(param);
          set(children, proxy(ret.children));
          set(suppressLevels, proxy(ret.suppressLevels));
          set(isUpdating, false);
        }));
      }
    })({
      key,
      expandLimit: get(expandLimit),
      depth: depth(),
      tags: get(tags),
      trailLower: get(trailLower),
      _setting: get(_setting),
      isMainTree: $$props.isMainTree,
      isSuppressibleLevel: get(isSuppressibleLevel),
      viewType: viewType(),
      previousTrail: get(previousTrail),
      _tagInfo: get(_tagInfo),
      _items: get(_items),
      linkedItems: get(linkedItems),
      isRoot: $$props.isRoot,
      isFolderVisible: get(isFolderVisible),
      sortFunc
    });
  }));
  var div = root4();
  div.__click = toggleFolder;
  div.__contextmenu = [ on_contextmenu2, shouldResponsibleFor, $$props, trail, suppressLevels, viewType, tagName, filename, _items ];
  var node = child(div);
  if_block(node, (() => $$props.isRoot || !$$props.isMainTree), ($$anchor2 => {
    var fragment = comment();
    if_block(first_child(fragment), (() => $$props.isRoot), ($$anchor3 => {
      var div_1 = root_22(), div_2 = child(div_1), text2 = child(div_2, true);
      reset(div_2);
      reset(div_1);
      template_effect((() => set_text(text2, headerTitle())));
      append($$anchor3, div_1);
    }));
    append($$anchor2, fragment);
  }), ($$anchor2 => {
    var cssClass = derived((() => `tree-item-self${!$$props.isRoot ? " is-clickable mod-collapsible" : ""} nav-folder-title tag-folder-title${get(isActive) ? " is-active" : ""}`));
    OnDemandRender($$anchor2, {
      get cssClass() {
        return get(cssClass);
      },
      get isVisible() {
        return get(isFolderVisible);
      },
      set isVisible($$value) {
        set(isFolderVisible, proxy($$value));
      },
      children: ($$anchor3, $$slotProps) => {
        var fragment_2 = root_4(), div_3 = first_child(fragment_2);
        div_3.__click = toggleFolder;
        if_block(child(div_3), (() => get(isFolderVisible)), ($$anchor4 => {
          var fragment_3 = comment();
          html(first_child(fragment_3), folderIcon, false, false);
          append($$anchor4, fragment_3);
        }), ($$anchor4 => {
          append($$anchor4, root_6());
        }));
        reset(div_3);
        var div_4 = sibling(div_3, 2);
        div_4.__click = [ handleOpenItem, viewType, $$props, filename ];
        var node_4 = child(div_4);
        if_block(node_4, (() => get(isFolderVisible)), ($$anchor4 => {
          var div_5 = root_7();
          html(child(div_5), (() => get(tagsDispHtml)), false, false);
          reset(div_5);
          template_effect((() => set_attribute(div_5, "draggable", get(draggable))));
          event("dragstart", div_5, dragStartName);
          append($$anchor4, div_5);
        }), ($$anchor4 => {
          append($$anchor4, root_8());
        }));
        var div_7 = sibling(node_4, 2);
        div_7.__click = [ on_click3, handleOpenScroll, trail, _items ];
        var span = child(div_7), text_1 = child(span, true);
        reset(span);
        reset(div_7);
        reset(div_4);
        template_effect((() => {
          toggle_class(div_3, "is-collapsed", get(collapsed));
          set_attribute(span, "draggable", get(draggable));
          set_text(text_1, get(itemCount));
        }));
        event("dragstart", span, dragStartFiles);
        append($$anchor3, fragment_2);
      },
      $$slots: {
        default: true
      }
    });
  }));
  if_block(sibling(node, 2), (() => !get(collapsed)), ($$anchor2 => {
    var fragment_9 = comment();
    const treeContent = ($$anchor3, childrenDisp2 = noop, leftOverItemsDisp2 = noop) => {
      var fragment_4 = root_10(), node_7 = first_child(fragment_4);
      each(node_7, 17, childrenDisp2, index, (($$anchor4, items2) => {
        var fragment_5 = comment();
        each(first_child(fragment_5), 17, (() => get(items2)), index, (($$anchor5, $$item) => {
          let f = () => get($$item)[0];
          var trail_1 = derived((() => [ ...trail(), ...get(suppressLevels), f() ])), depth_1 = derived((() => get(isInDedicatedTag) ? depth() : depth() + 1));
          V2TreeFolderComponent_1($$anchor5, {
            get viewType() {
              return viewType();
            },
            get items() {
              return get($$item)[3];
            },
            get thisName() {
              return f();
            },
            get trail() {
              return get(trail_1);
            },
            get folderIcon() {
              return folderIcon();
            },
            get openFile() {
              return $$props.openFile;
            },
            isRoot: false,
            get showMenu() {
              return $$props.showMenu;
            },
            get isMainTree() {
              return $$props.isMainTree;
            },
            get openScrollView() {
              return $$props.openScrollView;
            },
            get hoverPreview() {
              return $$props.hoverPreview;
            },
            get tagName() {
              return get($$item)[1];
            },
            get tagNameDisp() {
              return get($$item)[2];
            },
            get depth() {
              return get(depth_1);
            }
          });
        }));
        append($$anchor4, fragment_5);
      }));
      each(sibling(node_7, 2), 17, leftOverItemsDisp2, index, (($$anchor4, items2) => {
        var fragment_7 = comment();
        each(first_child(fragment_7), 17, (() => get(items2)), index, (($$anchor5, item) => {
          var trail_2 = derived((() => $$props.isRoot ? [ ...trail() ] : [ ...trail(), ...get(suppressLevels) ]));
          V2TreeItemComponent($$anchor5, {
            get item() {
              return get(item);
            },
            get openFile() {
              return $$props.openFile;
            },
            get trail() {
              return get(trail_2);
            },
            get showMenu() {
              return $$props.showMenu;
            },
            get hoverPreview() {
              return $$props.hoverPreview;
            }
          });
        }));
        append($$anchor4, fragment_7);
      }));
      append($$anchor3, fragment_4);
    };
    if_block(first_child(fragment_9), (() => !$$props.isRoot), ($$anchor3 => {
      var div_8 = root_15(), node_12 = child(div_8);
      treeContent(node_12, (() => get(childrenDisp)), (() => get(leftOverItemsDisp)));
      reset(div_8);
      append($$anchor3, div_8);
    }), ($$anchor3 => {
      treeContent($$anchor3, (() => get(childrenDisp)), (() => get(leftOverItemsDisp)));
    }));
    append($$anchor2, fragment_9);
  }));
  reset(div);
  template_effect((() => set_class(div, `tree-item nav-folder${get(collapsed) ? " is-collapsed" : ""}${$$props.isRoot ? " mod-root" : ""}${get(isUpdating) ? " updating" : ""}`)));
  append($$anchor, div);
  pop();
}

delegate([ "click", "contextmenu" ]);

var import_obsidian3 = require("obsidian");

function toggleSearch(_, showSearch, $searchString) {
  set(showSearch, !get(showSearch));
  if (!get(showSearch)) store_set(searchString, "");
}

function clearSearch(__1, $searchString) {
  store_set(searchString, "");
}

function doSwitch(__2, $$props) {
  if ($$props.switchView) $$props.switchView();
}

var root_13 = template('<div class="clickable-icon nav-action-button" aria-label="Change sort order"><!></div>  <div class="clickable-icon nav-action-button" aria-label="Expand limit"><!></div>  <div aria-label="Search"><!></div>', 1), root_23 = template('<div class="clickable-icon nav-action-button" aria-label="Switch List/Tree"><!></div>'), root_3 = template('<div class="clickable-icon nav-action-button" aria-label="Toggle Incoming"><!></div>  <div class="clickable-icon nav-action-button" aria-label="Toggle Outgoing"><!></div>  <div class="clickable-icon nav-action-button" aria-label="Toggle Incoming&amp;Outgoing"><!></div>  <div class="clickable-icon nav-action-button" aria-label="Toggle Hide indirect notes"><!></div>', 1), root_42 = template('<div class="search-row"><div class="search-input-container global-search-input-container"><input type="search" spellcheck="false" placeholder="Type to start search...">  <div class="search-input-clear-button" aria-label="Clear search"></div></div></div>'), root5 = template('<div hidden></div> <div class="nav-header"><div class="nav-buttons-container tagfolder-buttons-container"><div class="clickable-icon nav-action-button" aria-label="New note"><!></div> <!> <!> <!></div></div> <!> <div class="nav-files-container node-insert-event svelte-1xm87ro"><!></div>', 1), $$css3 = {
  hash: "svelte-1xm87ro",
  code: "\n\t.nav-files-container.svelte-1xm87ro {\n\t\theight: 100%;\n\t}\n"
};

function TagFolderViewComponent($$anchor, $$props) {
  push($$props, true);
  append_styles($$anchor, $$css3);
  const $$stores = setup_stores(), $searchString = () => store_get(searchString, "$searchString", $$stores);
  let vaultName = prop($$props, "vaultName", 3, ""), title = prop($$props, "title", 15, ""), tags = prop($$props, "tags", 31, (() => proxy([]))), viewType = prop($$props, "viewType", 3, "tags");
  const isMainTree = derived((() => 0 == tags().length));
  null === $$props.stateStore || void 0 === $$props.stateStore || $$props.stateStore.subscribe((state2 => {
    tags(state2.tags);
    title(state2.title);
  }));
  let updatedFiles = state(proxy([]));
  appliedFiles.subscribe((async filenames => {
    set(updatedFiles, proxy(null != filenames ? filenames : []));
  }));
  const viewItemsSrc = derived((() => {
    if ("tags" == viewType()) return store_get(allViewItems, "$allViewItems", $$stores); else return store_get(allViewItemsByLink, "$allViewItemsByLink", $$stores);
  }));
  let _setting = state(proxy(store_get(tagFolderSetting, "$tagFolderSetting", $$stores))), outgoingEnabled = state(false), incomingEnabled = state(false), bothEnabled = state(false), onlyFDREnabled = state(false);
  tagFolderSetting.subscribe((setting => {
    var _a, _b, _c, _d, _e, _f;
    set(_setting, proxy(setting));
    const incoming = null !== (_c = null === (_b = null === (_a = get(_setting).linkConfig) || void 0 === _a ? void 0 : _a.incoming) || void 0 === _b ? void 0 : _b.enabled) && void 0 !== _c ? _c : false, outgoing = null !== (_f = null === (_e = null === (_d = get(_setting).linkConfig) || void 0 === _d ? void 0 : _d.outgoing) || void 0 === _e ? void 0 : _e.enabled) && void 0 !== _f ? _f : false;
    if (!incoming && !outgoing) {
      let newSet = {
        ...get(_setting)
      };
      newSet.linkConfig.incoming.enabled = true;
      newSet.linkConfig.outgoing.enabled = true;
      if ($$props.saveSettings) $$props.saveSettings(newSet);
      set(bothEnabled, true);
    } else {
      set(outgoingEnabled, proxy(!incoming && outgoing));
      set(incomingEnabled, proxy(incoming && !outgoing));
      set(bothEnabled, proxy(incoming && outgoing));
    }
    set(onlyFDREnabled, proxy(get(_setting).linkShowOnlyFDR));
  }));
  let observer, showSearch = state(false), iconDivEl = state(void 0), newNoteIcon = state(""), folderIcon = state(""), upAndDownArrowsIcon = state(""), stackedLevels = state(""), searchIcon = state(""), switchIcon = state(""), outgoingIcon = state(""), incomingIcon = state(""), bothIcon = state(""), linkIcon = state("");
  async function switchIncoming() {
    let newSet = {
      ...get(_setting)
    };
    newSet.linkConfig.incoming.enabled = true;
    newSet.linkConfig.outgoing.enabled = false;
    if ($$props.saveSettings) await $$props.saveSettings(newSet);
  }
  async function switchOutgoing() {
    let newSet = {
      ...get(_setting)
    };
    newSet.linkConfig.incoming.enabled = false;
    newSet.linkConfig.outgoing.enabled = true;
    if ($$props.saveSettings) await $$props.saveSettings(newSet);
  }
  async function switchBoth() {
    let newSet = {
      ...get(_setting)
    };
    newSet.linkConfig.incoming.enabled = true;
    newSet.linkConfig.outgoing.enabled = true;
    if ($$props.saveSettings) await $$props.saveSettings(newSet);
  }
  async function switchOnlyFDR() {
    let newSet = {
      ...get(_setting)
    };
    newSet.linkShowOnlyFDR = !get(_setting).linkShowOnlyFDR;
    if ($$props.saveSettings) await $$props.saveSettings(newSet);
  }
  let scrollParent, observingElements = new Map, observingElQueue = [];
  function unobserve(el) {
    null == observer || observer.unobserve(el);
  }
  function observeAllQueued() {
    observingElQueue.forEach((el => {
      null == observer || observer.observe(el);
    }));
    observingElQueue = [];
  }
  setContext("observer", {
    observe: function observe(el, callback) {
      if (!observer) observingElQueue.push(el); else if (observingElQueue.length > 0) observeAllQueued();
      if (observingElements.has(el)) {
        unobserve(el);
        observingElements.delete(el);
      }
      observingElements.set(el, {
        callback,
        lastState: void 0
      });
      null == observer || observer.observe(el);
    },
    unobserve
  });
  onMount((() => {
    observer = new IntersectionObserver((ex => {
      for (const v of ex) if (observingElements.has(v.target)) {
        const tg = observingElements.get(v.target);
        if (tg && tg.lastState !== v.isIntersecting) {
          tg.lastState = v.isIntersecting;
          setTimeout((() => tg.callback(v.isIntersecting)), 10);
        }
      }
    }), {
      root: scrollParent,
      rootMargin: "40px 0px",
      threshold: 0
    });
    observeAllQueued();
    if (get(iconDivEl)) {
      (0, import_obsidian3.setIcon)(get(iconDivEl), "right-triangle");
      set(folderIcon, `${get(iconDivEl).innerHTML}`);
      (0, import_obsidian3.setIcon)(get(iconDivEl), "lucide-edit");
      set(newNoteIcon, `${get(iconDivEl).innerHTML}`);
      if (get(isMainTree)) {
        (0, import_obsidian3.setIcon)(get(iconDivEl), "lucide-sort-asc");
        set(upAndDownArrowsIcon, proxy(get(iconDivEl).innerHTML));
        (0, import_obsidian3.setIcon)(get(iconDivEl), "stacked-levels");
        set(stackedLevels, proxy(get(iconDivEl).innerHTML));
        (0, import_obsidian3.setIcon)(get(iconDivEl), "search");
        set(searchIcon, proxy(get(iconDivEl).innerHTML));
      }
      if ("links" == viewType()) {
        (0, import_obsidian3.setIcon)(get(iconDivEl), "links-coming-in");
        set(incomingIcon, proxy(get(iconDivEl).innerHTML));
        (0, import_obsidian3.setIcon)(get(iconDivEl), "links-going-out");
        set(outgoingIcon, proxy(get(iconDivEl).innerHTML));
        (0, import_obsidian3.setIcon)(get(iconDivEl), "link");
        set(linkIcon, proxy(get(iconDivEl).innerHTML));
        (0, import_obsidian3.setIcon)(get(iconDivEl), "lucide-link-2");
        set(bothIcon, proxy(get(iconDivEl).innerHTML));
      }
      (0, import_obsidian3.setIcon)(get(iconDivEl), "lucide-arrow-left-right");
      set(switchIcon, proxy(get(iconDivEl).innerHTML));
    }
    const int = setInterval((() => {
      performHide.set(Date.now());
    }), 5e3);
    return () => {
      clearInterval(int);
    };
  }));
  onDestroy((() => {
    null == observer || observer.disconnect();
  }));
  let headerTitle = derived((() => "" == title() ? `${"tags" == viewType() ? "Tags" : "Links"}: ${vaultName()}` : `Items: ${title()}`));
  const viewItems = derived((() => {
    var _a;
    if (!get(viewItemsSrc)) return [];
    if (get(isMainTree)) return get(viewItemsSrc);
    let items = get(viewItemsSrc);
    const lowerTags = tags().map((e => e.toLowerCase()));
    for (const tag of lowerTags) items = items.filter((e => e.tags.some((e2 => (e2.toLowerCase() + "/").startsWith(tag)))));
    const firstLevel = trimTrailingSlash(null !== (_a = tags().first()) && void 0 !== _a ? _a : "").toLowerCase(), archiveTags = get(_setting).archiveTags.toLowerCase().replace(/[\n ]/g, "").split(",");
    if (!archiveTags.contains(firstLevel)) items = items.filter((item => !item.tags.some((e => archiveTags.contains(e.toLowerCase())))));
    return items;
  }));
  setContext("viewID", `${Math.random()}`);
  var fragment = root5(), div = first_child(fragment);
  bind_this(div, ($$value => set(iconDivEl, $$value)), (() => get(iconDivEl)));
  var div_1 = sibling(div, 2), div_2 = child(div_1), div_3 = child(div_2);
  div_3.__click = function(...$$args) {
    var _a;
    null == (_a = $$props.newNote) || _a.apply(this, $$args);
  };
  html(child(div_3), (() => get(newNoteIcon)), false, false);
  reset(div_3);
  var node_1 = sibling(div_3, 2);
  if_block(node_1, (() => get(isMainTree)), ($$anchor2 => {
    var fragment_1 = root_13(), div_4 = first_child(fragment_1);
    div_4.__click = function(...$$args) {
      var _a;
      null == (_a = $$props.showOrder) || _a.apply(this, $$args);
    };
    html(child(div_4), (() => get(upAndDownArrowsIcon)), false, false);
    reset(div_4);
    var div_5 = sibling(div_4, 2);
    div_5.__click = function(...$$args) {
      var _a;
      null == (_a = $$props.showLevelSelect) || _a.apply(this, $$args);
    };
    html(child(div_5), (() => get(stackedLevels)), false, false);
    reset(div_5);
    var div_6 = sibling(div_5, 2);
    div_6.__click = [ toggleSearch, showSearch, $searchString ];
    html(child(div_6), (() => get(searchIcon)), false, false);
    reset(div_6);
    template_effect((() => {
      var _a;
      return set_class(div_6, `${null != (_a = "clickable-icon nav-action-button " + (get(showSearch) ? " is-active" : "")) ? _a : ""} svelte-1xm87ro`);
    }));
    append($$anchor2, fragment_1);
  }));
  var node_5 = sibling(node_1, 2);
  if_block(node_5, (() => $$props.isViewSwitchable), ($$anchor2 => {
    var div_7 = root_23();
    div_7.__click = [ doSwitch, $$props ];
    html(child(div_7), (() => get(switchIcon)), false, false);
    reset(div_7);
    append($$anchor2, div_7);
  }));
  if_block(sibling(node_5, 2), (() => "links" == viewType()), ($$anchor2 => {
    var fragment_2 = root_3(), div_8 = first_child(fragment_2);
    div_8.__click = switchIncoming;
    html(child(div_8), (() => get(incomingIcon)), false, false);
    reset(div_8);
    var div_9 = sibling(div_8, 2);
    div_9.__click = switchOutgoing;
    html(child(div_9), (() => get(outgoingIcon)), false, false);
    reset(div_9);
    var div_10 = sibling(div_9, 2);
    div_10.__click = switchBoth;
    html(child(div_10), (() => get(bothIcon)), false, false);
    reset(div_10);
    var div_11 = sibling(div_10, 2);
    div_11.__click = switchOnlyFDR;
    html(child(div_11), (() => get(linkIcon)), false, false);
    reset(div_11);
    template_effect((() => {
      toggle_class(div_8, "is-active", get(incomingEnabled));
      toggle_class(div_9, "is-active", get(outgoingEnabled));
      toggle_class(div_10, "is-active", get(bothEnabled));
      toggle_class(div_11, "is-active", get(onlyFDREnabled));
    }));
    append($$anchor2, fragment_2);
  }));
  reset(div_2);
  reset(div_1);
  var node_12 = sibling(div_1, 2);
  if_block(node_12, (() => get(showSearch) && get(isMainTree)), ($$anchor2 => {
    var div_12 = root_42(), div_13 = child(div_12), input = child(div_13);
    remove_input_defaults(input);
    var div_14 = sibling(input, 2);
    const style_derived = derived((() => {
      var _a;
      return `display:${null != (_a = "" == $searchString().trim() ? "none" : "") ? _a : ""};`;
    }));
    div_14.__click = [ clearSearch, $searchString ];
    reset(div_13);
    reset(div_12);
    template_effect((() => set_attribute(div_14, "style", get(style_derived))));
    bind_value(input, $searchString, ($$value => store_set(searchString, $$value)));
    append($$anchor2, div_12);
  }));
  var div_15 = sibling(node_12, 2);
  bind_this(div_15, ($$value => scrollParent = $$value), (() => scrollParent));
  V2TreeFolderComponent_1(child(div_15), {
    get viewType() {
      return viewType();
    },
    get items() {
      return get(viewItems);
    },
    get folderIcon() {
      return get(folderIcon);
    },
    thisName: "",
    isRoot: true,
    get showMenu() {
      return $$props.showMenu;
    },
    get openFile() {
      return $$props.openFile;
    },
    get isMainTree() {
      return get(isMainTree);
    },
    get hoverPreview() {
      return $$props.hoverPreview;
    },
    get openScrollView() {
      return $$props.openScrollView;
    },
    depth: 1,
    get headerTitle() {
      return get(headerTitle);
    }
  });
  reset(div_15);
  append($$anchor, fragment);
  pop();
}

delegate([ "click" ]);

var import_obsidian5 = require("obsidian"), import_obsidian4 = require("obsidian"), askString = (app, title, placeholder, initialText) => new Promise((res => {
  new PopoverSelectString(app, title, placeholder, initialText, (result => res(result))).open();
})), PopoverSelectString = class extends import_obsidian4.SuggestModal {
  constructor(app, title, placeholder, initialText, callback) {
    super(app);
    this.callback = () => {};
    this.title = "";
    this.app = app;
    this.title = title;
    this.setPlaceholder(null != placeholder ? placeholder : ">");
    this.callback = callback;
    setTimeout((() => {
      this.inputEl.value = initialText;
    }));
    const parent = this.containerEl.querySelector(".prompt");
    if (parent) parent.addClass("override-input");
  }
  getSuggestions(query) {
    return [ query ];
  }
  renderSuggestion(value, el) {
    el.createDiv({
      text: `${this.title}${value}`
    });
  }
  onChooseSuggestion(item, evt) {
    var _a;
    null == (_a = this.callback) || _a.call(this, item);
    this.callback = void 0;
  }
  onClose() {
    setTimeout((() => {
      if (this.callback) this.callback(false);
    }), 100);
  }
};

function toggleObjectProp(obj, propName, value) {
  if (false === value) {
    const newTagInfoEntries = Object.entries(obj || {}).filter((([key]) => key != propName));
    if (0 == newTagInfoEntries.length) return {}; else return Object.fromEntries(newTagInfoEntries);
  } else return {
    ...null != obj ? obj : {},
    [propName]: value
  };
}

var TagFolderViewBase = class extends import_obsidian5.ItemView {
  constructor() {
    super(...arguments);
    this.navigation = false;
  }
  async saveSettings(settings) {
    this.plugin.settings = {
      ...this.plugin.settings,
      ...settings
    };
    await this.plugin.saveSettings();
    this.plugin.updateFileCaches();
  }
  showOrder(evt) {
    const menu = new import_obsidian5.Menu;
    menu.addItem((item => {
      item.setTitle("Tags").setIcon("hashtag").onClick((evt2 => {
        const menu2 = new import_obsidian5.Menu;
        for (const key in OrderKeyTag) for (const direction in OrderDirection) menu2.addItem((item2 => {
          const newSetting = `${key}_${direction}`;
          item2.setTitle(OrderKeyTag[key] + " " + OrderDirection[direction]).onClick((async () => {
            this.plugin.settings.sortTypeTag = newSetting;
            await this.plugin.saveSettings();
          }));
          if (newSetting == this.plugin.settings.sortTypeTag) item2.setIcon("checkmark");
          return item2;
        }));
        menu2.showAtPosition({
          x: evt.x,
          y: evt.y
        });
      }));
      return item;
    }));
    menu.addItem((item => {
      item.setTitle("Items").setIcon("document").onClick((evt2 => {
        const menu2 = new import_obsidian5.Menu;
        for (const key in OrderKeyItem) for (const direction in OrderDirection) menu2.addItem((item2 => {
          const newSetting = `${key}_${direction}`;
          item2.setTitle(OrderKeyItem[key] + " " + OrderDirection[direction]).onClick((async () => {
            this.plugin.settings.sortType = newSetting;
            await this.plugin.saveSettings();
          }));
          if (newSetting == this.plugin.settings.sortType) item2.setIcon("checkmark");
          return item2;
        }));
        menu2.showAtPosition({
          x: evt.x,
          y: evt.y
        });
      }));
      return item;
    }));
    menu.showAtMouseEvent(evt);
  }
  showLevelSelect(evt) {
    const menu = new import_obsidian5.Menu, setLevel = async level => {
      this.plugin.settings.expandLimit = level;
      await this.plugin.saveSettings();
      maxDepth.set(level);
    };
    for (const level of [ 2, 3, 4, 5 ]) menu.addItem((item => {
      item.setTitle("Level " + (level - 1)).onClick((() => {
        setLevel(level);
      }));
      if (this.plugin.settings.expandLimit == level) item.setIcon("checkmark");
      return item;
    }));
    menu.addItem((item => {
      item.setTitle("No limit").onClick((() => {
        setLevel(0);
      }));
      if (0 == this.plugin.settings.expandLimit) item.setIcon("checkmark");
      return item;
    }));
    menu.showAtMouseEvent(evt);
  }
  showMenu(evt, trail, targetTag, targetItems) {
    const isTagTree = this.getViewType() == VIEW_TYPE_TAGFOLDER, menu = new import_obsidian5.Menu;
    if (isTagTree) {
      const expandedTagsAll = ancestorToLongestTag(ancestorToTags(joinPartialPath(removeIntermediatePath(trail)))).map((e => trimTrailingSlash(e))), expandedTags = expandedTagsAll.map((e => e.split("/").filter((ee => !isSpecialTag(ee))).join("/"))).filter((e => "" != e)).map((e => "#" + e)).join(" ").trim(), displayExpandedTags = expandedTagsAll.map((e => e.split("/").filter((ee => renderSpecialTag(ee))).join("/"))).filter((e => "" != e)).map((e => "#" + e)).join(" ").trim();
      if (navigator && navigator.clipboard) menu.addItem((item => item.setTitle(`Copy tags:${expandedTags}`).setIcon("hashtag").onClick((async () => {
        await navigator.clipboard.writeText(expandedTags);
        new import_obsidian5.Notice("Copied");
      }))));
      menu.addItem((item => item.setTitle("New note " + (targetTag ? "in here" : "as like this")).setIcon("create-new").onClick((async () => {
        await this.plugin.createNewNote(trail);
      }))));
      if (targetTag) if (this.plugin.settings.useTagInfo && null != this.plugin.tagInfo) {
        const tag = targetTag;
        if (tag in this.plugin.tagInfo && "key" in this.plugin.tagInfo[tag]) menu.addItem((item => item.setTitle("Unpin").setIcon("pin").onClick((async () => {
          this.plugin.tagInfo[tag] = toggleObjectProp(this.plugin.tagInfo[tag], "key", false);
          this.plugin.applyTagInfo();
          await this.plugin.saveTagInfo();
        })))); else menu.addItem((item => {
          item.setTitle("Pin").setIcon("pin").onClick((async () => {
            this.plugin.tagInfo[tag] = toggleObjectProp(this.plugin.tagInfo[tag], "key", "");
            this.plugin.applyTagInfo();
            await this.plugin.saveTagInfo();
          }));
        }));
        menu.addItem((item => {
          item.setTitle("Set an alternative label").setIcon("pencil").onClick((async () => {
            var _a;
            const oldAlt = tag in this.plugin.tagInfo ? null != (_a = this.plugin.tagInfo[tag].alt) ? _a : "" : "", label = await askString(this.app, "", "", oldAlt);
            if (false !== label) {
              this.plugin.tagInfo[tag] = toggleObjectProp(this.plugin.tagInfo[tag], "alt", "" == label ? false : label);
              this.plugin.applyTagInfo();
              await this.plugin.saveTagInfo();
            }
          }));
        }));
        menu.addItem((item => {
          item.setTitle("Change the mark").setIcon("pencil").onClick((async () => {
            var _a;
            const oldMark = tag in this.plugin.tagInfo ? null != (_a = this.plugin.tagInfo[tag].mark) ? _a : "" : "", mark = await askString(this.app, "", "", oldMark);
            if (false !== mark) {
              this.plugin.tagInfo[tag] = toggleObjectProp(this.plugin.tagInfo[tag], "mark", "" == mark ? false : mark);
              this.plugin.applyTagInfo();
              await this.plugin.saveTagInfo();
            }
          }));
        }));
        menu.addItem((item => {
          item.setTitle("Redirect this tag to ...").setIcon("pencil").onClick((async () => {
            var _a;
            const oldRedirect = tag in this.plugin.tagInfo ? null != (_a = this.plugin.tagInfo[tag].redirect) ? _a : "" : "", redirect = await askString(this.app, "", "", oldRedirect);
            if (false !== redirect) {
              this.plugin.tagInfo[tag] = toggleObjectProp(this.plugin.tagInfo[tag], "redirect", "" == redirect ? false : redirect);
              this.plugin.applyTagInfo();
              await this.plugin.saveTagInfo();
            }
          }));
        }));
        if (targetItems) {
          menu.addItem((item => {
            item.setTitle("Open scroll view").setIcon("sheets-in-box").onClick((async () => {
              const files = targetItems.map((e => e.path));
              await this.plugin.openScrollView(void 0, displayExpandedTags, expandedTagsAll.join(", "), files);
            }));
          }));
          menu.addItem((item => {
            item.setTitle("Open list").setIcon("sheets-in-box").onClick((() => {
              selectedTags.set(expandedTagsAll);
            }));
          }));
        }
      }
    }
    if (!targetTag && targetItems && 1 == targetItems.length) {
      const path = targetItems[0].path, file = this.app.vault.getAbstractFileByPath(path);
      this.app.workspace.trigger("file-menu", menu, file, "file-explorer");
      menu.addSeparator();
      menu.addItem((item => item.setTitle("Open in new tab").setSection("open").setIcon("lucide-file-plus").onClick((async () => {
        await this.app.workspace.openLinkText(path, path, "tab");
      }))));
      menu.addItem((item => item.setTitle("Open to the right").setSection("open").setIcon("lucide-separator-vertical").onClick((async () => {
        await this.app.workspace.openLinkText(path, path, "split");
      }))));
    } else if (!isTagTree && targetTag) {
      const path = targetTag, file = this.app.vault.getAbstractFileByPath(path);
      this.app.workspace.trigger("file-menu", menu, file, "file-explorer");
      menu.addSeparator();
      menu.addItem((item => item.setTitle("Open in new tab").setSection("open").setIcon("lucide-file-plus").onClick((async () => {
        await this.app.workspace.openLinkText(path, path, "tab");
      }))));
      menu.addItem((item => item.setTitle("Open to the right").setSection("open").setIcon("lucide-separator-vertical").onClick((async () => {
        await this.app.workspace.openLinkText(path, path, "split");
      }))));
    }
    if ("screenX" in evt) menu.showAtPosition({
      x: evt.pageX,
      y: evt.pageY
    }); else menu.showAtPosition({
      x: evt.nativeEvent.locationX,
      y: evt.nativeEvent.locationY
    });
    evt.preventDefault();
  }
  switchView() {
    let viewType = VIEW_TYPE_TAGFOLDER;
    const currentType = this.getViewType();
    if (currentType == VIEW_TYPE_TAGFOLDER) viewType = VIEW_TYPE_TAGFOLDER_LIST; else if (currentType == VIEW_TYPE_TAGFOLDER_LINK) return; else if (currentType == VIEW_TYPE_TAGFOLDER_LIST) viewType = VIEW_TYPE_TAGFOLDER;
    const leaves = this.app.workspace.getLeavesOfType(viewType).filter((e => !e.getViewState().pinned && e != this.leaf));
    if (leaves.length) this.app.workspace.revealLeaf(leaves[0]);
  }
}, TagFolderView = class extends TagFolderViewBase {
  constructor(leaf, plugin, viewType) {
    super(leaf);
    this.icon = "stacked-levels";
    this.plugin = plugin;
    this.showMenu = this.showMenu.bind(this);
    this.showOrder = this.showOrder.bind(this);
    this.newNote = this.newNote.bind(this);
    this.showLevelSelect = this.showLevelSelect.bind(this);
    this.switchView = this.switchView.bind(this);
    this.treeViewType = viewType;
  }
  getIcon() {
    return "stacked-levels";
  }
  newNote(evt) {
    this.app.commands.executeCommandById("file-explorer:new-file");
  }
  getViewType() {
    return "tags" == this.treeViewType ? VIEW_TYPE_TAGFOLDER : VIEW_TYPE_TAGFOLDER_LINK;
  }
  getDisplayText() {
    return "tags" == this.treeViewType ? "Tag Folder" : "Link Folder";
  }
  async onOpen() {
    this.containerEl.empty();
    const app = mount(TagFolderViewComponent, {
      target: this.containerEl,
      props: {
        openFile: this.plugin.focusFile,
        hoverPreview: (a, b) => this.plugin.hoverPreview(a, b),
        vaultName: this.app.vault.getName(),
        showMenu: this.showMenu,
        showLevelSelect: this.showLevelSelect,
        showOrder: this.showOrder,
        newNote: this.newNote,
        openScrollView: this.plugin.openScrollView,
        isViewSwitchable: this.plugin.settings.useMultiPaneList,
        switchView: this.switchView,
        viewType: this.treeViewType,
        saveSettings: this.saveSettings.bind(this)
      }
    });
    this.component = app;
    return await Promise.resolve();
  }
  async onClose() {
    unmount(this.component);
    return await Promise.resolve();
  }
}, import_obsidian7 = require("obsidian"), TagFolderList = class extends TagFolderViewBase {
  constructor(leaf, plugin) {
    super(leaf);
    this.icon = "stacked-levels";
    this.title = "";
    this.state = {
      tags: [],
      title: ""
    };
    this.stateStore = writable(this.state);
    this.plugin = plugin;
    this.showMenu = this.showMenu.bind(this);
    this.showOrder = this.showOrder.bind(this);
    this.newNote = this.newNote.bind(this);
    this.showLevelSelect = this.showLevelSelect.bind(this);
    this.switchView = this.switchView.bind(this);
  }
  onPaneMenu(menu, source2) {
    super.onPaneMenu(menu, source2);
    menu.addItem((item => {
      item.setIcon("pin").setTitle("Pin").onClick((() => {
        this.leaf.togglePinned();
      }));
    }));
  }
  getIcon() {
    return "stacked-levels";
  }
  async setState(state2, result) {
    this.state = {
      ...this.state,
      ...state2
    };
    this.title = state2.tags.join(",");
    this.stateStore.set(this.state);
    return await Promise.resolve();
  }
  getState() {
    return this.state;
  }
  async newNote(evt) {
    await this.plugin.createNewNote(this.state.tags);
  }
  getViewType() {
    return VIEW_TYPE_TAGFOLDER_LIST;
  }
  getDisplayText() {
    return `Files with ${this.state.title}`;
  }
  async onOpen() {
    this.containerEl.empty();
    this.component = mount(TagFolderViewComponent, {
      target: this.containerEl,
      props: {
        openFile: this.plugin.focusFile,
        hoverPreview: this.plugin.hoverPreview,
        title: "",
        showMenu: this.showMenu,
        showLevelSelect: this.showLevelSelect,
        showOrder: this.showOrder,
        newNote: this.newNote,
        openScrollView: this.plugin.openScrollView,
        isViewSwitchable: this.plugin.settings.useMultiPaneList,
        switchView: this.switchView,
        saveSettings: this.saveSettings.bind(this),
        stateStore: this.stateStore
      }
    });
    return await Promise.resolve();
  }
  async onClose() {
    if (this.component) {
      unmount(this.component);
      this.component = void 0;
    }
    return await Promise.resolve();
  }
}, HideItemsType = {
  NONE: "Hide nothing",
  DEDICATED_INTERMIDIATES: "Only intermediates of nested tags",
  ALL_EXCEPT_BOTTOM: "All intermediates"
};

function dotted(object, notation) {
  return notation.split(".").reduce(((a, b) => a && b in a ? a[b] : null), object);
}

function getCompareMethodItems(settings) {
  const invert = settings.sortType.contains("_DESC") ? -1 : 1;
  switch (settings.sortType) {
   case "DISPNAME_ASC":
   case "DISPNAME_DESC":
    return (a, b) => compare(a.displayName, b.displayName) * invert;

   case "FULLPATH_ASC":
   case "FULLPATH_DESC":
    return (a, b) => compare(a.path, b.path) * invert;

   case "MTIME_ASC":
   case "MTIME_DESC":
    return (a, b) => (a.mtime - b.mtime) * invert;

   case "CTIME_ASC":
   case "CTIME_DESC":
    return (a, b) => (a.ctime - b.ctime) * invert;

   case "NAME_ASC":
   case "NAME_DESC":
    return (a, b) => compare(a.filename, b.filename) * invert;

   default:
    console.warn("Compare method (items) corrupted");
    return (a, b) => compare(a.displayName, b.displayName) * invert;
  }
}

function onElement(el, event2, selector, callback, options) {
  el.on(event2, selector, callback, options);
  return () => el.off(event2, selector, callback, options);
}

var TagFolderPlugin5 = class extends import_obsidian8.Plugin {
  constructor() {
    super(...arguments);
    this.settings = {
      ...DEFAULT_SETTINGS
    };
    this.expandedFolders = [ "root" ];
    this.currentOpeningFile = "";
    this.searchString = "";
    this.allViewItems = [];
    this.allViewItemsByLink = [];
    this.compareItems = (_, __) => 0;
    this.focusFile = (path, specialKey) => {
      if (this.currentOpeningFile == path) return;
      const _targetFile = this.app.vault.getAbstractFileByPath(path), targetFile = _targetFile instanceof import_obsidian8.TFile ? _targetFile : this.app.vault.getFiles().find((f => f.path === path));
      if (targetFile) if (specialKey) this.app.workspace.openLinkText(targetFile.path, targetFile.path, "tab"); else this.app.workspace.openLinkText(targetFile.path, targetFile.path);
    };
    this.fileCaches = [];
    this.oldFileCache = "";
    this.parsedFileCache = new Map;
    this.lastSettings = "";
    this.lastSearchString = "";
    this.processingFileInfo = false;
    this.loadFileQueue = [];
    this.loadFileTimer = void 0;
    this.tagInfo = {};
    this.tagInfoFrontMatterBuffer = {};
    this.skipOnce = false;
    this.tagInfoBody = "";
  }
  getView() {
    for (const leaf of this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER)) {
      const view = leaf.view;
      if (view instanceof TagFolderView) return view;
    }
    return null;
  }
  getLinkView() {
    for (const leaf of this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER_LINK)) {
      const view = leaf.view;
      if (view instanceof TagFolderView) return view;
    }
    return null;
  }
  hoverPreview(e, path) {
    this.app.workspace.trigger("hover-link", {
      event: e,
      source: "file-explorer",
      hoverParent: this,
      targetEl: e.target,
      linktext: path
    });
  }
  setSearchString(search) {
    searchString.set(search);
  }
  getFileTitle(file) {
    if (!this.settings.useTitle) return file.basename;
    const metadata = this.app.metadataCache.getCache(file.path);
    if ((null == metadata ? void 0 : metadata.frontmatter) && this.settings.frontmatterKey) {
      const d = dotted(metadata.frontmatter, this.settings.frontmatterKey);
      if (d) return `${d}`;
    }
    if (null == metadata ? void 0 : metadata.headings) {
      const h1 = metadata.headings.find((e => 1 == e.level));
      if (h1) return h1.heading;
    }
    return file.basename;
  }
  getDisplayName(file) {
    const filename = this.getFileTitle(file) || file.basename;
    if ("NAME" == this.settings.displayMethod) return filename;
    const path = file.path.split("/");
    path.pop();
    const displayPath = path.join("/");
    if ("NAME : PATH" == this.settings.displayMethod) return `${filename} : ${displayPath}`;
    if ("PATH/NAME" == this.settings.displayMethod) return `${displayPath}/${filename}`; else return filename;
  }
  async onload() {
    await this.loadSettings();
    this.hoverPreview = this.hoverPreview.bind(this);
    this.modifyFile = this.modifyFile.bind(this);
    this.setSearchString = this.setSearchString.bind(this);
    this.openScrollView = this.openScrollView.bind(this);
    this.loadFileInfo = (0, import_obsidian8.debounce)(this.loadFileInfo.bind(this), this.settings.scanDelay, true);
    pluginInstance.set(this);
    this.registerView(VIEW_TYPE_TAGFOLDER, (leaf => new TagFolderView(leaf, this, "tags")));
    this.registerView(VIEW_TYPE_TAGFOLDER_LINK, (leaf => new TagFolderView(leaf, this, "links")));
    this.registerView(VIEW_TYPE_TAGFOLDER_LIST, (leaf => new TagFolderList(leaf, this)));
    this.registerView(VIEW_TYPE_SCROLL, (leaf => new ScrollView(leaf, this)));
    this.app.workspace.onLayoutReady((async () => {
      this.loadFileInfo();
      if (this.settings.alwaysOpen) {
        await this.initView();
        await this.activateView();
      }
      if (this.settings.useTagInfo) await this.loadTagInfo();
    }));
    this.addCommand({
      id: "tagfolder-open",
      name: "Show Tag Folder",
      callback: () => {
        this.activateView();
      }
    });
    this.addCommand({
      id: "tagfolder-link-open",
      name: "Show Link Folder",
      callback: () => {
        this.activateViewLink();
      }
    });
    this.addCommand({
      id: "tagfolder-rebuild-tree",
      name: "Force Rebuild",
      callback: () => {
        this.refreshAllTree();
      }
    });
    this.addCommand({
      id: "tagfolder-create-similar",
      name: "Create a new note with the same tags",
      editorCallback: async (editor, view) => {
        var _a;
        const file = null == view ? void 0 : view.file;
        if (!file) return;
        const cache = this.app.metadataCache.getFileCache(file);
        if (!cache) return;
        const tagsWithoutPrefix = (null != (_a = (0, import_obsidian8.getAllTags)(cache)) ? _a : []).map((e => trimPrefix(e, "#")));
        await this.createNewNote(tagsWithoutPrefix);
      }
    });
    this.metadataCacheChanged = this.metadataCacheChanged.bind(this);
    this.watchWorkspaceOpen = this.watchWorkspaceOpen.bind(this);
    this.metadataCacheResolve = this.metadataCacheResolve.bind(this);
    this.metadataCacheResolved = this.metadataCacheResolved.bind(this);
    this.loadFileInfo = this.loadFileInfo.bind(this);
    this.registerEvent(this.app.metadataCache.on("changed", this.metadataCacheChanged));
    this.registerEvent(this.app.metadataCache.on("resolve", this.metadataCacheResolve));
    this.registerEvent(this.app.metadataCache.on("resolved", this.metadataCacheResolved));
    this.refreshAllTree = this.refreshAllTree.bind(this);
    this.refreshTree = this.refreshTree.bind(this);
    this.registerEvent(this.app.vault.on("rename", this.refreshTree));
    this.registerEvent(this.app.vault.on("delete", this.refreshTree));
    this.registerEvent(this.app.vault.on("modify", this.modifyFile));
    this.registerEvent(this.app.workspace.on("file-open", this.watchWorkspaceOpen));
    this.watchWorkspaceOpen(this.app.workspace.getActiveFile());
    this.addSettingTab(new TagFolderSettingTab(this.app, this));
    maxDepth.set(this.settings.expandLimit);
    searchString.subscribe((search => {
      this.searchString = search;
      this.refreshAllTree();
    }));
    const setTagSearchString = (event2, tagString) => {
      if (tagString) {
        const regExpTagStr = new RegExp(`(^|\\s)${tagString.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(\\s|$)`, "u"), regExpTagStrInv = new RegExp(`(^|\\s)-${tagString.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(\\s|$)`, "u");
        if (event2.altKey) return; else if (event2.ctrlKey && event2.shiftKey) {
          if (this.searchString.match(regExpTagStr)) this.setSearchString(this.searchString.replace(regExpTagStr, "")); else if (!this.searchString.match(regExpTagStrInv)) this.setSearchString(this.searchString + (0 == this.searchString.length ? "" : " ") + `-${tagString}`);
        } else if (event2.ctrlKey) {
          if (this.searchString.match(regExpTagStrInv)) this.setSearchString(this.searchString.replace(regExpTagStrInv, "")); else if (!this.searchString.match(regExpTagStr)) this.setSearchString(this.searchString + (0 == this.searchString.length ? "" : " ") + `${tagString}`);
        } else this.setSearchString(tagString);
        event2.preventDefault();
        event2.stopPropagation();
      }
    };
    this.register(onElement(document, "click", 'a.tag[href^="#"]', ((event2, targetEl) => {
      var _a;
      if (!this.settings.overrideTagClicking) return;
      const tagString = targetEl.innerText.substring(1);
      if (tagString) {
        setTagSearchString(event2, tagString);
        const leaf = null == (_a = this.getView()) ? void 0 : _a.leaf;
        if (leaf) this.app.workspace.revealLeaf(leaf);
      }
    }), {
      capture: true
    }));
    this.register(onElement(document, "click", "span.cm-hashtag.cm-meta", ((event2, targetEl) => {
      var _a;
      if (!this.settings.overrideTagClicking) return;
      let enumTags = targetEl, tagString = "";
      for (;!enumTags.classList.contains("cm-hashtag-begin"); ) {
        enumTags = enumTags.previousElementSibling;
        if (!enumTags) {
          console.log("Error! start tag not found.");
          return;
        }
      }
      do {
        if (enumTags instanceof HTMLElement) {
          tagString += enumTags.innerText;
          if (enumTags.classList.contains("cm-hashtag-end")) break;
        }
        enumTags = enumTags.nextElementSibling;
      } while (enumTags);
      tagString = tagString.substring(1);
      setTagSearchString(event2, tagString);
      const leaf = null == (_a = this.getView()) ? void 0 : _a.leaf;
      if (leaf) this.app.workspace.revealLeaf(leaf);
    }), {
      capture: true
    }));
    selectedTags.subscribe((newTags => {
      this.openListView(newTags);
    }));
  }
  watchWorkspaceOpen(file) {
    if (file) this.currentOpeningFile = file.path; else this.currentOpeningFile = "";
    currentFile.set(this.currentOpeningFile);
  }
  metadataCacheChanged(file) {
    this.loadFileInfoAsync(file);
  }
  metadataCacheResolve(file) {
    if (null != this.getLinkView()) this.loadFileInfoAsync(file);
  }
  metadataCacheResolved() {
    if (null != this.getLinkView()) ;
  }
  refreshTree(file, oldName) {
    if (oldName) this.refreshAllTree(); else if (file instanceof import_obsidian8.TFile) this.loadFileInfo(file);
  }
  refreshAllTree() {
    this.loadFileInfo();
  }
  getFileCacheLinks(file) {
    const cachedLinks = this.app.metadataCache.resolvedLinks;
    return [ ...(null == this.getLinkView() ? [] : parseAllReference(cachedLinks, file.path, this.settings.linkConfig)).filter((e => e.endsWith(".md"))).map((e => `${e}`)) ];
  }
  getFileCacheData(file) {
    const metadata = this.app.metadataCache.getFileCache(file);
    if (!metadata) return false; else return {
      file,
      links: this.getFileCacheLinks(file),
      tags: (0, import_obsidian8.getAllTags)(metadata) || []
    };
  }
  updateFileCachesAll() {
    const caches = [ ...this.app.vault.getMarkdownFiles(), ...this.app.vault.getAllLoadedFiles().filter((e => "extension" in e && "canvas" == e.extension)) ].filter((file => {
      var _a;
      return null != (_a = this.parsedFileCache.get(file.path)) ? _a : 0 != file.stat.mtime;
    })).map((entry => this.getFileCacheData(entry))).filter((e => false !== e));
    this.fileCaches = [ ...caches ];
    return this.isFileCacheChanged();
  }
  isFileCacheChanged() {
    const fileCacheDump = JSON.stringify(this.fileCaches.map((e => ({
      path: e.file.path,
      links: e.links,
      tags: e.tags
    }))));
    if (this.oldFileCache == fileCacheDump) return false; else {
      this.oldFileCache = fileCacheDump;
      return true;
    }
  }
  updateFileCaches(diffs = []) {
    let anyUpdated = false;
    if (0 == this.fileCaches.length || 0 == diffs.length) return this.updateFileCachesAll(); else {
      const processDiffs = [ ...diffs ];
      let newCaches = [ ...this.fileCaches ], diff = processDiffs.shift();
      do {
        const procDiff = diff;
        if (!procDiff) break;
        const old = newCaches.find((fileCache => fileCache.file.path == procDiff.path));
        if (old) newCaches = newCaches.filter((fileCache => fileCache !== old));
        const newCache = this.getFileCacheData(procDiff);
        if (newCache) {
          if (null != this.getLinkView()) {
            const oldLinks = (null == old ? void 0 : old.links) || [], newLinks = newCache.links, diffs2 = unique([ ...oldLinks, ...newLinks ]).filter((link2 => !oldLinks.contains(link2) || !newLinks.contains(link2)));
            for (const filename of diffs2) {
              const file = this.app.vault.getAbstractFileByPath(filename);
              if (file instanceof import_obsidian8.TFile) processDiffs.push(file);
            }
          }
          newCaches.push(newCache);
        }
        anyUpdated = anyUpdated || JSON.stringify(fileCacheToCompare(old)) != JSON.stringify(fileCacheToCompare(newCache));
        diff = processDiffs.shift();
      } while (void 0 !== diff);
      this.fileCaches = newCaches;
    }
    return anyUpdated;
  }
  async getItemsList(mode) {
    const items = [], ignoreDocTags = this.settings.ignoreDocTags.toLowerCase().replace(/[\n ]/g, "").split(","), ignoreTags = this.settings.ignoreTags.toLowerCase().replace(/[\n ]/g, "").split(","), ignoreFolders = this.settings.ignoreFolders.toLowerCase().replace(/\n/g, "").split(",").map((e => e.trim())).filter((e => !!e)), targetFolders = this.settings.targetFolders.toLowerCase().replace(/\n/g, "").split(",").map((e => e.trim())).filter((e => !!e)), searchItems = this.searchString.toLowerCase().split("|").map((ee => ee.split(" ").map((e => e.trim())))), today = Date.now(), archiveTags = this.settings.archiveTags.toLowerCase().replace(/[\n ]/g, "").split(",");
    for (const fileCache of this.fileCaches) {
      if (targetFolders.length > 0 && !targetFolders.some((e => "" != e && fileCache.file.path.toLowerCase().startsWith(e)))) continue;
      if (ignoreFolders.some((e => "" != e && fileCache.file.path.toLowerCase().startsWith(e)))) continue;
      await doEvents();
      const tagRedirectList = {};
      if (this.settings.useTagInfo && this.tagInfo) for (const [key, taginfo] of Object.entries(this.tagInfo)) if (null == taginfo ? void 0 : taginfo.redirect) tagRedirectList[key] = taginfo.redirect;
      let allTags = [];
      if ("tag" == mode) {
        const allTagsDocs = unique(fileCache.tags);
        allTags = unique(allTagsDocs.map((e => e.substring(1))).map((e => e in tagRedirectList ? tagRedirectList[e] : e)));
      } else allTags = unique(fileCache.links);
      if (this.settings.disableNestedTags && "tag" == mode) allTags = allTags.map((e => e.split("/"))).flat();
      if (0 == allTags.length) if ("tag" == mode) allTags = [ "_untagged" ]; else if ("link" == mode) allTags = [ "_unlinked" ];
      if ("canvas" == fileCache.file.extension) allTags.push("_VIRTUAL_TAG_CANVAS");
      if (this.settings.useVirtualTag) {
        const disp = secondsToFreshness(today - fileCache.file.stat.mtime);
        allTags.push(`_VIRTUAL_TAG_FRESHNESS/${disp}`);
      }
      if (this.settings.displayFolderAsTag) {
        const path = [ "_VIRTUAL_TAG_FOLDER", ...fileCache.file.path.split("/") ];
        path.pop();
        if (path.length > 0) allTags.push(`${path.join("/")}`);
      }
      if (allTags.some((tag => ignoreDocTags.contains(tag.toLowerCase())))) continue;
      if (searchItems.map((searchItem => {
        let bx = false;
        if (0 == allTags.length) return false;
        for (const searchSrc of searchItem) {
          let search = searchSrc, func = "contains";
          if (search.startsWith("#")) {
            search = search.substring(1);
            func = "startsWith";
          }
          if (search.startsWith("-")) bx = bx || allTags.some((tag => tag.toLowerCase()[func](search.substring(1)))); else bx = bx || allTags.every((tag => !tag.toLowerCase()[func](search)));
        }
        return bx;
      })).every((e => e))) continue;
      allTags = allTags.filter((tag => !ignoreTags.contains(tag.toLowerCase())));
      const links = [ ...fileCache.links ];
      if (0 == links.length) links.push("_unlinked");
      if (this.settings.disableNarrowingDown && "tag" == mode) {
        const archiveTagsMatched = allTags.filter((e => archiveTags.contains(e.toLowerCase()))), targetTags = 0 == archiveTagsMatched.length ? allTags : archiveTagsMatched;
        for (const tags of targetTags) items.push({
          tags: [ tags ],
          extraTags: allTags.filter((e => e != tags)),
          path: fileCache.file.path,
          displayName: this.getDisplayName(fileCache.file),
          ancestors: [],
          mtime: fileCache.file.stat.mtime,
          ctime: fileCache.file.stat.ctime,
          filename: fileCache.file.basename,
          links
        });
      } else items.push({
        tags: allTags,
        extraTags: [],
        path: fileCache.file.path,
        displayName: this.getDisplayName(fileCache.file),
        ancestors: [],
        mtime: fileCache.file.stat.mtime,
        ctime: fileCache.file.stat.ctime,
        filename: fileCache.file.basename,
        links
      });
    }
    return items;
  }
  loadFileInfo(diff) {
    this.loadFileInfoAsync(diff).then((e => {}));
  }
  isSettingChanged() {
    const strSetting = JSON.stringify(this.settings), isSettingChanged = strSetting != this.lastSettings, isSearchStringModified = this.searchString != this.lastSearchString;
    if (isSettingChanged) this.lastSettings = strSetting;
    if (isSearchStringModified) this.lastSearchString = this.searchString;
    return isSearchStringModified || isSettingChanged;
  }
  async loadFileInfos(diffs) {
    if (!this.processingFileInfo) try {
      this.processingFileInfo = true;
      const cacheUpdated = this.updateFileCaches(diffs);
      if (this.isSettingChanged() || cacheUpdated) {
        appliedFiles.set(diffs.map((e => e.path)));
        await this.applyFileInfoToView();
      }
      await this.applyUpdateIntoScroll(diffs);
      const af = this.app.workspace.getActiveFile();
      if (af && this.currentOpeningFile != af.path) {
        this.currentOpeningFile = af.path;
        currentFile.set(this.currentOpeningFile);
      }
    } finally {
      this.processingFileInfo = false;
    } else diffs.forEach((e => {
      this.loadFileInfoAsync(e);
    }));
  }
  async applyFileInfoToView() {
    const itemsSorted = (await this.getItemsList("tag")).sort(this.compareItems);
    this.allViewItems = itemsSorted;
    allViewItems.set(this.allViewItems);
    if (null != this.getLinkView()) {
      const itemsLink = await this.getItemsList("link");
      updateItemsLinkMap(itemsLink);
      const itemsLinkSorted = itemsLink.sort(this.compareItems);
      this.allViewItemsByLink = itemsLinkSorted;
      allViewItemsByLink.set(this.allViewItemsByLink);
    }
  }
  async loadFileInfoAsync(diff) {
    if (diff) {
      if (diff && this.loadFileQueue.some((e => e.path == (null == diff ? void 0 : diff.path)))) ; else this.loadFileQueue.push(diff);
      if (this.loadFileTimer) clearTimeout(this.loadFileTimer);
      this.loadFileTimer = setTimeout((() => {
        if (0 === this.loadFileQueue.length) ; else {
          const diffs = [ ...this.loadFileQueue ];
          this.loadFileQueue = [];
          this.loadFileInfos(diffs);
        }
      }), 200);
    } else {
      this.loadFileQueue = [];
      if (this.loadFileTimer) {
        clearTimeout(this.loadFileTimer);
        this.loadFileTimer = void 0;
      }
      await this.loadFileInfos([]);
    }
  }
  onunload() {
    pluginInstance.set(void 0);
  }
  async openScrollView(leaf, title, tagPath, files) {
    if (!leaf) leaf = this.app.workspace.getLeaf("split");
    await leaf.setViewState({
      type: VIEW_TYPE_SCROLL,
      active: true,
      state: {
        files: files.map((e => ({
          path: e
        }))),
        title,
        tagPath
      }
    });
    this.app.workspace.revealLeaf(leaf);
  }
  async applyUpdateIntoScroll(files) {
    const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_SCROLL);
    for (const leaf of leaves) {
      const view = leaf.view;
      if (!view) continue;
      const viewState = leaf.getViewState(), scrollViewState = null == view ? void 0 : view.getScrollViewState();
      if (!viewState || !scrollViewState) continue;
      const viewStat = {
        ...viewState,
        state: {
          ...scrollViewState
        }
      };
      for (const file of files) if (file && view.isFileOpened(file.path)) {
        const newStat = {
          ...viewStat,
          state: {
            ...viewStat.state,
            files: viewStat.state.files.map((e => e.path == file.path ? {
              path: file.path
            } : e))
          }
        };
        await leaf.setViewState(newStat);
      }
      const tags = viewStat.state.tagPath.split(", ");
      let matchedFiles = this.allViewItems;
      for (const tag of tags) matchedFiles = matchedFiles.filter((item => item.tags.map((tag2 => tag2.toLowerCase())).some((itemTag => itemTag == tag.toLowerCase() || (itemTag + "/").startsWith(tag.toLowerCase() + (tag.endsWith("/") ? "" : "/"))))));
      const newFilesArray = matchedFiles.map((e => e.path));
      if (newFilesArray.sort().join("-") != viewStat.state.files.map((e => e.path)).sort().join("-")) {
        const newStat = {
          ...viewStat,
          state: {
            ...viewStat.state,
            files: newFilesArray.map((path => {
              const old = viewStat.state.files.find((e => e.path == path));
              if (old) return old; else return {
                path
              };
            }))
          }
        };
        await leaf.setViewState(newStat);
      }
    }
  }
  async _initTagView() {
    var _a;
    const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER);
    if (0 == leaves.length) await (null == (_a = this.app.workspace.getLeftLeaf(false)) ? void 0 : _a.setViewState({
      type: VIEW_TYPE_TAGFOLDER,
      state: {
        treeViewType: "tags"
      }
    })); else {
      const newState = leaves[0].getViewState();
      await leaves[0].setViewState({
        type: VIEW_TYPE_TAGFOLDER,
        state: {
          ...newState,
          treeViewType: "tags"
        }
      });
    }
  }
  async _initLinkView() {
    var _a;
    const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER_LINK);
    if (0 == leaves.length) await (null == (_a = this.app.workspace.getLeftLeaf(false)) ? void 0 : _a.setViewState({
      type: VIEW_TYPE_TAGFOLDER_LINK,
      state: {
        treeViewType: "links"
      }
    })); else {
      const newState = leaves[0].getViewState();
      await leaves[0].setViewState({
        type: VIEW_TYPE_TAGFOLDER_LINK,
        state: {
          ...newState,
          treeViewType: "links"
        }
      });
    }
  }
  async initView() {
    this.loadFileInfo();
    await this._initTagView();
  }
  async initLinkView() {
    this.loadFileInfo();
    await this._initLinkView();
  }
  async activateView() {
    const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER);
    await this.initView();
    if (leaves.length > 0) await this.app.workspace.revealLeaf(leaves[0]);
  }
  async activateViewLink() {
    const leaves = this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER_LINK);
    await this.initLinkView();
    if (leaves.length > 0) await this.app.workspace.revealLeaf(leaves[0]);
  }
  async modifyFile(file) {
    if (this.settings.useTagInfo) if (!this.skipOnce) {
      if (file.name == this.getTagInfoFilename()) await this.loadTagInfo();
    } else this.skipOnce = false;
  }
  getTagInfoFilename() {
    return (0, import_obsidian8.normalizePath)(this.settings.tagInfo);
  }
  getTagInfoFile() {
    const file = this.app.vault.getAbstractFileByPath(this.getTagInfoFilename());
    if (file instanceof import_obsidian8.TFile) return file; else return null;
  }
  applyTagInfo() {
    if (null != this.tagInfo) if (this.settings.useTagInfo) tagInfo.set(this.tagInfo);
  }
  async loadTagInfo() {
    if (!this.settings.useTagInfo) return;
    if (null == this.tagInfo) this.tagInfo = {};
    const file = this.getTagInfoFile();
    if (null == file) return;
    const data = await this.app.vault.read(file);
    try {
      const bodyStartIndex = data.indexOf("\n---");
      if (!data.startsWith("---") || -1 === bodyStartIndex) return;
      const yaml = data.substring(3, bodyStartIndex), yamlData = (0, import_obsidian8.parseYaml)(yaml), keys = Object.keys(yamlData);
      this.tagInfoBody = data.substring(bodyStartIndex + 5);
      this.tagInfoFrontMatterBuffer = yamlData;
      const newTagInfo = {};
      for (const key of keys) {
        const w = yamlData[key];
        if (!w) continue;
        if ("object" != typeof w) continue;
        const keys2 = [ "key", "mark", "alt", "redirect" ], entries = Object.entries(w).filter((([key2]) => keys2.some((e => key2.contains(e)))));
        if (0 != entries.length) newTagInfo[key] = Object.fromEntries(entries);
      }
      this.tagInfo = newTagInfo;
      this.applyTagInfo();
    } catch (ex) {
      console.log(ex);
    }
  }
  async saveTagInfo() {
    if (!this.settings.useTagInfo) return;
    if (null == this.tagInfo) return;
    let file = this.getTagInfoFile();
    if (null == file) file = await this.app.vault.create(this.getTagInfoFilename(), "");
    await this.app.fileManager.processFrontMatter(file, (matter => {
      const ti = Object.entries(this.tagInfo);
      for (const [key, value] of ti) if (void 0 === value) delete matter[key]; else matter[key] = value;
    }));
  }
  async refreshAllViewItems() {
    this.parsedFileCache.clear();
    const itemsSorted = (await this.getItemsList("tag")).sort(this.compareItems);
    this.allViewItems = itemsSorted;
    allViewItems.set(this.allViewItems);
    const itemsLinkSorted = (await this.getItemsList("link")).sort(this.compareItems);
    this.allViewItemsByLink = itemsLinkSorted;
    allViewItemsByLink.set(this.allViewItemsByLink);
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
    await this.loadTagInfo();
    tagFolderSetting.set(this.settings);
    this.compareItems = getCompareMethodItems(this.settings);
  }
  async saveSettings() {
    await this.saveData(this.settings);
    await this.saveTagInfo();
    tagFolderSetting.set(this.settings);
    this.compareItems = getCompareMethodItems(this.settings);
    this.refreshAllViewItems();
  }
  async openListView(tagSrc) {
    var _a, _b;
    if (!tagSrc) return;
    const tags = "root" == tagSrc.first() ? tagSrc.slice(1) : tagSrc;
    let theLeaf;
    for (const leaf of this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER_LIST)) {
      const state2 = leaf.getViewState();
      if (null == (_a = state2.state) ? void 0 : _a.tags) {
        if (state2.state.tags.slice().sort().join("-") == tags.slice().sort().join("-")) {
          this.app.workspace.setActiveLeaf(leaf, {
            focus: true
          });
          return;
        }
        if (state2.pinned) ; else theLeaf = leaf;
      }
    }
    if (!theLeaf) {
      const parent = null == (_b = this.app.workspace.getLeavesOfType(VIEW_TYPE_TAGFOLDER)) ? void 0 : _b.first();
      if (!parent) return;
      switch (this.settings.showListIn) {
       case "CURRENT_PANE":
        theLeaf = this.app.workspace.getLeaf();
        break;

       case "SPLIT_PANE":
        theLeaf = this.app.workspace.getLeaf("split", "horizontal");
        break;

       case "":
       default:
        if (!import_obsidian8.Platform.isMobile) theLeaf = this.app.workspace.createLeafBySplit(parent, "horizontal", false); else theLeaf = this.app.workspace.getLeftLeaf(false);
        break;
      }
    }
    const title = tags.map((e => e.split("/").map((ee => renderSpecialTag(ee))).join("/"))).join(" ");
    await theLeaf.setViewState({
      type: VIEW_TYPE_TAGFOLDER_LIST,
      active: true,
      state: {
        tags,
        title
      }
    });
    await this.app.workspace.revealLeaf(theLeaf);
  }
  async createNewNote(tags) {
    const expandedTagsAll = ancestorToLongestTag(ancestorToTags(joinPartialPath(removeIntermediatePath(null != tags ? tags : [])))).map((e => trimTrailingSlash(e))), expandedTags = expandedTagsAll.map((e => e.split("/").filter((ee => !isSpecialTag(ee))).join("/"))).filter((e => "" != e)).map((e => "#" + e)).join(" ").trim(), ww = await this.app.fileManager.createAndOpenMarkdownFile();
    if (this.settings.useFrontmatterTagsForNewNotes) await this.app.fileManager.processFrontMatter(ww, (matter => {
      var _a;
      matter.tags = null != (_a = matter.tags) ? _a : [];
      matter.tags = expandedTagsAll.filter((e => !isSpecialTag(e))).filter((e => matter.tags.indexOf(e) < 0)).concat(matter.tags);
    })); else await this.app.vault.append(ww, expandedTags);
  }
}, TagFolderSettingTab = class extends import_obsidian8.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  hide() {
    this.plugin.loadFileInfo();
  }
  display() {
    const {containerEl} = this;
    containerEl.empty();
    containerEl.createEl("h2", {
      text: "Behavior"
    });
    new import_obsidian8.Setting(containerEl).setName("Always Open").setDesc("Place TagFolder on the left pane and activate it at every Obsidian launch").addToggle((toggle => toggle.setValue(this.plugin.settings.alwaysOpen).onChange((async value => {
      this.plugin.settings.alwaysOpen = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Use pinning").setDesc("When this feature is enabled, the pin information is saved in the file set in the next configuration.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.useTagInfo).onChange((async value => {
        this.plugin.settings.useTagInfo = value;
        if (this.plugin.settings.useTagInfo) await this.plugin.loadTagInfo();
        await this.plugin.saveSettings();
        pi.setDisabled(!value);
      }));
    }));
    const pi = new import_obsidian8.Setting(containerEl).setName("Pin information file").setDisabled(!this.plugin.settings.useTagInfo).addText((text2 => {
      text2.setValue(this.plugin.settings.tagInfo).onChange((async value => {
        this.plugin.settings.tagInfo = value;
        if (this.plugin.settings.useTagInfo) await this.plugin.loadTagInfo();
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Disable narrowing down").setDesc("When this feature is enabled, relevant tags will be shown with the title instead of making a sub-structure.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.disableNarrowingDown).onChange((async value => {
        this.plugin.settings.disableNarrowingDown = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Files"
    });
    new import_obsidian8.Setting(containerEl).setName("Display method").setDesc("How to show a title of files").addDropdown((dropdown => dropdown.addOptions({
      "PATH/NAME": "PATH/NAME",
      NAME: "NAME",
      "NAME : PATH": "NAME : PATH"
    }).setValue(this.plugin.settings.displayMethod).onChange((async value => {
      this.plugin.settings.displayMethod = value;
      this.plugin.loadFileInfo();
      await this.plugin.saveSettings();
    }))));
    const setOrderMethod = async (key, order) => {
      const oldSetting = this.plugin.settings.sortType.split("_");
      if (!key) key = oldSetting[0];
      if (!order) order = oldSetting[1];
      this.plugin.settings.sortType = `${key}_${order}`;
      await this.plugin.saveSettings();
    };
    new import_obsidian8.Setting(containerEl).setName("Order method").setDesc("how to order items").addDropdown((dd => {
      dd.addOptions(OrderKeyItem).setValue(this.plugin.settings.sortType.split("_")[0]).onChange((key => setOrderMethod(key, void 0)));
    })).addDropdown((dd => {
      dd.addOptions(OrderDirection).setValue(this.plugin.settings.sortType.split("_")[1]).onChange((order => setOrderMethod(void 0, order)));
    }));
    new import_obsidian8.Setting(containerEl).setName("Prioritize items which are not contained in sub-folder").setDesc("If this has been enabled, the items which have no more extra tags are first.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.sortExactFirst).onChange((async value => {
        this.plugin.settings.sortExactFirst = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Use title").setDesc("Use value in the frontmatter or first level one heading for `NAME`.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.useTitle).onChange((async value => {
        this.plugin.settings.useTitle = value;
        fpath.setDisabled(!value);
        await this.plugin.saveSettings();
      }));
    }));
    const fpath = new import_obsidian8.Setting(containerEl).setName("Frontmatter path").setDisabled(!this.plugin.settings.useTitle).addText((text2 => {
      text2.setValue(this.plugin.settings.frontmatterKey).onChange((async value => {
        this.plugin.settings.frontmatterKey = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Tags"
    });
    const setOrderMethodTag = async (key, order) => {
      const oldSetting = this.plugin.settings.sortTypeTag.split("_");
      if (!key) key = oldSetting[0];
      if (!order) order = oldSetting[1];
      this.plugin.settings.sortTypeTag = `${key}_${order}`;
      await this.plugin.saveSettings();
    };
    new import_obsidian8.Setting(containerEl).setName("Order method").setDesc("how to order tags").addDropdown((dd => {
      dd.addOptions(OrderKeyTag).setValue(this.plugin.settings.sortTypeTag.split("_")[0]).onChange((key => setOrderMethodTag(key, void 0)));
    })).addDropdown((dd => {
      dd.addOptions(OrderDirection).setValue(this.plugin.settings.sortTypeTag.split("_")[1]).onChange((order => setOrderMethodTag(void 0, order)));
    }));
    new import_obsidian8.Setting(containerEl).setName("Use virtual tags").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.useVirtualTag).onChange((async value => {
        this.plugin.settings.useVirtualTag = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Display folder as tag").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.displayFolderAsTag).onChange((async value => {
        this.plugin.settings.displayFolderAsTag = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Store tags in frontmatter for new notes").setDesc("Otherwise, tags are stored with #hashtags at the top of the note").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.useFrontmatterTagsForNewNotes).onChange((async value => {
        this.plugin.settings.useFrontmatterTagsForNewNotes = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Actions"
    });
    new import_obsidian8.Setting(containerEl).setName("Search tags inside TagFolder when clicking tags").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.overrideTagClicking).onChange((async value => {
        this.plugin.settings.overrideTagClicking = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("List files in a separated pane").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.useMultiPaneList).onChange((async value => {
        this.plugin.settings.useMultiPaneList = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Show list in").setDesc("This option applies to the newly opened list").addDropdown((dropdown => {
      dropdown.addOptions(enumShowListIn).setValue(this.plugin.settings.showListIn).onChange((async value => {
        this.plugin.settings.showListIn = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Arrangements"
    });
    new import_obsidian8.Setting(containerEl).setName("Hide Items").setDesc("Hide items on the landing or nested tags").addDropdown((dd => {
      dd.addOptions(HideItemsType).setValue(this.plugin.settings.hideItems).onChange((async key => {
        if ("NONE" == key || "DEDICATED_INTERMIDIATES" == key || "ALL_EXCEPT_BOTTOM" == key) this.plugin.settings.hideItems = key;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Merge redundant combinations").setDesc("When this feature is enabled, a/b and b/a are merged into a/b if there is no intermediates.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.mergeRedundantCombination).onChange((async value => {
        this.plugin.settings.mergeRedundantCombination = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Do not simplify empty folders").setDesc("Keep empty folders, even if they can be simplified.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.doNotSimplifyTags).onChange((async value => {
        this.plugin.settings.doNotSimplifyTags = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Do not treat nested tags as dedicated levels").setDesc("Treat nested tags as normal tags").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.disableNestedTags).onChange((async value => {
        this.plugin.settings.disableNestedTags = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Reduce duplicated parents in nested tags").setDesc("If enabled, #web/css, #web/javascript will merged into web -> css -> javascript").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.reduceNestedParent).onChange((async value => {
        this.plugin.settings.reduceNestedParent = value;
        await this.plugin.saveSettings();
      }));
    }));
    new import_obsidian8.Setting(containerEl).setName("Keep untagged items on the root").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.expandUntaggedToRoot).onChange((async value => {
        this.plugin.settings.expandUntaggedToRoot = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Link Folder"
    });
    new import_obsidian8.Setting(containerEl).setName("Use Incoming").setDesc("").addToggle((toggle => toggle.setValue(this.plugin.settings.linkConfig.incoming.enabled).onChange((async value => {
      this.plugin.settings.linkConfig.incoming.enabled = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Use Outgoing").setDesc("").addToggle((toggle => toggle.setValue(this.plugin.settings.linkConfig.outgoing.enabled).onChange((async value => {
      this.plugin.settings.linkConfig.outgoing.enabled = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Hide indirectly linked notes").setDesc("").addToggle((toggle => toggle.setValue(this.plugin.settings.linkShowOnlyFDR).onChange((async value => {
      this.plugin.settings.linkShowOnlyFDR = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Connect linked tree").setDesc("").addToggle((toggle => toggle.setValue(this.plugin.settings.linkCombineOtherTree).onChange((async value => {
      this.plugin.settings.linkCombineOtherTree = value;
      await this.plugin.saveSettings();
    }))));
    containerEl.createEl("h2", {
      text: "Filters"
    });
    new import_obsidian8.Setting(containerEl).setName("Target Folders").setDesc("If configured, the plugin will only target files in it.").addTextArea((text2 => text2.setValue(this.plugin.settings.targetFolders).setPlaceholder("study,documents/summary").onChange((async value => {
      this.plugin.settings.targetFolders = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Ignore Folders").setDesc("Ignore documents in specific folders.").addTextArea((text2 => text2.setValue(this.plugin.settings.ignoreFolders).setPlaceholder("template,list/standard_tags").onChange((async value => {
      this.plugin.settings.ignoreFolders = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Ignore note Tag").setDesc("If the note has the tag listed below, the note would be treated as there was not.").addTextArea((text2 => text2.setValue(this.plugin.settings.ignoreDocTags).setPlaceholder("test,test1,test2").onChange((async value => {
      this.plugin.settings.ignoreDocTags = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Ignore Tag").setDesc("Tags in the list would be treated as there were not.").addTextArea((text2 => text2.setValue(this.plugin.settings.ignoreTags).setPlaceholder("test,test1,test2").onChange((async value => {
      this.plugin.settings.ignoreTags = value;
      await this.plugin.saveSettings();
    }))));
    new import_obsidian8.Setting(containerEl).setName("Archive tags").setDesc("If configured, notes with these tags will be moved under the tag.").addTextArea((text2 => text2.setValue(this.plugin.settings.archiveTags).setPlaceholder("archived, discontinued").onChange((async value => {
      this.plugin.settings.archiveTags = value;
      await this.plugin.saveSettings();
    }))));
    containerEl.createEl("h2", {
      text: "Misc"
    });
    new import_obsidian8.Setting(containerEl).setName("Tag scanning delay").setDesc("Sets the delay for reflecting metadata changes to the tag tree. (Plugin reload is required.)").addText((text2 => {
      (text2 = text2.setValue(this.plugin.settings.scanDelay + "").onChange((async value => {
        const newDelay = Number.parseInt(value, 10);
        if (newDelay) {
          this.plugin.settings.scanDelay = newDelay;
          await this.plugin.saveSettings();
        }
      }))).inputEl.setAttribute("type", "number");
      text2.inputEl.setAttribute("min", "250");
      return text2;
    }));
    new import_obsidian8.Setting(containerEl).setName("Disable dragging tags").setDesc("The `Dragging tags` is using internal APIs. If something happens, please disable this once and try again.").addToggle((toggle => {
      toggle.setValue(this.plugin.settings.disableDragging).onChange((async value => {
        this.plugin.settings.disableDragging = value;
        await this.plugin.saveSettings();
      }));
    }));
    containerEl.createEl("h2", {
      text: "Utilities"
    });
    new import_obsidian8.Setting(containerEl).setName("Dumping tags for reporting bugs").setDesc("If you want to open an issue to the GitHub, this information can be useful. and, also if you want to keep secrets about names of tags, you can use `disguised`.").addButton((button => button.setButtonText("Copy tags").setDisabled(false).onClick((async () => {
      const items = (await this.plugin.getItemsList("tag")).map((e => e.tags.filter((e2 => "_untagged" != e2)))).filter((e => e.length));
      await navigator.clipboard.writeText(items.map((e => e.map((e2 => `#${e2}`)).join(", "))).join("\n"));
      new import_obsidian8.Notice("Copied to clipboard");
    })))).addButton((button => button.setButtonText("Copy disguised tags").setDisabled(false).onClick((async () => {
      const x = new Map;
      let i = 0;
      const items = (await this.plugin.getItemsList("tag")).map((e => e.tags.filter((e2 => "_untagged" != e2)).map((e2 => e2.split("/").map((e3 => e3.startsWith("_VIRTUAL") ? e3 : x.has(e3) ? x.get(e3) : (x.set(e3, "tag" + i++), 
      i))).join("/"))).filter((e2 => e2.length))));
      await navigator.clipboard.writeText(items.map((e => e.map((e2 => `#${e2}`)).join(", "))).join("\n"));
      new import_obsidian8.Notice("Copied to clipboard");
    }))));
  }
};
/* nosourcemap */