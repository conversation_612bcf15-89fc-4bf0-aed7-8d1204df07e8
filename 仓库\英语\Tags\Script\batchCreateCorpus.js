// 批量语料笔记创建器
async function batchCreateCorpus(token) {
  if (!token) {
    new Notice("❌ 请传入AI密钥参数");
    return;
  }

  const { currentFile } = this;
  const file = currentFile;
  const practiceFileName = file.basename;

  // 检查是否是口语练习笔记
  if (!file.path.includes('口语练习') || !practiceFileName.match(/^\d{6}/)) {
    new Notice("请在口语练习笔记中使用此脚本");
    return;
  }

  // 显示批量输入界面
  const batchData = await showBatchInputModal();
  if (!batchData || batchData.length === 0) {
    new Notice("已取消批量创建语料笔记");
    return;
  }

  try {
    new Notice(`🤖 开始批量处理 ${batchData.length} 个语料...`, 3000);
    
    let successCount = 0;
    let failCount = 0;

    // 批量处理每个语料
    for (let i = 0; i < batchData.length; i++) {
      const corpusItem = batchData[i];
      
      try {
        new Notice(`🤖 正在处理 ${i + 1}/${batchData.length}: ${corpusItem.title}`, 2000);
        
        // 调用AI分析语料
        const aiAnalysis = await analyzeCorpusWithAI(token, corpusItem.title, file);
        
        if (aiAnalysis) {
          corpusItem.translation = aiAnalysis.translation;
          corpusItem.meaning = aiAnalysis.meaning;
          corpusItem.examples = aiAnalysis.examples;
          corpusItem.exampleTranslations = aiAnalysis.exampleTranslations;
          corpusItem.errorPoint = corpusItem.errorPoint || aiAnalysis.errorPoint;
        }

        // 获取当前日期信息
        const today = new Date();
        const fullDateString = today.toISOString().split('T')[0];
        const timeString = today.toLocaleTimeString('zh-CN', { 
          hour12: false, 
          hour: '2-digit', 
          minute: '2-digit' 
        });

        // 创建语料笔记文件
        await createBatchCorpusFile(corpusItem, practiceFileName, fullDateString, timeString);
        
        successCount++;
        
        // 添加延迟避免API限制
        if (i < batchData.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        console.error(`处理语料失败: ${corpusItem.title}`, error);
        failCount++;
      }
    }

    new Notice(`✅ 批量创建完成！成功: ${successCount}, 失败: ${failCount}`);
    
  } catch (error) {
    new Notice(`❌ 批量创建失败: ${error.message}`);
    console.error("批量语料创建错误:", error);
  }
}

// 显示批量输入模态框
async function showBatchInputModal() {
  return new Promise((resolve) => {
    const modal = new obsidian.Modal(app);
    modal.titleEl.setText("📚 批量语料笔记创建器");

    const container = modal.contentEl.createDiv();
    container.style.padding = "20px";

    // 说明文字
    const description = container.createEl("div");
    description.innerHTML = `
      <p style="color: #666; margin-bottom: 15px;">
        💡 <strong>批量创建模式</strong>：一次输入多个语料，AI自动批量处理！
      </p>
      <p style="color: #888; font-size: 13px; margin-bottom: 15px;">
        每行输入一个语料，格式：<code>语料内容 | 易错点（可选）</code><br>
        示例：<br>
        <code>critical issue | 容易与important混淆</code><br>
        <code>pose a challenge</code><br>
        <code>data breach | 注意单复数</code>
      </p>
    `;

    // 批量输入框
    container.createEl("label", { text: "批量语料输入：" });
    const batchInput = container.createEl("textarea", {
      placeholder: `critical issue | 容易与important混淆
pose a major challenge
data breach | 注意单复数
regulatory requirement
compliance framework | 正式场合使用`
    });
    batchInput.style.cssText = "width: 100%; padding: 10px; margin: 5px 0 15px 0; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; height: 200px; resize: vertical; font-family: monospace;";

    // 预览区域
    const previewContainer = container.createDiv();
    previewContainer.style.marginTop = "15px";
    
    const previewLabel = previewContainer.createEl("label", { text: "预览（将创建以下语料）：" });
    const previewList = previewContainer.createEl("div");
    previewList.style.cssText = "max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px; background: #f9f9f9;";

    // 实时预览
    const updatePreview = () => {
      const lines = batchInput.value.trim().split('\n').filter(line => line.trim());
      previewList.innerHTML = '';
      
      if (lines.length === 0) {
        previewList.innerHTML = '<span style="color: #999;">暂无语料</span>';
        return;
      }

      lines.forEach((line, index) => {
        const [title, errorPoint] = line.split('|').map(s => s.trim());
        const item = previewList.createEl("div");
        item.style.cssText = "margin: 5px 0; padding: 5px; border-left: 3px solid #4CAF50;";
        item.innerHTML = `
          <strong>${index + 1}. ${title}</strong>
          ${errorPoint ? `<br><small style="color: #666;">易错点: ${errorPoint}</small>` : ''}
        `;
      });
      
      previewLabel.textContent = `预览（将创建 ${lines.length} 个语料）：`;
    };

    batchInput.addEventListener('input', updatePreview);
    updatePreview();

    const buttonContainer = container.createDiv();
    buttonContainer.style.display = "flex";
    buttonContainer.style.gap = "10px";
    buttonContainer.style.justifyContent = "center";
    buttonContainer.style.marginTop = "20px";

    // 确认按钮
    const confirmBtn = buttonContainer.createEl("button", { text: "🚀 批量创建" });
    confirmBtn.style.cssText = "background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";

    let isProcessing = false; // 添加处理状态标志

    confirmBtn.onclick = () => {
      if (isProcessing) return; // 如果正在处理，直接返回

      const lines = batchInput.value.trim().split('\n').filter(line => line.trim());
      if (lines.length === 0) {
        new Notice("请输入至少一个语料");
        return;
      }

      // 设置处理状态（仅视觉反馈）
      isProcessing = true;
      confirmBtn.disabled = true;
      confirmBtn.textContent = "🔄 准备中...";
      confirmBtn.style.background = "#999";
      confirmBtn.style.cursor = "not-allowed";

      // 短暂延迟后关闭模态框，给用户视觉反馈
      setTimeout(() => {
        const batchData = lines.map(line => {
          const [title, errorPoint] = line.split('|').map(s => s.trim());
          return {
            title: title,
            errorPoint: errorPoint || ""
          };
        });

        modal.close();
        resolve(batchData);
      }, 500);
    };

    // 取消按钮
    const cancelBtn = buttonContainer.createEl("button", { text: "❌ 取消" });
    cancelBtn.style.cssText = "background: #f44336; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;";
    cancelBtn.onclick = () => {
      if (isProcessing) {
        new Notice("正在准备中，请稍候...");
        return;
      }
      modal.close();
      resolve(null);
    };

    modal.open();
    setTimeout(() => batchInput.focus(), 100);
  });
}

// 创建批量语料文件
async function createBatchCorpusFile(corpusData, practiceFileName, fullDate, time) {
  // 生成安全的文件名
  const safeCorpusTitle = corpusData.title
    .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
    .replace(/[_\s]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 50);
  
  const corpusFileName = `${safeCorpusTitle}.md`;
  const corpusFolder = "英语/口语训练/语料卡";
  const corpusFilePath = `${corpusFolder}/${corpusFileName}`;
  
  // 检查文件是否已存在
  if (await app.vault.adapter.exists(corpusFilePath)) {
    console.log(`跳过已存在的文件: ${corpusFileName}`);
    return;
  }

  // 生成语料笔记内容
  const examples = corpusData.examples || [
    `This ${corpusData.title} is very important.`,
    `We need to consider this ${corpusData.title} carefully.`
  ];
  
  const exampleTranslations = corpusData.exampleTranslations || [
    `这个${corpusData.translation || '表达'}非常重要。`,
    `我们需要仔细考虑这个${corpusData.translation || '表达'}。`
  ];

  // 处理属性值，确保YAML格式正确
  const safeTranslation = (corpusData.translation || '待翻译').replace(/"/g, '\\"').replace(/\n/g, ' ');
  const safeMeaning = (corpusData.meaning || '待补充').replace(/"/g, '\\"').replace(/\n/g, ' ');
  const safeErrorPoint = (corpusData.errorPoint || '待补充').replace(/"/g, '\\"').replace(/\n/g, ' ');

  const corpusContent = `---
记录日期: "${fullDate}"
记录时间: "${time}"
翻译: "${safeTranslation}"
具体意义: "${safeMeaning}"
出错点: "${safeErrorPoint}"
语料类型: "批量创建"
难度等级: "intermediate"
提取方式: "AI批量创建"
来源练习: "[[${practiceFileName}]]"
---

# ${corpusData.title}

## 📚 例句

### 例句1
**英文**: ${examples[0]}
**中文**: ${exampleTranslations[0]}

### 例句2
**英文**: ${examples[1]}
**中文**: ${exampleTranslations[1]}

---
来源练习: [[${practiceFileName}]]
`;

  try {
    // 确保文件夹存在
    try {
      await app.vault.createFolder(corpusFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 创建语料笔记文件
    await app.vault.create(corpusFilePath, corpusContent);
    
  } catch (error) {
    throw new Error(`创建文件失败: ${error.message}`);
  }
}

// AI分析语料（复用现有函数）
async function analyzeCorpusWithAI(token, corpusTitle, file) {
  const prompt = `你是一个专业的英语学习助手，请分析这个英语语料短语，提供准确的翻译和详细的用法说明。

语料短语：${corpusTitle}

请以JSON格式返回：
{
  "translation": "准确的中文翻译",
  "meaning": "详细的用法说明，包括使用场景、语法要点、搭配等",
  "errorPoint": "学习这个语料时需要注意的要点或容易出错的地方",
  "examples": [
    "第一个实用例句",
    "第二个实用例句"
  ],
  "exampleTranslations": [
    "第一个例句的中文翻译",
    "第二个例句的中文翻译"
  ]
}

要求：
1. 翻译要准确、简洁
2. 用法说明要详细，包含使用场景
3. 错误点要针对中国学习者的常见问题
4. 返回标准JSON格式`;

  try {
    const response = await obsidian.requestUrl({
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "GLM-4-Flash",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    });

    const result = response.json;
    const aiContent = result.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new Error("AI返回结果为空");
    }

    // 尝试解析JSON
    try {
      const jsonMatch = aiContent.match(/```json\n([\s\S]*?)\n```/) || aiContent.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : aiContent;
      
      const parsedResult = JSON.parse(jsonStr);
      return parsedResult;
    } catch (parseError) {
      console.error("JSON解析失败:", parseError);
      
      // 如果JSON解析失败，返回默认值
      return {
        translation: "待翻译",
        meaning: aiContent.substring(0, 200) + "...",
        errorPoint: "请注意正确的使用场景和语法搭配",
        examples: [
          `This ${corpusTitle} is important.`,
          `We should consider this ${corpusTitle}.`
        ],
        exampleTranslations: [
          `这个${corpusTitle}很重要。`,
          `我们应该考虑这个${corpusTitle}。`
        ]
      };
    }

  } catch (error) {
    console.error("AI调用失败:", error);
    throw error;
  }
}

exports.default = {
  entry: batchCreateCorpus,
  name: "batchCreateCorpus",
  description: `📚 批量语料笔记创建器

🎯 核心特点：
- 🚀 一次输入多个语料，批量处理
- 🤖 AI自动分析每个语料
- 📝 支持自定义易错点
- ⚡ 自动处理API限制和延迟

🎮 使用方法：
batchCreateCorpus("your_api_key_here")

💡 输入格式：
每行一个语料，可选易错点：
- critical issue | 容易与important混淆
- pose a challenge
- data breach | 注意单复数

🔧 功能优势：
- 批量处理，提高效率
- 实时预览，确认无误
- 自动跳过已存在文件
- 详细进度提示

适用于需要批量创建多个语料笔记的场景！

==请先在 https://open.bigmodel.cn/ 注册并获取 API 密钥==
  `
};
