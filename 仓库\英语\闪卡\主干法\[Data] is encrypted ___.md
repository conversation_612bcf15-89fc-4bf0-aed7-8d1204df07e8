---
title: "[Data] is encrypted ___"
correct_expression: "[Data] is encrypted ___"
usage_scenario: 技术措施描述
common_errors: 混淆不同加密方式的适用场景
error_type: 主干法 - 技术措施描述
tags:
  - 数据加密
  - 技术措施
  - 安全保护
  - GDPR合规
difficulty: 中级
frequency: 高频
concept_type: 技术安全必备句型
数据_options:
  - Personal Data
  - Sensitive Information
  - User Records
  - Log Files
  - Financial Data
  - Health Records
  - Backup Data
  - Metadata
  - Anonymized Data
  - Customer Data
加密方式_options:
  - at rest
  - in transit
  - end-to-end
  - using AES-256
  - with strong encryption
  - by default
  - during storage
  - during transmission
  - before processing
  - after collection
加密状态:
  - 静态加密 (存储时)
  - 传输加密
  - 端到端加密
  - 默认加密
  - 强加密
  - 标准加密
  - 动态加密
技术标准:
  - AES-256
  - RSA
  - TLS
  - SSL
  - HTTPS
  - VPN
  - PKI
  - Hash functions
相关法律:
  - GDPR
  - CCPA
  - HIPAA
  - PIPL
  - LGPD
  - DPA 2018
  - NIS Directive
---

# [Data] is encrypted ___

## 中文翻译
[数据]被____加密。

## 使用场景
数据保护、安全措施、技术规范中的加密要求描述

## 示例
- Personal data is encrypted at rest
- Sensitive information is encrypted in transit
- User records are encrypted end-to-end

## 记忆要点
- at rest指存储时加密，in transit指传输时加密
- GDPR Article 32要求适当的技术措施
- 不同类型数据需要不同级别的加密保护